package com.chaty.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chaty.common.BaseResponse;
import com.chaty.dto.PromptChangeLogDTO;
import com.chaty.entity.PromptChangeLog;
import com.chaty.mapper.PromptChangeLogMapper;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

@RestController
@RequestMapping("/api/promptChangeLog")
public class PromptChangeLogController {

    @Resource
    private PromptChangeLogMapper promptChangeLogMapper;

    @PostMapping("/page")
    public BaseResponse<?> page(@RequestBody PromptChangeLogDTO param) {
        LambdaQueryWrapper<PromptChangeLog> queryWrapper = Wrappers.<PromptChangeLog>lambdaQuery()
                .eq(Objects.nonNull(param.getId()), PromptChangeLog::getId, param.getId())
                .like(StrUtil.isNotBlank(param.getOldValue()), PromptChangeLog::getOldValue, param.getOldValue())
                .like(StrUtil.isNotBlank(param.getNewValue()), PromptChangeLog::getNewValue, param.getNewValue())
                .eq(StrUtil.isNotBlank(param.getPromptKey()), PromptChangeLog::getPromptKey, param.getPromptKey())
                .orderByAsc(PromptChangeLog::getCreateTime);
        return BaseResponse.ok(promptChangeLogMapper.selectPage(param.getPage().page(), queryWrapper));
    }
}
