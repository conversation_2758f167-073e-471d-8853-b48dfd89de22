package com.chaty.api.lianke;

import cn.hutool.json.JSONObject;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import feign.Response;
import org.springframework.web.bind.annotation.RequestParam;


public interface OpenLiankeApi {
    @RequestLine("POST /api/auth/login")
    @Headers({
            "Content-Type: application/json",
            "cookie: {cookie}",
            "csrftoken: {csrftoken}"
    })
    Response login(String body, @Param("cookie") String cookie, @Param("csrftoken") String csrftoken);

    @RequestLine("GET /api/device/device/?page=1&limit=1000")
    @Headers({
            "Content-Type: application/json",
            "Cookie: {cookie}",
            "csrftoken: {csrftoken}"
    })
    LiankeBaseResponse<JSONObject> deviceList(@Param("cookie") String cookie, @Param("csrftoken") String csrftoken);

    @RequestLine("GET /api/auth/login")
    @Headers({
            "Content-Type: application/json",
    })
    Response getCSRFToken();
}
