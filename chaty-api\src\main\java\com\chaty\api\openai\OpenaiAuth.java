package com.chaty.api.openai;

import lombok.Data;

@Data
public class OpenaiAuth {

    public OpenaiAuth(String key, String url, String model) {
        this.key = key;
        this.url = url;
        this.model = model;
    }

    private String key;

    private String url;

    private String model;

    private long validTime;

    public boolean verify() {
        return System.currentTimeMillis() >= validTime;
    }

}
