package com.chaty.service;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.chaty.entity.GptAskLogEntity;
import com.chaty.enums.GptAskLogType;
import org.apache.poi.util.StringUtil;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.chaty.dto.ChatCompletionDTO;
import com.chaty.exception.BaseException;

import reactor.core.publisher.Flux;

import javax.annotation.Resource;

public interface BasicAiService {

    Map<String, Object> chatForCompletion(ChatCompletionDTO param, GptAskLogEntity gptAskLogEntity);

    default Flux<String> streamCompletetion(ChatCompletionDTO param) {
        throw new BaseException("Unsupported stream completion for this service");
    }

    Map<String, Object> getFinalCompletion(ChatCompletionDTO param);

    Boolean isSupport(String model);

    public static BasicAiService findSupport(Collection<BasicAiService> services, String model) {
        for (BasicAiService service : services) {
            if (service.isSupport(model)) {
                return service;
            }
        }
        return null;
    }

    public static GptAskLogEntity getGptAskLogEntityInAsk(Map<String, Object> apiParam, Map<String, Object> resp, GptAskLogEntity gptAskLogEntity) {
        gptAskLogEntity.setRequestDetail(JSONUtil.toJsonStr(apiParam));
        gptAskLogEntity.setResponseDetail(JSONUtil.toJsonStr(resp));
        Boolean isSuccess = true;
        try {
            if (Objects.isNull(resp)) {
                isSuccess = false;
            }else if (StrUtil.isNotBlank(gptAskLogEntity.getType())) {
                String type = gptAskLogEntity.getType();
                String response = (String) resp.get("$response");
                if (type.equals(GptAskLogType.scorePoint)) {
                    // 是否是数字
                    String score = JSONUtil.parseObj(response).getStr("score");
                    try {
                        Double.parseDouble(score);
                        isSuccess = true;
                    } catch (NumberFormatException e) {
                        isSuccess = false;
                    }
                } else if(type.equals(GptAskLogType.essayAnalyticalReport)) {
                    isSuccess = response.length() > 100;
                } else if(type.equals(GptAskLogType.studentName)) {
                    String studentName = JSONUtil.parseObj(response).getStr("studentName");
                    isSuccess = studentName.length() < 10;
                } else if (type.equals(GptAskLogType.studentNumer)) {
                    String studentNum = JSONUtil.parseObj(response).getStr("studentNumber");
                    isSuccess = studentNum.length() < 20;
                } else {
                    isSuccess = JSONUtil.isTypeJSON(response);
                }
            }
        }catch (Exception e) {
            isSuccess = false;
        }
        try {
            gptAskLogEntity.setIsSuccess(isSuccess ? 1 : 0);
        } catch (Exception e) {
            gptAskLogEntity.setIsSuccess(0);
        }
        long endTime = System.currentTimeMillis();
        gptAskLogEntity.setTimeConsumption((int) ((endTime - gptAskLogEntity.getStartTime())));
        return gptAskLogEntity;
    }

}
