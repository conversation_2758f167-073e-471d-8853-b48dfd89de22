package com.chaty.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.dto.GptAskLogDTO;
import com.chaty.dto.RecordTimeStatsDTO;
import com.chaty.entity.DocCorrectFile;
import com.chaty.entity.DocCorrectRecord;
import com.chaty.entity.GptAskLogEntity;
import com.chaty.mapper.DocCorrectFileMapper;
import com.chaty.mapper.DocCorrectRecordMapper;
import com.chaty.mapper.GptAskLogMapper;
import com.chaty.service.GptAskLogService;
import com.chaty.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GptAskLogServiceImpl extends ServiceImpl<GptAskLogMapper, GptAskLogEntity>
        implements GptAskLogService {

    @Resource
    private GptAskLogMapper gptAskLogMapper;
    @Value("${server.url}")
    public String serverUrl;
    @Resource
    private DocCorrectRecordMapper docCorrectRecordMapper;
    @Autowired
    private DocCorrectFileMapper docCorrectFileMapper;

    @Override
    public Long add(GptAskLogEntity param) {
        try {
            if (Objects.isNull(param)) {
                log.error("GptAskLogEntity is null");
                return null;
            }
            if (Objects.nonNull(param.getRequestDetail())) {
                JSONObject json = JSONUtil.parseObj(param.getRequestDetail());
                replaceBase64Images(json);
                // 将清理后的 JSON 重新写回实体
                param.setRequestDetail(JSONUtil.toJsonStr(json));
            }

            gptAskLogMapper.insert(param);
        } catch (Exception e) {
            log.error("Error while adding GptAskLogEntity: {}", e.getMessage());
        }
        return param.getId();
    }

    /**
     * 深度遍历 JSONObject/JSONArray，删除所有值以 data:image/jpeg 开头的字符串节点。
     */
    public void replaceBase64Images(Object node) {
        if (node instanceof JSONObject) {
            JSONObject obj = (JSONObject) node;
            for (String key : new ArrayList<>(obj.keySet())) {
                Object value = obj.get(key);
                if (value instanceof String && ((String) value).startsWith("data:image/jpeg")) {
                    // 替换为占位符
                    obj.put(key, "need_fill_image_base64");
                } else {
                    // 递归处理子节点
                    replaceBase64Images(value);
                }
            }

        } else if (node instanceof JSONArray) {
            JSONArray arr = (JSONArray) node;
            // 倒序遍历，便于安全替换
            for (int i = arr.size() - 1; i >= 0; i--) {
                Object elem = arr.get(i);
                if (elem instanceof String && ((String) elem).startsWith("data:image/jpeg")) {
                    // 替换为占位符
                    arr.set(i, "need_fill_image_base64");
                } else {
                    // 递归处理子节点
                    replaceBase64Images(elem);
                }
            }
        }
    }


    @Override
    public void delete(Long id) {
        gptAskLogMapper.deleteById(id);
    }

    @Override
    public IPage<GptAskLogEntity> page(GptAskLogDTO param) {
        LambdaQueryWrapper<GptAskLogEntity> queryWrapper = Wrappers.<GptAskLogEntity>lambdaQuery()
                .like(StrUtil.isNotBlank(param.getRecordName()), GptAskLogEntity::getRecordName, param.getRecordName())
                .eq(StrUtil.isNotBlank(param.getAimodel()), GptAskLogEntity::getAimodel, param.getAimodel())
                .eq(StrUtil.isNotBlank(param.getFileId()), GptAskLogEntity::getFileId, param.getFileId())
                .eq(StrUtil.isNotBlank(param.getTaskId()), GptAskLogEntity::getTaskId, param.getTaskId())
                .eq(StrUtil.isNotBlank(param.getConfigId()), GptAskLogEntity::getConfigId, param.getConfigId())
                .eq(StrUtil.isNotBlank(param.getRecordId()), GptAskLogEntity::getRecordId, param.getRecordId())
                .eq(Objects.nonNull(param.getId()), GptAskLogEntity::getId, param.getId())
                .eq(Objects.nonNull(param.getAreaIdx()), GptAskLogEntity::getAreaIdx, param.getAreaIdx())
                .eq(Objects.nonNull(param.getIsSuccess()), GptAskLogEntity::getIsSuccess, param.getIsSuccess())
                .eq(StrUtil.isNotBlank(param.getType()), GptAskLogEntity::getType, param.getType())
                // 核心分页条件：小于上一页最后一条的时间戳
                .lt(param.getLastCreateTime() != null, GptAskLogEntity::getCreateTime, param.getLastCreateTime())
                .last("limit " + param.getPage().getPageSize());
        // 判断是否有查询条件
        boolean hasQueryCondition = StrUtil.isNotBlank(param.getRecordName()) ||
                StrUtil.isNotBlank(param.getAimodel()) ||
                StrUtil.isNotBlank(param.getFileId()) ||
                StrUtil.isNotBlank(param.getTaskId()) ||
                StrUtil.isNotBlank(param.getConfigId()) ||
                StrUtil.isNotBlank(param.getRecordId()) ||
                Objects.nonNull(param.getId()) ||
                Objects.nonNull(param.getAreaIdx()) ||
                Objects.nonNull(param.getIsSuccess()) ||
                StrUtil.isNotBlank(param.getType()) ||
                param.getLastCreateTime() != null;

        // 根据是否有查询条件设置不同的排序规则
        queryWrapper.orderByDesc(GptAskLogEntity::getCreateTime);
//        if (hasQueryCondition) {
//            // 有查询条件时，先按区域号排序，再按时间降序排序
//            queryWrapper.orderByAsc(GptAskLogEntity::getAreaIdx)
//                    .orderByDesc(GptAskLogEntity::getCreateTime);
//        } else {
//            // 无查询条件时，直接按时间降序排序
//            queryWrapper.orderByDesc(GptAskLogEntity::getCreateTime);
//        }
        List<GptAskLogEntity> dbRecords = gptAskLogMapper.selectList(queryWrapper);

        // 包装为 IPage 返回
        IPage<GptAskLogEntity> res = new Page<>();
        res.setRecords(dbRecords);
        res.setSize(param.getPage().getPageSize());
        res.setCurrent(1);
        res.setTotal(-1);

        if (res.getRecords().isEmpty()) {
            return res;
        }
        if (Boolean.TRUE.equals(param.getNeedBase64())) {
            List<GptAskLogEntity> data = res.getRecords();
            List<DocCorrectRecord> records = new ArrayList<>();
            List<String> recordIds = data.stream()
                    .filter(e -> StrUtil.isBlank(e.getImgUrl()))
                    .map(GptAskLogEntity::getRecordId)
                    .filter(StrUtil::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
            if (!recordIds.isEmpty()) {
                records = docCorrectRecordMapper.selectList(
                        Wrappers.<DocCorrectRecord>lambdaQuery()
                                .in(DocCorrectRecord::getId, recordIds)
                );
            }

            // 获取所有recordId，然后去找areaImg
            for (GptAskLogEntity entity : data) {
                if (StrUtil.isNotBlank(entity.getRequestDetail()) && (Objects.nonNull(entity.getAreaIdx()) || StrUtil.isNotBlank(entity.getImgUrl()))) {
                    JSONObject json = null;
                    try {
                        json = JSONUtil.parseObj(entity.getRequestDetail());
                    } catch (Exception e) {
                        log.error("Error while parsing JSON: {} entity:{}", e.getMessage(), entity);
                    }
                    if (Objects.isNull(json)) {
                        continue;
                    }
                    Integer areaIdx = entity.getAreaIdx();
                    // 获取当前记录的 recordId
                    String recordId = entity.getRecordId();
                    // 查找对应的 DocCorrectRecord
                    DocCorrectRecord record = records.stream()
                            .filter(r -> r.getId().equals(recordId))
                            .findFirst()
                            .orElse(null);
                    if (StrUtil.isNotBlank(entity.getImgUrl()) || Objects.nonNull(record)) {
                        try {
                            String areaImgUrl = null;
                            if (StrUtil.isNotBlank(entity.getImgUrl())) {
                                areaImgUrl = entity.getImgUrl();
                            } else {
                                String reviewed = record.getReviewed();
                                JSONArray reviewedObj = JSONUtil.parseArray(reviewed);
                                areaImgUrl = reviewedObj.stream()
                                        .filter(obj -> obj instanceof JSONObject)
                                        .map(obj -> (JSONObject) obj)
                                        .filter(obj -> obj.getInt("areaIdx") == areaIdx)
                                        .map(obj -> obj.getStr("areaImg"))
                                        .filter(StrUtil::isNotBlank)
                                        .findFirst()
                                        .orElse(null);
                            }

                            if (StrUtil.isNotBlank(areaImgUrl)) {
                                if (!Base64.isBase64(areaImgUrl)) {
                                    if (!areaImgUrl.startsWith("http")) {
                                        areaImgUrl = String.format("%s%s", serverUrl, areaImgUrl);
                                    }
                                    areaImgUrl = "data:image/jpeg;base64," + FileUtil.INSTANCE.url2Base64(areaImgUrl);
                                }
                                fillBase64Images(json, areaImgUrl);
                                entity.setRequestDetail(JSONUtil.toJsonStr(json));
                            }
                        } catch (Exception e) {
                            log.error("Error while parsing reviewed JSON: {}", e.getMessage());
                        }
                    }
                }
                res.setRecords(data);
                return res;
            }
        } else {
            return res;
        }
        return res;
    }

    private void fillBase64Images(Object node, String base64) {
        if (node instanceof JSONObject) {
            JSONObject obj = (JSONObject) node;
            for (String key : new ArrayList<>(obj.keySet())) {
                Object value = obj.get(key);
                if (value instanceof String && "need_fill_image_base64".equals(value)) {
                    // 用传入的 base64 字符串替换占位符
                    obj.put(key, base64);
                } else {
                    // 继续递归
                    fillBase64Images(value, base64);
                }
            }

        } else if (node instanceof JSONArray) {
            JSONArray arr = (JSONArray) node;
            for (int i = 0; i < arr.size(); i++) {
                Object elem = arr.get(i);
                if (elem instanceof String && "need_fill_image_base64".equals(elem)) {
                    // 用传入的 base64 字符串替换占位符
                    arr.set(i, base64);
                } else {
                    // 继续递归
                    fillBase64Images(elem, base64);
                }
            }
        }
    }


    @Transactional
    @Override
    public Long update(GptAskLogEntity param) {
        GptAskLogEntity existed = getById(param.getId());
        if (Objects.nonNull(existed)) {
            param.setId(existed.getId());
            gptAskLogMapper.updateById(param);
        } else {
            param.setId(null);
            gptAskLogMapper.insert(param);
        }
        return param.getId();
    }

    @Override
    public GptAskLogEntity getById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return gptAskLogMapper.selectById(id);
    }

    @Override
    public RecordTimeStatsDTO getStatsByRecordName(String recordName) {
        QueryWrapper<GptAskLogEntity> wrapper = new QueryWrapper<>();
        if (recordName != null && !recordName.trim().isEmpty()) {
            wrapper.like("record_name", recordName.trim());
        }
        // AVG/SUM 自动忽略 NULL，COUNT(col) 只统计非空，COUNT(*)-COUNT(col) 得到空值数
        wrapper.select(
                "AVG(time_consumption)                AS averageTimeConsumption",
                "SUM(time_consumption)                AS totalTimeConsumption",
                "COUNT(time_consumption)              AS totalCount",
                "COUNT(*) - COUNT(time_consumption)   AS nullCount"
        );
        Map<String, Object> map = gptAskLogMapper
                .selectMaps(wrapper)
                .stream()
                .findFirst()
                .orElse(Collections.emptyMap());

        RecordTimeStatsDTO dto = new RecordTimeStatsDTO();
        // 毫秒(ms) → 秒(s)
        double avgMs = map.get("averageTimeConsumption") == null
                ? 0D
                : ((Number) map.get("averageTimeConsumption")).doubleValue();
        double sumMs = map.get("totalTimeConsumption") == null
                ? 0D
                : ((Number) map.get("totalTimeConsumption")).doubleValue();

        dto.setAverageTimeConsumption(avgMs / 1000D);
        dto.setTotalTimeConsumption((long) (sumMs / 1000D));
        dto.setTotalCount(
                map.get("totalCount") == null
                        ? 0L
                        : ((Number) map.get("totalCount")).longValue()
        );
        dto.setNullCount(
                map.get("nullCount") == null
                        ? 0L
                        : ((Number) map.get("nullCount")).longValue()
        );
        return dto;
    }

    @Override
    public RecordTimeStatsDTO getStatsByFileId(String fileId) {
        QueryWrapper<GptAskLogEntity> wrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(fileId)) {
            wrapper.eq("file_id", fileId.trim());
        }
        wrapper.select(
                "AVG(time_consumption)              AS averageTimeConsumption",
                "SUM(time_consumption)              AS totalTimeConsumption",
                "COUNT(time_consumption)            AS totalCount",
                "COUNT(*) - COUNT(time_consumption) AS nullCount"
        );
        Map<String, Object> map = gptAskLogMapper
                .selectMaps(wrapper)
                .stream()
                .findFirst()
                .orElse(Collections.emptyMap());

        RecordTimeStatsDTO dto = new RecordTimeStatsDTO();
        // 毫秒(ms) → 秒(s)
        double avgMs = map.get("averageTimeConsumption") == null
                ? 0D
                : ((Number) map.get("averageTimeConsumption")).doubleValue();
        double sumMs = map.get("totalTimeConsumption") == null
                ? 0D
                : ((Number) map.get("totalTimeConsumption")).doubleValue();

        dto.setAverageTimeConsumption(avgMs / 1000D);
        dto.setTotalTimeConsumption((long) (sumMs / 1000D));
        dto.setTotalCount(
                map.get("totalCount") == null
                        ? 0L
                        : ((Number) map.get("totalCount")).longValue()
        );
        dto.setNullCount(
                map.get("nullCount") == null
                        ? 0L
                        : ((Number) map.get("nullCount")).longValue()
        );

        DocCorrectFile update = new DocCorrectFile();
        update.setId(fileId);
        update.setAverageTimeConsumption(dto.getAverageTimeConsumption());
        update.setTotalTimeConsumption(dto.getTotalTimeConsumption());
        update.setTotalCount(dto.getTotalCount());
        update.setNullCount(dto.getNullCount());
        docCorrectFileMapper.updateById(update);
        return dto;
    }


    @Override
    public List<String> getDistinctRecordNames(String searchStr) {
        QueryWrapper<GptAskLogEntity> wrapper = new QueryWrapper<>();
        // 只选择去重后的 record_name 并按别名映射为 recordName
        wrapper.select("DISTINCT record_name AS recordName");
        if (StrUtil.isNotBlank(searchStr)) {
            wrapper.like("record_name", searchStr);
        }
        // 执行查询
        List<Map<String, Object>> resultMaps = gptAskLogMapper.selectMaps(wrapper);
        // 提取并返回字符串列表
        return resultMaps.stream()
                .map(m -> (String) m.get("recordName"))
                .collect(Collectors.toList());
    }

    @Override
    public Long getRequestCount() {
        QueryWrapper<GptAskLogEntity> wrapper = new QueryWrapper<>();
        wrapper.select("COUNT(*) AS totalCount");
        Map<String, Object> map = gptAskLogMapper
                .selectMaps(wrapper)
                .stream()
                .findFirst()
                .orElse(Collections.emptyMap());
        
        return map.get("totalCount") == null 
                ? 0L 
                : ((Number) map.get("totalCount")).longValue();
    }
}
