package com.chaty.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class ScoreTypesDTO {

    private List<String> scoreTypes = new ArrayList<>();

    private List<Double> scoreTypesMaxScore = new ArrayList<>();

    private Map<String, Double> scoreTypeObj;

    public void initByScoreTypeObj(Map<String, Double> scoreTypeObj) {
        if (scoreTypeObj == null) {
            return;
        }
        this.scoreTypeObj = scoreTypeObj;
        this.scoreTypes.clear();
        this.scoreTypesMaxScore.clear();
        for (String key : scoreTypeObj.keySet()) {
            this.scoreTypes.add(key);
            this.scoreTypesMaxScore.add(scoreTypeObj.getOrDefault(key, 0.0));
        }
    }

    /**
     * 将当前实例与另一个 ScoreTypesDTO 合并，合并规则为“相同 key 的值相加”：
     * 1. 如果当前 this.scoreTypeObj 为空，则直接复制 other.scoreTypeObj 并初始化列表。
     * 2. 否则，对于 other.scoreTypeObj 中的每个 (key, value)，
     * 如果当前 map 中存在该 key，则执行 oldValue + incomingValue；否则直接 put incomingValue。
     * 3. 最后，清空并重新生成 scoreTypes 与 scoreTypesMaxScore 列表，使其与合并后的 map 保持一致。
     *
     * @param other 另一个待合并的 ScoreTypesDTO，若为 null 或其内部 map 为 null，则不做任何操作
     */
    public void merge(ScoreTypesDTO other) {
        if (other == null || other.getScoreTypeObj() == null) {
            return;
        }

        // 如果当前实例尚未初始化 map，则直接复制一份 other 的 map，并初始化列表
        if (this.scoreTypeObj == null || this.scoreTypeObj.isEmpty()) {
            // 深拷贝一份以避免后续引用冲突
            this.scoreTypeObj = new HashMap<>(other.getScoreTypeObj());
            initByScoreTypeObj(this.scoreTypeObj);
            return;
        }

        // 否则逐条合并：相同 key 累加，不存在则插入
        for (Map.Entry<String, Double> entry : other.getScoreTypeObj().entrySet()) {
            String key = entry.getKey();
            Double otherValue = entry.getValue();

            this.scoreTypeObj.merge(
                    key,
                    otherValue,
                    (currentValue, incomingValue) -> {
                        // 若 currentValue 为 null，则直接返回 incomingValue
                        if (currentValue == null) {
                            return incomingValue;
                        }
                        // 若 incomingValue 为 null，则保留 currentValue
                        if (incomingValue == null) {
                            return currentValue;
                        }
                        // 相同 key 的值相加
                        return currentValue + incomingValue;
                    }
            );
        }

        // 合并完成后，重建两个列表
        this.scoreTypes.clear();
        this.scoreTypesMaxScore.clear();
        for (String key : this.scoreTypeObj.keySet()) {
            this.scoreTypes.add(key);
            this.scoreTypesMaxScore.add(this.scoreTypeObj.getOrDefault(key, 0.0));
        }
    }
}
