package com.chaty.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * GPT 问答日志表对应实体类
 */
@Data
@TableName("gpt_ask_log")
public class GptAskLogEntity {

    /** 主键，自增 */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 文件 ID */
    private String fileId;

    /** 任务 ID */
    private String taskId;

    /** 记录 ID */
    private String recordId;

    /** 区域索引 */
    private Integer areaIdx;

    /** 请求详情 */
    private String requestDetail;

    /** 响应详情 */
    private String responseDetail;

    /** 是否成功（0：否，1：是） */
    private Integer isSuccess;

    /** 是否删除（0：否，1：是） */
    @TableLogic
    private Integer isDeleted;

    /** 创建时间，插入时填充 */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新时间，插入和更新时填充 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /** 记录名称 */
    private String recordName;

    private String configId;

    private String aimodel;

    private String type;

    /**
     * 单位 ms
     */
    private Integer timeConsumption;

    /**
     * 问答开始时间
     */
    private long startTime;

    private String error;

    private String imgUrl;
}

