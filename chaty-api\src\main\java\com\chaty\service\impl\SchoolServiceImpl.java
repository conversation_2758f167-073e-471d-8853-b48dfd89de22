package com.chaty.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chaty.dto.SchoolDTO;
import com.chaty.entity.School;
import com.chaty.mapper.SchoolMapper;
import com.chaty.service.SchoolService;

import cn.hutool.core.bean.BeanUtil;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

@Service
public class SchoolServiceImpl implements SchoolService {
    @Resource
    private SchoolMapper schoolMapper;

    @Override
    public List<School> selectAll() {
        return schoolMapper.selectList(null);
    }

    @Override
    public Integer add(SchoolDTO param) {
        School add = BeanUtil.copyProperties(param, School.class);
        return schoolMapper.insert(add);
    }

    @Override
    public Integer updateById(SchoolDTO param) {
        School update = BeanUtil.copyProperties(param, School.class);
        return schoolMapper.updateById(update);
    }

    @Override
    public Integer deleteById(String id) {
        return schoolMapper.update(Wrappers
                .lambdaUpdate(School.class)
                .eq(School::getSchoolId, id)
                .set(School::getDeleted, true));
    }

    @Override
    public Integer deleteByBatch(List<String> ids) {
        return schoolMapper.deleteBatchIds(ids);
    }

    @Override
    public IPage<?> findPage(SchoolDTO params) {
        LambdaQueryWrapper<School> wrapper = Wrappers.lambdaQuery(School.class)
                .eq(School::getDeleted, false)
                .like(StringUtils.hasText(params.getSchoolName()), School::getSchoolName, params.getSchoolName())
                .orderByDesc(School::getCreateTime);
        return schoolMapper.selectPage(params.getPage().page(School.class), wrapper);
    }

}
