<#if reviewContext.docType == "A3">
\documentclass[12pt,landscape]{article}
\usepackage[a3paper]{geometry}
<#else>
\documentclass[a4paper]{article}
</#if>
\usepackage[absolute, overlay]{textpos}
\usepackage{CJKutf8}
\usepackage{fancyhdr}
\usepackage{pifont}
\usepackage{color}
\usepackage{graphicx}
\usepackage{wrapfig}
\usepackage{xcolor}
\usepackage{pdfpages} % Add this line to include the pdfpages package
\pagestyle{fancy}
\fancyhf{}
\renewcommand{\headrulewidth}{0pt}

\setlength{\TPHorizModule}{1mm}
\setlength{\TPVertModule}{1mm}

\begin{document}

<#list reviewContext.questions as question>
<#if reviewedRes[question?index]?has_content>
<#assign questionData = reviewedRes[question?index]>
<#assign count = questionData.count>
<#assign correct = questionData.correct!0>

\begin{textblock}{1000}(${question.checkArea.x / 300 * 25.4}, ${question.checkArea.y / 300 * 25.4})
\begin{minipage}{${question.checkArea.width / 300 * 25.4}mm}
<#if (correct/count > 0.6) >
\textcolor{green}{\fontsize{${reviewContext.signSize}}{50pt}\selectfont \textbf{${(correct/count*100)?string("0.00")}\%}}
<#else>
\textcolor{red}{\fontsize{${reviewContext.signSize}}{50pt}\selectfont \textbf{${(correct/count*100)?string("0.00")}\%}}
</#if>
\end{minipage}
\end{textblock}

</#if>
</#list>

<#if reviewContext.docurl?has_content>
\includepdf[pages=-, frame=true, scale=1, pagecommand={}]{${reviewContext.docurl?split("/")?last}}
<#else>
\null
\clearpage
</#if>

\end{document}
