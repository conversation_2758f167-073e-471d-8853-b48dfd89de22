package com.chaty.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.completion.CompletionService;
import com.chaty.dto.PaperTopicDTO;
import com.chaty.dto.RemoteFileWithRemarkDTO;
import com.chaty.entity.*;
import com.chaty.dto.FtpMessageDTO;
import com.chaty.dto.BindConfigPackageDTO;
import com.chaty.exception.BaseException;
import com.chaty.mapper.FtpMessageMapper;
import com.chaty.mapper.FtpFilesMapper;
import com.chaty.security.AuthUtil;
import com.chaty.service.cache.DefaultModelRedisService;
import com.chaty.service.FileService;
import com.chaty.service.FtpMessageService;
import com.chaty.service.RemoteFileService;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.util.StrUtil;

import java.io.IOException;
import java.util.*;
import java.io.File;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.chaty.mapper.DocCorrectFileMapper;
import com.chaty.mapper.DocCorrectConfigPackageMapper;

import javax.annotation.Resource;
import java.util.function.Function;
import java.time.LocalDateTime;
import java.util.stream.Stream;

import static com.chaty.enums.GptAskLogType.standardVolumeMatching;
// 新增 import
import com.chaty.mapper.FtpMessageUserTitleMapper;
import com.chaty.mapper.FtpMessageTitleMapper;
import com.chaty.entity.FtpMessageUserTitle;
import com.chaty.entity.FtpMessageTitle;

@Slf4j
@Service
public class FtpMessageServiceImpl extends ServiceImpl<FtpMessageMapper, FtpMessage> implements FtpMessageService {

    @Autowired
    private FtpMessageMapper ftpMessageMapper;
    @Resource
    private RemoteFileService remoteFileService;
    @Resource
    private FileService fileService;
    @Autowired
    private FtpFilesMapper ftpFilesMapper;
    @Resource
    private DefaultModelRedisService defaultModelRedisService;
    @Autowired
    private DocCorrectFileMapper docCorrectFileMapper;
    @Resource
    private CompletionService completionService;
    @Autowired
    private DocCorrectConfigPackageMapper docCorrectConfigPackageMapper;
    @Value("${file.local.ctxpath}")
    private String ctxPath;
    @Value("${file.local.path}")
    private String path;
    @Value("${remote.fileurl:https://alist.saomiaoshijuan.com}")
    private String alistUrl;
    // 在类里新增注入
    @Resource
    private FtpMessageUserTitleMapper ftpMessageUserTitleMapper;
    @Resource
    private FtpMessageTitleMapper ftpMessageTitleMapper;

// ===== 权限相关工具 =====
    /** 当前用户可访问的“学校名称”(即 ftp_message_title.title) 列表 */
    private List<String> getPermittedSchoolNamesOfCurrentUser() {
        String userId = AuthUtil.getLoginUser().getId();
        if (userId == null || userId.isEmpty()) return Collections.emptyList();

        // 取到当前用户的所有 titleId
        List<Integer> titleIds = ftpMessageUserTitleMapper.selectList(
                        Wrappers.<FtpMessageUserTitle>lambdaQuery()
                                .eq(FtpMessageUserTitle::getUserId, userId)
                ).stream().map(FtpMessageUserTitle::getTitleId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (titleIds.isEmpty()) return Collections.emptyList();

        // 根据 id 查出 title（学校名称）
        return ftpMessageTitleMapper.selectBatchIds(titleIds).stream()
                .map(FtpMessageTitle::getTitle)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    /** 该消息是否属于当前用户可访问的学校 */
    private boolean hasPermissionForMessage(FtpMessage msg) {
        if (msg == null || StrUtil.isBlank(msg.getSchoolName())) return false;
        List<String> names = getPermittedSchoolNamesOfCurrentUser();
        return !names.isEmpty() && names.contains(msg.getSchoolName());
    }


    @Override
    @Transactional
    public FtpMessageDTO updateMessage(FtpMessageDTO dto) {
        if (dto.getId() == null) {
            throw new BaseException("id is required for updating");
        }

        FtpMessage ftpMessage = getById(dto.getId());
        if (ftpMessage == null) {
            throw new BaseException("FTP message not found");
        }

        // ===== 权限校验 =====
        if (!hasPermissionForMessage(ftpMessage)) {
            throw new BaseException("no permission to update this message");
        }

        // 如果exportCompleted从false变为true，设置exportCompletedTime为当前时间
        if (Boolean.TRUE.equals(dto.getExportCompleted()) && !Boolean.TRUE.equals(ftpMessage.getExportCompleted())) {
            dto.setExportCompletedTime(LocalDateTime.now());
        }

        BeanUtils.copyProperties(dto, ftpMessage, "id", "createTime", "updateTime");
        updateById(ftpMessage);

        FtpMessageDTO result = new FtpMessageDTO();
        BeanUtils.copyProperties(ftpMessage, result);
        return result;
    }


    @Override
    @Transactional
    public void deleteMessage(Integer id) {
        if (id == null) {
            throw new BaseException("id is required for deleting");
        }

        FtpMessage ftpMessage = getById(id);
        if (ftpMessage == null) {
            throw new BaseException("FTP message not found");
        }

        // ===== 权限校验 =====
        if (!hasPermissionForMessage(ftpMessage)) {
            throw new BaseException("no permission to delete this message");
        }

        removeById(id);
    }


    @Override
    public IPage<FtpMessageDTO> selectPage(FtpMessageDTO param) {

        LambdaQueryWrapper<FtpMessage> queryWrapper = Wrappers.lambdaQuery();

        queryWrapper.eq(Objects.nonNull(param.getId()), FtpMessage::getId, param.getId());
        queryWrapper.eq(Objects.nonNull(param.getNotificationCount()), FtpMessage::getNotificationCount, param.getNotificationCount());
        queryWrapper.like(StrUtil.isNotBlank(param.getSchoolName()), FtpMessage::getSchoolName, param.getSchoolName());
        queryWrapper.eq(Objects.nonNull(param.getNotificationTime()), FtpMessage::getNotificationTime, param.getNotificationTime());
        queryWrapper.like(StrUtil.isNotBlank(param.getFilePath()), FtpMessage::getFilePath, param.getFilePath());
        queryWrapper.like(StrUtil.isNotBlank(param.getFileName()), FtpMessage::getFileName, param.getFileName());
        queryWrapper.like(StrUtil.isNotBlank(param.getFileDetail()), FtpMessage::getFileDetail, param.getFileDetail());
        queryWrapper.like(StrUtil.isNotBlank(param.getRemark()), FtpMessage::getRemark, param.getRemark());
        queryWrapper.like(StrUtil.isNotBlank(param.getFileRemark()), FtpMessage::getFileRemark, param.getFileRemark());
        queryWrapper.eq(Objects.nonNull(param.getFileCompleted()), FtpMessage::getFileCompleted, param.getFileCompleted());
        queryWrapper.eq(Objects.nonNull(param.getConfigPackageCompleted()), FtpMessage::getConfigPackageCompleted, param.getConfigPackageCompleted());
        queryWrapper.eq(Objects.nonNull(param.getExportCompleted()), FtpMessage::getExportCompleted, param.getExportCompleted());
        queryWrapper.eq(Objects.nonNull(param.getCreateTime()), FtpMessage::getCreateTime, param.getCreateTime());
        queryWrapper.eq(Objects.nonNull(param.getUpdateTime()), FtpMessage::getUpdateTime, param.getUpdateTime());
        queryWrapper.eq(Objects.nonNull(param.getIsCollect()), FtpMessage::getIsCollect, param.getIsCollect());

        // 处理是否只查询今天的数据
        if (Boolean.TRUE.equals(param.getIsSelectToday())) {
            LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
            LocalDateTime tomorrow = today.plusDays(1);
            queryWrapper.ge(FtpMessage::getNotificationTime, today)
                    .lt(FtpMessage::getNotificationTime, tomorrow);
        }

        queryWrapper.orderByDesc(FtpMessage::getNotificationTime);

        if (param.getPage() == null) {
            throw new BaseException("Pagination parameters are required");
        }

        List<String> permittedSchools = getPermittedSchoolNamesOfCurrentUser();
        if (permittedSchools.isEmpty()) {
            // 构造一个必不成立的条件，返回空页（也可以直接构造空 Page 返回）
            queryWrapper.eq(FtpMessage::getId, -1);
        } else {
            queryWrapper.in(FtpMessage::getSchoolName, permittedSchools);
        }
        queryWrapper.orderByDesc(FtpMessage::getNotificationTime);
        if (param.getPage() == null) {
            throw new BaseException("Pagination parameters are required");
        }

        IPage<FtpMessage> ftpMessagePage = page(param.getPage().page(FtpMessage.class), queryWrapper);

        List<String> filePaths = ftpMessagePage.getRecords().stream()
                .map(FtpMessage::getFilePath)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());
        List<String> fileNames = ftpMessagePage.getRecords().stream()
                .map(FtpMessage::getFileName)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        List<String> parentPaths = filePaths.stream()
                .map(filePath -> {
                    int lastSeparatorIndex = filePath.lastIndexOf('/');
                    return (lastSeparatorIndex != -1) ? filePath.substring(0, lastSeparatorIndex) : filePath;
                })
                .distinct()
                .collect(Collectors.toList());

        Map<String, FtpFiles> ftpFilesMap;
        if (!parentPaths.isEmpty() && !fileNames.isEmpty()) {
            ftpFilesMap = ftpFilesMapper.selectList(Wrappers.<FtpFiles>lambdaQuery()
                            .in(FtpFiles::getPath, parentPaths)
                            .in(FtpFiles::getFilename, fileNames))
                    .stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(
                            ftpFile -> ftpFile.getPath() + "/" + ftpFile.getFilename(),
                            ftpFile -> ftpFile,
                            (existing, replacement) -> {
                                return existing.getCreateTime().compareTo(replacement.getCreateTime()) >= 0
                                        ? existing
                                        : replacement;
                            }
                    ));
        } else {
            ftpFilesMap = new java.util.HashMap<>();
        }

        if (!ftpFilesMap.isEmpty()) {
            String sampleKey = ftpFilesMap.keySet().iterator().next();
        }

        // 获取所有匹配的 fileId
        List<String> fileIds = ftpFilesMap.values().stream()
                .map(FtpFiles::getFileId)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        // 获取所有匹配的 configPackageId
        List<String> configPackageIds = ftpFilesMap.values().stream()
                .map(FtpFiles::getConfigPackageId)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        // 查询 doc_correct_file 表
        final Map<String, DocCorrectFile> docCorrectFileMap;
        if (!fileIds.isEmpty()) {
            List<DocCorrectFile> docCorrectFiles = docCorrectFileMapper.selectList(
                    Wrappers.<DocCorrectFile>lambdaQuery()
                            .in(DocCorrectFile::getId, fileIds)
            );
            docCorrectFileMap = docCorrectFiles.stream().collect(Collectors.toMap(
                    DocCorrectFile::getId,
                    Function.identity(),
                    (existing, replacement) -> existing
            ));
        } else {
            docCorrectFileMap = new HashMap<>();
        }

        // 查询 doc_correct_config_package 表
        final Map<String, DocCorrectConfigPackage> configPackageMap;
        if (!configPackageIds.isEmpty()) {
            List<DocCorrectConfigPackage> configPackages = docCorrectConfigPackageMapper.selectList(
                    Wrappers.<DocCorrectConfigPackage>lambdaQuery()
                            .in(DocCorrectConfigPackage::getId, configPackageIds)
            );
            configPackageMap = configPackages.stream().collect(Collectors.toMap(
                    DocCorrectConfigPackage::getId,
                    Function.identity(),
                    (existing, replacement) -> existing
            ));
        } else {
            configPackageMap = new HashMap<>();
        }

        return ftpMessagePage.convert(entity -> {
            FtpMessageDTO dto = new FtpMessageDTO();
            BeanUtils.copyProperties(entity, dto);

            String fullFilePath = entity.getFilePath();
            int lastSeparatorIndex = fullFilePath.lastIndexOf('/');
            String parentPath = (lastSeparatorIndex != -1) ? fullFilePath.substring(0, lastSeparatorIndex) : fullFilePath;

            String key = parentPath + "/" + entity.getFileName();

            if (ftpFilesMap.containsKey(key)) {
                FtpFiles ftpFile = ftpFilesMap.get(key);

                dto.setFileId(ftpFile.getFileId());

                // 设置 doc_correct_file 对象
                if (StrUtil.isNotBlank(ftpFile.getFileId())) {
                    if (docCorrectFileMap.containsKey(ftpFile.getFileId())) {
                        dto.setDocCorrectFile(docCorrectFileMap.get(ftpFile.getFileId()));
                    }
                }

                // 设置 doc_correct_config_package 对象
                if (StrUtil.isNotBlank(ftpFile.getConfigPackageId())) {
                    if (configPackageMap.containsKey(ftpFile.getConfigPackageId())) {
                        dto.setDocCorrectConfigPackage(configPackageMap.get(ftpFile.getConfigPackageId()));
                    }
                }
            }
            if (Boolean.FALSE.equals(dto.getFileCompleted()) && Objects.nonNull(dto.getDocCorrectFile()) && StrUtil.isNotBlank(dto.getDocCorrectFile().getId())) {
                // 数据校验
                FtpMessage update = new FtpMessage();
                update.setId(dto.getId());
                update.setFileCompleted(Boolean.TRUE);
                ftpMessageMapper.updateById(update);
            }
            return dto;
        });
    }

    @Override
    public Map<String, Long> countUnFinishedUploadsBySchool() {
        List<String> permittedSchools = getPermittedSchoolNamesOfCurrentUser();
        if (permittedSchools.isEmpty()) {
            return Collections.emptyMap();
        }

        List<Map<String, Object>> raw = ftpMessageMapper.selectMaps(
                Wrappers.<FtpMessage>query()
                        .select("school_name", "COUNT(*) AS cnt")
                        .eq("export_completed", false)
                        .in("school_name", permittedSchools)
                        .groupBy("school_name")
        );

        return raw.stream()
                .collect(Collectors.toMap(
                        m -> (String) m.get("school_name"),
                        m -> ((Number) m.get("cnt")).longValue()
                ));
    }


    @Override
    public List<FtpMessageDTO> selectSameTopic(String configPackageId) {
        if (StrUtil.isBlank(configPackageId)) {
            return new ArrayList<>();
        }

        // 1. 根据configPackageId查询ftp_files表的第一条记录
        LambdaQueryWrapper<FtpFiles> ftpFilesWrapper = Wrappers.lambdaQuery();
        ftpFilesWrapper.eq(FtpFiles::getConfigPackageId, configPackageId)
                .last("LIMIT 1");
        FtpFiles ftpFile = ftpFilesMapper.selectOne(ftpFilesWrapper);

        if (ftpFile == null) {
            return new ArrayList<>();
        }

        // 2. 根据ftp_files的记录查询对应的ftp_message记录
        LambdaQueryWrapper<FtpMessage> ftpMessageWrapper = Wrappers.lambdaQuery();
        ftpMessageWrapper.apply("CONCAT(path, '/', filename) = {0}", ftpFile.getPath());
        FtpMessage ftpMessage = getOne(ftpMessageWrapper);

        if (ftpMessage == null || StrUtil.isBlank(ftpMessage.getSameToOthersPaperId())) {
            return new ArrayList<>();
        }

        // 3. 查询所有具有相同sameToOthersPaperId的记录
        LambdaQueryWrapper<FtpMessage> sameTopicWrapper = Wrappers.lambdaQuery();
        sameTopicWrapper.eq(FtpMessage::getSameToOthersPaperId, ftpMessage.getSameToOthersPaperId());
        List<FtpMessage> sameTopicMessages = list(sameTopicWrapper);

        // 4. 转换为DTO并处理空值
        return sameTopicMessages.stream()
                .map(message -> {
                    FtpMessageDTO dto = new FtpMessageDTO();
                    BeanUtils.copyProperties(message, dto);

                    // 设置默认值，避免空指针
                    if (dto.getFileCompleted() == null) dto.setFileCompleted(false);
                    if (dto.getConfigPackageCompleted() == null) dto.setConfigPackageCompleted(false);
                    if (dto.getExportCompleted() == null) dto.setExportCompleted(false);
                    if (dto.getIsCollect() == null) dto.setIsCollect(false);
                    if (dto.getNotificationCount() == null) dto.setNotificationCount(0);

                    return dto;
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bindConfigPackage(BindConfigPackageDTO dto) {
        if (dto == null || dto.getFtpMessageIds() == null || dto.getFtpMessageIds().isEmpty()
                || StrUtil.isBlank(dto.getConfigPackageId())) {
            return false;
        }

        // 1. 查询FtpMessage列表
        List<FtpMessage> ftpMessages = listByIds(dto.getFtpMessageIds());
        if (ftpMessages.isEmpty()) {
            return false;
        }

        // 2. 查询对应的ftp_files记录，使用CONCAT(path, '/', filename) = ftp_message.path进行匹配
        List<FtpFiles> ftpFilesList = new ArrayList<>();
        for (FtpMessage message : ftpMessages) {
            LambdaQueryWrapper<FtpFiles> ftpFilesWrapper = Wrappers.lambdaQuery();
            ftpFilesWrapper.apply("CONCAT(path, '/', filename) = {0}", message.getFilePath());
            FtpFiles ftpFile = ftpFilesMapper.selectOne(ftpFilesWrapper);
            if (ftpFile != null) {
                ftpFilesList.add(ftpFile);
            }
        }

        if (ftpFilesList.isEmpty()) {
            return false;
        }

        // 3. 更新ftp_files记录
        List<FtpFiles> updatedFtpFiles = ftpFilesList.stream()
                .map(ftpFile -> {
                    // 设置默认值，避免空指针
                    if (ftpFile.getType() == null) {
                        ftpFile.setType("file");
                    }
                    if (ftpFile.getName() == null) {
                        ftpFile.setName(ftpFile.getFilename());
                    }
                    if (ftpFile.getRemark() == null) {
                        ftpFile.setRemark("");
                    }
                    if (ftpFile.getFileId() == null) {
                        ftpFile.setFileId("");
                    }

                    // 更新configPackageId
                    ftpFile.setConfigPackageId(dto.getConfigPackageId());
                    return ftpFile;
                })
                .collect(Collectors.toList());

        // 4. 循环更新数据库
        boolean allSuccess = true;
        for (FtpFiles ftpFile : updatedFtpFiles) {
            int result = ftpFilesMapper.updateById(ftpFile);
            if (result <= 0) {
                allSuccess = false;
                break;
            }
        }
        return allSuccess;
    }

    @Override
    public FtpMessage getFtpMessageById(Integer id) {
        if (id == null) return null;
        FtpMessage m = getById(id);
        if (m == null) return null;
        return hasPermissionForMessage(m) ? m : null;
    }

    @Override
    public List<String> titles(String keyword) {
        // 1) 取当前用户可访问的学校（title）
        List<String> permittedSchools = getPermittedSchoolNamesOfCurrentUser();
        if (permittedSchools.isEmpty()) {
            return Collections.emptyList();
        }

        // 2) 在有权限的学校里做 like 查询 + 限制返回数量
        QueryWrapper<FtpMessage> wrapper = new QueryWrapper<>();
        wrapper.select("DISTINCT school_name")
                .in("school_name", permittedSchools)
                .like(StrUtil.isNotBlank(keyword), "school_name", keyword)
                .orderByDesc("school_name")
                .last("LIMIT 20");

        List<Object> list = ftpMessageMapper.selectObjs(wrapper);
        List<String> res = new ArrayList<>();
        if (list != null) {
            for (Object obj : list) {
                if (obj != null) res.add((String) obj);
            }
        }
        return res;
    }

    public PaperTopicDTO getTopicWithMatch(FtpMessage message) {
        if (StrUtil.isBlank(message.getImgUrl())) {
            return null;
        }
        List<PaperTopicDTO> topics = new ArrayList<>();
        double width = message.getWidth();
        double height = message.getHeight();
        // 近三天的
        QueryWrapper<FtpMessage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(message.getSchoolName()), "school_name", message.getSchoolName())
                .ge("notification_time", LocalDateTime.now().minusDays(7));
        if (!Double.isNaN(width) && !Double.isNaN(height) && width > 0 && height > 0) {
            double tolerance = 50.0;
            queryWrapper.between("width", width - tolerance, width + tolerance);
            queryWrapper.between("height", height - tolerance, height + tolerance);
        }

        List<FtpMessage> dbFtpMessages = ftpMessageMapper.selectList(queryWrapper);

        List<FtpMessage> nullGroup = dbFtpMessages.stream()
                .filter(m -> StrUtil.isBlank(m.getSameToOthersPaperId()))
                .collect(Collectors.toList());
        List<FtpMessage> deduped = dbFtpMessages.stream()
                .filter(m -> StrUtil.isNotBlank(m.getSameToOthersPaperId()))
                .collect(Collectors.groupingBy(
                        FtpMessage::getSameToOthersPaperId,
                        Collectors.minBy(Comparator.comparing(FtpMessage::getCreateTime))
                ))
                .values().stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
        List<FtpMessage> ftpMessages = Stream.concat(
                        nullGroup.stream(),
                        deduped.stream()
                )
                .collect(Collectors.toList());

        for (FtpMessage ftpMessage : ftpMessages) {
            if (StrUtil.isBlank(ftpMessage.getTopic())) {
                continue;
            }
            try {
                PaperTopicDTO paperTopicDTO = JSONUtil.toBean(ftpMessage.getTopic(), PaperTopicDTO.class);
                if (Objects.nonNull(paperTopicDTO) && StrUtil.isNotBlank(paperTopicDTO.getName()) && Boolean.FALSE.equals(paperTopicDTO.getIsSameToOthersPaper())) {
                    paperTopicDTO.setId(ftpMessage.getId());
                    topics.add(paperTopicDTO);
                }
            } catch (Exception e) {
                log.error("解析文件详情失败 e:{}", e.getMessage());
            }
        }
        GptAskLogEntity gptAskLogEntity = new GptAskLogEntity();
        gptAskLogEntity.setType(standardVolumeMatching);
        gptAskLogEntity.setAimodel(defaultModelRedisService.getModelValue());
        gptAskLogEntity.setFileId("");
        gptAskLogEntity.setStartTime(System.currentTimeMillis());

        List<String> imgUrls = new ArrayList<>();
        if (StrUtil.isNotBlank(message.getFileDetail())) {
            try {
                Map<String, Object> fileDetail = JSONUtil.parseObj(message.getFileDetail());
                imgUrls = (List<String>) fileDetail.get("first4pageList");
            } catch (Exception e) {
                log.error("获取 first4pageList失败 message:{}", message);
            }
        }
        if (imgUrls.isEmpty()) {
            imgUrls.add(message.getImgUrl());
        }
        return completionService.extraPaperTopic(imgUrls, topics, defaultModelRedisService.getModelValue(), gptAskLogEntity);
    }

    @Override
    public PaperTopicDTO getTopic(FtpMessage message) {
        if (StrUtil.isBlank(message.getImgUrl())) {
            return null;
        }
        GptAskLogEntity gptAskLogEntity = new GptAskLogEntity();
        gptAskLogEntity.setType(standardVolumeMatching);
        gptAskLogEntity.setAimodel(defaultModelRedisService.getModelValue());
        gptAskLogEntity.setFileId("");
        gptAskLogEntity.setStartTime(System.currentTimeMillis());

        List<String> imgUrls = new ArrayList<>();
        if (StrUtil.isNotBlank(message.getFileDetail())) {
            try {
                Map<String, Object> fileDetail = JSONUtil.parseObj(message.getFileDetail());
                imgUrls = (List<String>) fileDetail.get("first4pageList");
            } catch (Exception e) {
                log.error("获取 first4pageList失败 message:{}", message);
            }
        }
        if (imgUrls.isEmpty()) {
            imgUrls.add(message.getImgUrl());
        }
        return completionService.extraPaperTopic(imgUrls, new ArrayList<>(), defaultModelRedisService.getModelValue(), gptAskLogEntity);
    }

    /**
     * 处理相同标准卷的逻辑
     * 1. 根据sameToOthersPaperId获取相同标准卷的FtpMessage
     * 2. 查找对应的ftp_files记录
     * 3. 为当前message创建新的ftp_files记录，如果找到相同标准卷的configPackageId则复制
     *
     * @param message             当前消息对象
     * @param sameToOthersPaperId 相同标准卷的ID
     */
    @Override
    public void handleSamePaper(FtpMessage message, String sameToOthersPaperId) {
        // 1. 获取相同标准卷的FtpMessage对象
        Integer samePaperId = null;
        try {
            samePaperId = Integer.parseInt(sameToOthersPaperId);
        } catch (NumberFormatException e) {
            log.error("Invalid sameToOthersPaperId: {}, continuing without same paper processing", sameToOthersPaperId);
            return;
        }

        FtpMessage samePaperMessage = getFtpMessageById(samePaperId);
        if (samePaperMessage != null) {
            // 2. 根据匹配原则查询ftp_files表获取ftpfiles1对象
            LambdaQueryWrapper<FtpFiles> ftpFilesWrapper1 = Wrappers.lambdaQuery();
            ftpFilesWrapper1.apply("CONCAT(path, '/', filename) = {0}", samePaperMessage.getFilePath());
            FtpFiles ftpFiles1 = ftpFilesMapper.selectOne(ftpFilesWrapper1);

            // 3. 为当前message创建新的ftp_files记录
            FtpFiles ftpFiles2 = new FtpFiles();
            // 从message.path中提取目录路径（去掉文件名部分）
            String fullPath = message.getFilePath();
            int lastSlashIndex = fullPath.lastIndexOf('/');
            String dirPath = lastSlashIndex != -1 ? fullPath.substring(0, lastSlashIndex) : fullPath;
            String filename = lastSlashIndex != -1 ? fullPath.substring(lastSlashIndex + 1) : fullPath;

            ftpFiles2.setPath(dirPath);
            ftpFiles2.setFilename(filename);
            ftpFiles2.setType("configPackage");
            // 如果ftpFiles1存在且有configPackageId，则复制过来
            if (ftpFiles1 != null && StrUtil.isNotBlank(ftpFiles1.getConfigPackageId())) {
                ftpFiles2.setConfigPackageId(ftpFiles1.getConfigPackageId());
            }
            // 插入新记录
            ftpFilesMapper.insert(ftpFiles2);
        }
    }

    /**
     * 重试下载并校验 PDF，可解析时将 fileDetail 填充到 message。
     * 每次重试都重新过滤最新的 RemoteFileWithRemarkDTO 并生成 URL。
     *
     * @param message    输出目标对象
     * @param parentPath 远程文件父目录
     * @return 若下载+校验+填充成功，返回 true；否则返回 false
     */
    @Override
    public JSONObject downloadAndAttachPdfDetail(
            FtpMessage message,
            String parentPath
    ) {
        final int MAX_RETRIES = 5;
        int RETRIES_TIME = 2;
        for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
            String url = "";
            RemoteFileWithRemarkDTO dto = null;
            // 每次都重新获取 DTO 并构造 URL
            try {
                List<RemoteFileWithRemarkDTO> remoteFileWithRemarkDTOS = remoteFileService.listWithRemark(parentPath, "");
                dto = remoteFileWithRemarkDTOS.stream()
                        .filter(d -> message.getFileName().equals(d.getName()))
                        .findFirst()
                        .orElse(null);
                if (dto == null) {
                    log.error("找不到文件 {} 的 RemoteFileWithRemarkDTO", message.getFileName());
                    return null;
                }
                url = getRemoteFileUrl(parentPath, dto.getName(), dto.getSign());
            } catch (Exception e) {
                log.error("第 {} 次尝试：获取 RemoteFileWithRemarkDTO 失败 → {}", attempt, e.getMessage());
                continue; // 跳过当前循环，进行下一次尝试
            }

            if (StrUtil.isBlank(url) || Objects.isNull(dto)) {
                continue;
            }

            try {
                // 调用文件服务下载
                Map<String, Object> result = fileService.saveRemoteFile(url, "alist");
                String localPath = result.get("url").toString().replace(ctxPath, path);
                File file = new File(localPath);

                // 校验 PDF 可解析性
                try (PDDocument doc = PDDocument.load(file)) {
                    // 构造 fileDetail 并返回
                    JSONObject detail = JSONUtil.parseObj(dto);
                    detail.set("url", result.get("url").toString());
                    detail.set("path", parentPath);
                    message.setFileDetail(JSONUtil.toJsonStr(detail));
                    int totalPageSize = doc.getNumberOfPages();
                    detail.set("pdfPaperSize", totalPageSize);
                    // 保存第一页pdf
                    String filename = localPath.split("/")[localPath.split("/").length - 1];
                    int firstPageNumber = 1;
                    if (totalPageSize <= 60) {
                        firstPageNumber = 2;
                    } else if (totalPageSize <= 120) {
                        firstPageNumber = 3;
                    } else {
                        firstPageNumber = 5;
                    }
                    if (firstPageNumber > totalPageSize) {
                        firstPageNumber = 1;
                    }
                    Map<String, Object> firstPage = fileService.pdf2Img(filename, firstPageNumber);
                    detail.set("firstPage", firstPage);

                    List<Map<String, Object>> first4page = new ArrayList<>();
                    first4page.add(firstPage);
//                    for (int i = 2; i < doc.getNumberOfPages() && i <= 4; i++) {
//                        Map<String, Object> page = fileService.pdf2Img(filename, i);
//                        first4page.add(page);
//                    }
                    detail.set("first4page", first4page);
                    List<String> first4pageList = new ArrayList<>();
                    for (Map<String, Object> page : first4page) {
                        first4pageList.add(page.get("url").toString());
                    }
                    detail.set("first4pageList", first4pageList);

                    log.info("第 {} 次尝试：下载并校验通过 → {} firstPage:{}", attempt, url, firstPage);
                    return detail;
                }
            } catch (IOException parseEx) {
                log.warn("第 {} 次尝试：PDF 解析失败，准备重试 → {}", attempt, parseEx.getMessage());
            } catch (Exception downloadEx) {
                log.error("第 {} 次尝试：下载失败 → {}", attempt, downloadEx.getMessage());
            }

            if (attempt < MAX_RETRIES) {
                try {
                    TimeUnit.SECONDS.sleep(RETRIES_TIME);
                    RETRIES_TIME = RETRIES_TIME * 2;
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.error("重试等待被中断", ie);
                    break;
                }
            } else {
                log.error("已达最大重试次数（{}），下载或校验均失败", MAX_RETRIES);
            }
        }
        return null;
    }

    public String getRemoteFileUrl(String path, String filename, String sign) {
        return alistUrl + File.separator + "d" + path + File.separator + filename + "?sign=" + sign;
    }

} 