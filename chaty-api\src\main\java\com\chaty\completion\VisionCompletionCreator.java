package com.chaty.completion;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import cn.hutool.core.util.StrUtil;
import com.chaty.dto.PaperTopicDTO;
import com.chaty.enums.AIModelMaxTokenConsts;
import com.chaty.enums.DefaultCorrectConfigsConsts;
import com.chaty.service.PromptsRedisService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.chaty.dto.ChatCompletionDTO;
import com.chaty.dto.DocCorrectRecordDTO;
import com.chaty.dto.MessageDTO;
import com.chaty.entity.ModelSetting;
import com.chaty.enums.AIModelConsts;
import com.chaty.form.ExtraQsForm;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import static com.chaty.completion.DouBaoCompletionCreator.getOtherTopics;

@Slf4j
@Service
public class VisionCompletionCreator implements CompletionCreator {

    @Value("${server.url}")
    public String serverUrl;

    @Resource
    private PromptsRedisService promptsRedisService;

    @Override
    public ChatCompletionDTO createDocAreaCompletion(DocCorrectRecordDTO record, JSONObject areaObj,
                                                     JSONObject areaRes) {
        String aimodel = record.getAimodel();
        ChatCompletionDTO completion = new ChatCompletionDTO();
        completion.setModel(aimodel);
        completion.setMaxTokens(DefaultCorrectConfigsConsts.maxTokens);
        // 系统提示词
        List<MessageDTO> messages = new ArrayList<MessageDTO>();
        String sysMsg = promptsRedisService.getDocCorrectV2();
        if (Objects.nonNull(record.getConfig())) {
            sysMsg = record.getConfig().getConfigObj().getStr("prompt", sysMsg);
        }
        setSystemMessage(messages, aimodel, sysMsg);
        // 批改逻辑
        StringBuilder content = new StringBuilder("# 下面是所有的问题和答案：\n---\n");
        JSONArray questions = areaObj.getJSONArray("questions");
        questions.forEach(qs -> {
            JSONObject question = (JSONObject) qs;
            content.append(String.format(promptsRedisService.getDocCorrectContentTemplateV1(),
                    question.getStr("question"), question.getStr("qsInfo", ""),
                    question.getStr("answer"), question.getStr("scorePoints", ""),
                    question.getStr("score", "")));
            content.append("\n---\n");
        });
//        log.info("request content: {} {} \n {}", record.getId(), record.getDocname(), content.toString());
        String areaImg = areaObj.getStr("areaImg");
        MessageDTO message;
        if (areaImg.startsWith("data:image")) {
            message = createImgMessage(aimodel, "user", content.toString(), false, areaImg);
        } else {
            String areaImgUrl = areaImg;
            if (!areaImg.startsWith("http")) {
                areaImgUrl = String.format("%s%s", serverUrl, areaImg);
            }
            message = createImgMessage(aimodel, "user", content.toString(), true, areaObj, areaImgUrl);
        }
        messages.add(message);

        completion.setMessages(messages);
        return completion;
    }

    @Override
    public ChatCompletionDTO createDocAreaNormalQsFirstRequest(DocCorrectRecordDTO record, JSONObject areaObj,
                                                               JSONObject areaRes) {
        String aimodel = record.getAimodel();
        ChatCompletionDTO completion = new ChatCompletionDTO();
        completion.setModel(aimodel);
        completion.setMaxTokens(DefaultCorrectConfigsConsts.maxTokens);
        // 系统提示词
        List<MessageDTO> messages = new ArrayList<MessageDTO>();
        String sysMsg = promptsRedisService.getCommonQuestionFirstRequest(record.getModelRequestId(), (String) areaObj.getOrDefault("commonQuestionType", "通用"));
        if (Objects.nonNull(record.getConfig())) {
            sysMsg = record.getConfig().getConfigObj().getStr("prompt", sysMsg);
        }
        setSystemMessage(messages, aimodel, sysMsg);
        // 批改逻辑
        StringBuilder content = new StringBuilder("仔细识别图片中的学生答案");

        String areaImg = areaObj.getStr("areaImg");
        MessageDTO message;
        if (areaImg.startsWith("data:image")) {
            message = createImgMessage(aimodel, "user", content.toString(), false, areaImg);
        } else {
            String areaImgUrl = areaImg;
            if (!areaImg.startsWith("http")) {
                areaImgUrl = String.format("%s%s", serverUrl, areaImg);
            }
            message = createImgMessage(aimodel, "user", content.toString(), true, areaObj, areaImgUrl);
        }
        messages.add(message);

        completion.setMessages(messages);
        return completion;
    }

    @Override
    public ChatCompletionDTO createDocAreaNormalQsSecondRequest(DocCorrectRecordDTO record, JSONObject areaObj,
                                                               JSONObject areaRes, JSONArray resolvedRes) {
        String aimodel = record.getAimodel();
        ChatCompletionDTO completion = new ChatCompletionDTO();
        completion.setModel(aimodel);
        completion.setMaxTokens(DefaultCorrectConfigsConsts.maxTokens);
        // 系统提示词
        List<MessageDTO> messages = new ArrayList<MessageDTO>();
        String sysMsg = promptsRedisService.getCommonQuestionSecondRequest(record.getModelRequestId());
        if (Objects.nonNull(record.getConfig())) {
            sysMsg = record.getConfig().getConfigObj().getStr("prompt", sysMsg);
        }
        sysMsg += " 下面是学生答案" +
                "<学生答案>" +
                JSONUtil.toJsonStr(resolvedRes) +
                "</学生答案>";
        setSystemMessage(messages, aimodel, sysMsg);
        // 根据 isSecondRoundUseImage 参数决定是否添加图片
        ModelSetting modelSetting = record.getModelSetting();
        boolean useImageInSecondRound = modelSetting != null && Boolean.TRUE.equals(modelSetting.getIsSecondRoundUseImage());

        if (useImageInSecondRound) {
            // 批改逻辑
            StringBuilder content = new StringBuilder("仔细识别图片中的学生答案");

            String areaImg = areaObj.getStr("areaImg");
            MessageDTO message;
            if (areaImg.startsWith("data:image")) {
                message = createImgMessage(aimodel, "user", content.toString(), false, areaImg);
            } else {
                String areaImgUrl = areaImg;
                if (!areaImg.startsWith("http")) {
                    areaImgUrl = String.format("%s%s", serverUrl, areaImg);
                }
                message = createImgMessage(aimodel, "user", content.toString(), true, areaImgUrl);
            }
            messages.add(message);
        } else {
            // 不使用图片，只添加文本消息
            MessageDTO message = new MessageDTO();
            message.setRole("user");
            message.setContent("请根据第一轮识别的学生答案进行批改");
            messages.add(message);
        }

        completion.setMessages(messages);
        return completion;
    }

    @Override
    public boolean isSupported(String aimodel, Boolean jsonSchema) {
        // 必须是json_schema
        return Boolean.TRUE.equals(jsonSchema) || AIModelConsts.visionModels.contains(aimodel);
    }

    @Override
    public ChatCompletionDTO createRxtraQsCompletion(ExtraQsForm params) {
        String aimodel = params.getAimodel();

        ChatCompletionDTO completion = new ChatCompletionDTO();
        completion.setModel(aimodel);
        completion.setMaxTokens(4096);

        // 系统提示词
        List<MessageDTO> messages = new ArrayList<MessageDTO>();
        // String prompt = Optional.ofNullable(params.getPrompt()).orElse(PromptsConsts.EXTRA_QS_TEMPLATE);
        String prompt = Optional.ofNullable(params.getPrompt()).orElse(promptsRedisService.getExtraQsRespformatTemplete());
        if (params.getEnableOcr()) {
            prompt = String.format(prompt, params.getOcrResult());
        }
        MessageDTO message = createImgMessage(aimodel, "user",
                prompt, false,
                "data:image/jpeg;base64," + params.getImgStr());
        messages.add(message);
        // 结构化输出
        completion.setResponseFormat(JSONUtil.parseObj(promptsRedisService.getExtraQsRespformatSchema()));

        completion.setMessages(messages);
        return completion;
    }

    @Override
    public ChatCompletionDTO createDocAreaResponseFormatCompletion(DocCorrectRecordDTO record,
                                                                   JSONObject areaObj,
                                                                   JSONObject areaRes,
                                                                   String ocrContent) {
        String aimodel = record.getAimodel();
        ChatCompletionDTO completion = new ChatCompletionDTO();
        completion.setModel(aimodel);
        completion.setMaxTokens(4096);
        // 系统提示词
        List<MessageDTO> messages = new ArrayList<MessageDTO>();
        String sysMsg = null;
        if (StrUtil.isNotBlank(ocrContent)) {
            sysMsg = String.format(promptsRedisService.getDocCorrectResponseFormatTemplate(record.getModelRequestId()) + "\n ocr的识别结果：%s", ocrContent);
        } else {
            sysMsg = promptsRedisService.getDocCorrectResponseFormat(record.getModelRequestId());
        }
        if (Objects.nonNull(record.getConfig())) {
            sysMsg = record.getConfig().getConfigObj().getStr("prompt", sysMsg);
        }
        setSystemMessage(messages, aimodel, sysMsg);
        // 批改逻辑
        StringBuilder content = new StringBuilder();
        JSONArray questions = areaObj.getJSONArray("questions");
        for (int index = 0; index < questions.size(); index++) {
            JSONObject question = (JSONObject) questions.get(index);
            content.append(String.format(promptsRedisService.getDocCorrectResponseFormatTemplate(record.getModelRequestId()),
                    index + 1,
                    question.getStr("question"),
                    question.getStr("qsInfo", ""),
                    question.getStr("answer"),
                    question.getStr("score", "未定义"),
                    question.getStr("scorePoints", "未定义")));
            content.append("\n");
        }

        String areaImg = areaObj.getStr("areaImg");
        MessageDTO message;
        if (Base64.isBase64(areaImg)) {
            message = createImgMessage(aimodel, "user", content.toString(), false, areaImg);
        } else {
            String areaImgUrl = areaImg;
            if (!areaImg.startsWith("http")) {
                areaImgUrl = String.format("%s%s", serverUrl, areaImg);
            }
            message = createImgMessage(aimodel, "user", content.toString(), true, areaImgUrl);
        }
        messages.add(message);
        // 结构化输出
        JSONObject responseFormat = JSONUtil.parseObj(promptsRedisService.getDocCorrectResponseFormatSchema1(record.getModelRequestId()));
        // properties增加cot分三步：
        // 第一步识别图片中的所有文字和图片内容
        JSONObject step1 = JSONUtil.parseObj(promptsRedisService.getDocCorrectResponseFormatSchema3(record.getModelRequestId()));
        String step1Name = "第一步识别图片中的所有文字和图片内容";
        JSONObject stringSchema = new JSONObject();
        stringSchema.set("type", "string");
        // 第二步识别题干和完整学生回答内容
        JSONObject step2 = new JSONObject();
        String step2Name = "第二步识别题干和完整学生回答内容";
        step2.set("type", "object");
        step2.set("additionalProperties", false);
        JSONObject step2Properties = new JSONObject();
        JSONArray step2Required = new JSONArray();
        for (int i = 0; i < questions.size(); i++) {
            String propName = "题目" + (i + 1);
            step2Required.add(propName);
            String step2InSchemaJsonStr = String.format(promptsRedisService.getDocCorrectResponseFormatSchema4(record.getModelRequestId()),
                    (i + 1),
                    (i + 1),
                    (i + 1),
                    (i + 1));
            JSONObject step2InSchema = JSONUtil.parseObj(step2InSchemaJsonStr);
            step2Properties.set(propName, step2InSchema);
        }
        step2.set("required", step2Required);
        step2.set("properties", step2Properties);
        // 第三步识别学生答案
        JSONObject step3 = new JSONObject();
        String step3Name = "第三步识别学生答案";
        step3.set("type", "object");
        step3.set("additionalProperties", false);
        JSONObject step3Properties = new JSONObject();
        JSONArray step3Required = new JSONArray();
        stringSchema.set("type", "string");
        for (int i = 0; i < questions.size(); i++) {
            String propName = "题目" + (i + 1) + "的学生答案";
            step3Required.add(propName);
            step3Properties.set(propName, stringSchema);
        }
        step3.set("required", step3Required);
        step3.set("properties", step3Properties);
        // 第四步根据正确答案判断学生答案是否正确
        JSONObject step4 = new JSONObject();
        String step4Name = "第四步根据正确答案判断学生答案是否正确";
        step4.set("type", "object");
        step4.set("additionalProperties", false);
        JSONObject step4Properties = new JSONObject();
        JSONArray step4Required = new JSONArray();
        for (int i = 0; i < questions.size(); i++) {
            String propName = "题目" + (i + 1);
            step4Required.add(propName);
            JSONObject step4InSchema = JSONUtil.parseObj(promptsRedisService.getDocCorrectResponseFormatSchema5(record.getModelRequestId()));
            step4Properties.set(propName, step4InSchema);
        }
        step4.set("required", step4Required);
        step4.set("properties", step4Properties);
        // 第五步具体批改
        JSONObject step5 = new JSONObject();
        String step5Name = "第五步具体批改";
        step5.set("type", "object");
        step5.set("additionalProperties", false);
        JSONObject step5SchemaProps = new JSONObject();
        JSONArray step5RequiredProps = new JSONArray();
        JSONObject schemaPropObj = JSONUtil.parseObj(promptsRedisService.getDocCorrectResponseFormatSchema2(record.getModelRequestId()));
        for (int i = 0; i < questions.size(); i++) {
            String propName = "题目" + (i + 1);
            step5RequiredProps.add(propName);
            step5SchemaProps.set(propName, schemaPropObj);
        }
        step5.set("properties", step5SchemaProps);
        step5.set("required", step5RequiredProps);
        // 合体
        JSONArray requiredProps = new JSONArray();
//        requiredProps.add(step1Name);
//        requiredProps.add(step2Name);
//        requiredProps.add(step3Name);
//        requiredProps.add(step4Name);
        requiredProps.add(step5Name);
        JSONObject schemaProps = new JSONObject();
//        schemaProps.set(step1Name, step1);
//        schemaProps.set(step2Name, step2);
//        schemaProps.set(step3Name, step3);
//        schemaProps.set(step4Name, step4);
        schemaProps.set(step5Name, step5);
        JSONUtil.putByPath(responseFormat, "json_schema.schema.properties", schemaProps);
        JSONUtil.putByPath(responseFormat, "json_schema.schema.required", requiredProps);

        completion.setMessages(messages);
        completion.setResponseFormat(responseFormat);
        return completion;
    }

    @Override
    public ChatCompletionDTO createWriteQsCompletion1Request(DocCorrectRecordDTO record,
                                                             JSONObject areaObj,
                                                             JSONObject areaRes,
                                                             Integer allScore,
                                                             String ocrContent,
                                                             Integer modelRequestId) {
        String aimodel = record.getTask().getAimodel();
        JSONArray questions = areaObj.getJSONArray("questions");
        ChatCompletionDTO completion = new ChatCompletionDTO();
        completion.setModel(aimodel);
        if (aimodel.equals(AIModelConsts.GPT_4O_20240806)) {
            completion.setMaxTokens(AIModelMaxTokenConsts.GPT_4O_20240806);
        } else {
            completion.setMaxTokens(DefaultCorrectConfigsConsts.maxTokens);
        }
        List<MessageDTO> messages = new ArrayList<MessageDTO>();
        String sysMsg = null;
        if (StrUtil.isNotBlank(ocrContent)) {
            sysMsg = String.format(promptsRedisService.getDocCorrectWriteQsSystemPrompt(modelRequestId) + "\n ocr的识别结果：%s", allScore, ocrContent);
        } else {
            sysMsg = String.format(promptsRedisService.getDocCorrectWriteQsSystemPrompt(modelRequestId), allScore);
        }
        setSystemMessage(messages, aimodel, sysMsg);
        // 图片
        String areaImg = areaObj.getStr("areaImg");
        String areaImgUrl = String.format("%s%s", serverUrl, areaImg);
        MessageDTO message = createImgMessage(aimodel, "user", "", true, areaImgUrl);
        messages.add(message);
        // 结构化输出
        JSONObject responseFormat = JSONUtil.parseObj(promptsRedisService.getDocCorrectWriteQsResponseFormat(modelRequestId));
        completion.setMessages(messages);
        completion.setResponseFormat(responseFormat);
        return completion;
    }

    @Override
    public ChatCompletionDTO createWriteQsCompletion2Request(DocCorrectRecordDTO record,
                                                             JSONObject areaObj,
                                                             String essay,
                                                             String gradeName,
                                                             String title,
                                                             String apiCorrectResult,
                                                             Integer allScore,
                                                             Boolean isEnglishEssay,
                                                             Integer modelRequestId) {
        String aimodel = record.getTask().getAimodel();
        JSONArray questions = areaObj.getJSONArray("questions");
        ChatCompletionDTO completion = new ChatCompletionDTO();
        completion.setModel(aimodel);
        if (aimodel.equals(AIModelConsts.GPT_4O_20240806)) {
            completion.setMaxTokens(AIModelMaxTokenConsts.GPT_4O_20240806);
        } else {
            completion.setMaxTokens(DefaultCorrectConfigsConsts.maxTokens);
        }
        List<MessageDTO> messages2 = new ArrayList<MessageDTO>();
        String sysMsg = String.format(promptsRedisService.getDocCorrectWriteQsSystemPrompt2(modelRequestId), apiCorrectResult, isEnglishEssay ? "英语" : "汉语", gradeName, title, allScore, essay);
        setSystemMessage(messages2, aimodel, sysMsg);
        completion.setMessages(messages2);
        JSONObject responseFormat2 = JSONUtil.parseObj(promptsRedisService.getDocCorrectWriteQsResponseFormat2(modelRequestId));
        completion.setResponseFormat(responseFormat2);
        return completion;
    }

    @Override
    public ChatCompletionDTO createScoreCompletion(DocCorrectRecordDTO record,
                                                   JSONObject areaObj,
                                                   JSONObject areaRes,
                                                   String ocrContent) {
        String aimodel = record.getTask().getAimodel();
        JSONArray questions = areaObj.getJSONArray("questions");
        ChatCompletionDTO completion = new ChatCompletionDTO();
        completion.setModel(aimodel);
        completion.setMaxTokens(4096);

        List<MessageDTO> messages = new ArrayList<MessageDTO>();
        // 系统提示词
        String sysMsg = promptsRedisService.getDocCorrectScoreSystemPrompt(record.getModelRequestId());
        if (StrUtil.isNotBlank(ocrContent)) {
            sysMsg = String.format(sysMsg + "\n 请参考ocr的识别结果：%s", ocrContent);
        }
        setSystemMessage(messages, aimodel, sysMsg);
        // 图片
        String areaImg = areaObj.getStr("areaImg");
        String areaImgUrl = String.format("%s%s", serverUrl, areaImg);
        MessageDTO message = createImgMessage(aimodel, "user", "", true, areaImgUrl);
        messages.add(message);
        // 结构化输出
        JSONObject responseFormat = JSONUtil.parseObj(promptsRedisService.getDocCorrectScoreResponseFormat());

        completion.setMessages(messages);
        completion.setResponseFormat(responseFormat);
        return completion;
    }

    @Override
    public ChatCompletionDTO createEssayAnalyticalReportCompletion(List<DocCorrectRecordDTO> records, DocCorrectRecordDTO record, String scoreSituation, String gradeName) {
        String aimodel = record.getTask().getAimodel();
        ChatCompletionDTO completion = new ChatCompletionDTO();
        completion.setModel(aimodel);
        completion.setMaxTokens(4096);

        List<MessageDTO> messages = new ArrayList<MessageDTO>();
        String sysMsg = promptsRedisService.getDocCorrectEssayReportPrompt();
        // 删除多余的减少token消耗
        records.forEach(item -> {
            item.setReviewed(null);
        });
        setSystemMessage(messages, aimodel, String.format(sysMsg, scoreSituation, gradeName, JSONUtil.toJsonStr(records)));
        MessageDTO message = createImgMessage(aimodel, "user", "", true, null);
        messages.add(message);
        JSONObject responseFormat = JSONUtil.parseObj(promptsRedisService.getDocCorrectEssayReportResponseFormat());
        completion.setMessages(messages);
        completion.setResponseFormat(responseFormat);
        return completion;
    }

    @Override
    public ChatCompletionDTO createExtractStudentName(String aimodel,
                                                      String areaImgUrl,
                                                      String ocrContent,
                                                      Integer modelRequestId) {
        ChatCompletionDTO completion = new ChatCompletionDTO();
        completion.setModel(aimodel);
        completion.setMaxTokens(4096);

        List<MessageDTO> messages = new ArrayList<MessageDTO>();
        String sysMsg = promptsRedisService.getDocCorrectExtractStudentNamePrompt(modelRequestId);
        if (StrUtil.isNotBlank(ocrContent)) {
            sysMsg = String.format(sysMsg + "\n 请参考ocr的识别结果：%s", ocrContent);
        }
        setSystemMessage(messages, aimodel, sysMsg);
        MessageDTO message = createImgMessage(aimodel, "user", "", true, areaImgUrl);
        messages.add(message);
        JSONObject responseFormat = JSONUtil.parseObj(promptsRedisService.getDocCorrectExtractStudentNameFormat(modelRequestId));
        completion.setMessages(messages);
        completion.setResponseFormat(responseFormat);
        return completion;
    }

    @Override
    public ChatCompletionDTO createExtractStudentNumber(String aimodel,
                                                        String areaImgUrl,
                                                        String ocrContent,
                                                        Integer modelRequestId) {
        ChatCompletionDTO completion = new ChatCompletionDTO();
        completion.setModel(aimodel);
        completion.setMaxTokens(4096);

        List<MessageDTO> messages = new ArrayList<MessageDTO>();
        String sysMsg = promptsRedisService.getDocCorrectExtractStudentNumberPrompt(modelRequestId);
        if (StrUtil.isNotBlank(ocrContent)) {
            sysMsg = String.format(sysMsg + "\n 请参考ocr的识别结果：%s", ocrContent);
        }
        setSystemMessage(messages, aimodel, sysMsg);
        MessageDTO message = createImgMessage(aimodel, "user", "", true, areaImgUrl);
        messages.add(message);
        JSONObject responseFormat = JSONUtil.parseObj(promptsRedisService.getDocCorrectExtractStudentNumberFormat(modelRequestId));
        completion.setMessages(messages);
        completion.setResponseFormat(responseFormat);
        return completion;
    }


    @Override
    public ChatCompletionDTO createExtractPaperTopic(String aimodel, List<String> imgUrls, List<PaperTopicDTO> topics) {
        ChatCompletionDTO completion = new ChatCompletionDTO();
        completion.setModel(aimodel);
        completion.setMaxTokens(4096);

        List<MessageDTO> messages = new ArrayList<MessageDTO>();
        String sysMsg = promptsRedisService.getExtractPaperTopicPrompt();
        sysMsg = String.format(sysMsg, getOtherTopics(topics));

        setSystemMessage(messages, aimodel, sysMsg);
        // 图片信息
        for (String areaImgUrl : imgUrls) {
            MessageDTO message = createImgMessage(aimodel, "user", "", true, areaImgUrl);
            messages.add(message);
        }

        JSONObject responseFormat = JSONUtil.parseObj(promptsRedisService.getExtractPaperTopicJsonStructure());
        completion.setMessages(messages);
        completion.setResponseFormat(responseFormat);
        return completion;
    }

}
