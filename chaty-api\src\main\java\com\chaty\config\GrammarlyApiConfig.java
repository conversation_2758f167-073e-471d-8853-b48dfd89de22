package com.chaty.config;

import com.chaty.api.grammarly.GrammarlyApi;
import feign.Feign;
import feign.Logger;
import feign.Request;
import feign.RequestInterceptor;
import feign.codec.Encoder;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import feign.slf4j.Slf4jLogger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Configuration
public class GrammarlyApiConfig {

    @Value("${grammarly.api.url}")
    public String grammarlyApiUrl;
    @Value("${grammarly.api.key}")
    public String grammarlyApiKey;

    @Bean
    public GrammarlyApi grammarlyApi() {
        return Feign.builder()
                .options(new Request.Options(10, TimeUnit.SECONDS, 180, TimeUnit.SECONDS, true))
                .encoder(new JacksonEncoder())
                .decoder(new JacksonDecoder())
                .requestInterceptor(requestInterceptor())
                .logLevel(Logger.Level.HEADERS)
                .logger(new Slf4jLogger(GrammarlyApi.class))
                .target(GrammarlyApi.class, grammarlyApiUrl);
    }

    /**
     * 请求拦截器，用于每个请求添加认证头
     *
     * @return 请求拦截器
     */
    public RequestInterceptor requestInterceptor() {
        return template -> {
            // 添加 Authorization 头部
            template.header("Authorization", "Bearer " + grammarlyApiKey);
            // 你可以在这里加入其他头部信息
        };
    }

}

