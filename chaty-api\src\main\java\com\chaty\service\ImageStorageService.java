package com.chaty.service;



import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

import com.volcengine.tos.comm.common.ACLType;
import com.volcengine.tos.model.object.ObjectMetaRequestOptions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.volcengine.tos.TOSV2;
import com.volcengine.tos.TOSV2ClientBuilder;
import com.volcengine.tos.TosClientException;
import com.volcengine.tos.TosServerException;
import com.volcengine.tos.model.object.PutObjectInput;
import com.volcengine.tos.model.object.PutObjectOutput;

@Service
@Slf4j
public class ImageStorageService {

    private final TOSV2 tos;
    private final String bucket;

    public ImageStorageService(TOSV2 tos,
                               @Value("${volcengine.tos.bucket}") String bucket) {
        this.tos = tos;
        this.bucket = bucket;
    }

    /**
     * 按官方普通上传（putObject）实现，把前端 MultipartFile 上传到 TOS。
     *
     * @param file 前端传来的图片
     * @param key  存储在桶中的对象路径，比如 "images/uuid.jpg"
     * @return PutObjectOutput 包含 etag / crc64 等
     * @throws IOException 上传失败（包括底层流异常或 SDK 抛出）
     */
    public PutObjectOutput uploadImage(MultipartFile file, String key) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("file is empty");
        }

        try (InputStream contentStream = file.getInputStream()) {
            PutObjectInput putObjectInput = new PutObjectInput()
                    .setBucket(bucket)
                    .setKey(key)
                    .setContent(contentStream)
                    .setContentLength(file.getSize());

            PutObjectOutput output = tos.putObject(putObjectInput);
            log.info("Image uploaded successfully. Bucket: output {}", output);
            // 成功的话可以从 output 拿 etag / crc64
            return output;
        } catch (TosClientException e) {
            // 请求未真正发出（参数、构建等客户端错误）
            throw new IOException("TOS client error during upload: " + e.getMessage(), e);
        } catch (TosServerException e) {
            // 服务端返回错误，包含状态码/请求 ID
            String err = String.format("TOS server error. StatusCode=%d, Code=%s, Message=%s, RequestID=%s",
                    e.getStatusCode(), e.getCode(), e.getMessage(), e.getRequestID());
            throw new IOException(err, e);
        } catch (Throwable t) {
            // 兜底
            throw new IOException("Unexpected error during upload: " + t.getMessage(), t);
        }
    }

    /**
     * 直接从本地文件路径上传到 TOS，不依赖 MultipartFile。
     *
     * @param localPath 本地文件绝对路径
     * @param key       对象存储 key
     */
    public PutObjectOutput uploadImageByLocalPath(String localPath, String key) throws IOException {
        File file = new File(localPath);
        if (!file.exists() || !file.isFile()) {
            throw new IllegalArgumentException("local file not found: " + localPath);
        }
        String fileName = key + ".png";
        try (InputStream fis = new FileInputStream(file)) {
            ObjectMetaRequestOptions options = new ObjectMetaRequestOptions();
            options.setAclType(ACLType.ACL_PUBLIC_READ);
            PutObjectInput putObjectInput = new PutObjectInput()
                    .setBucket(bucket)
                    .setKey(fileName)
                    .setContent(fis)
                    .setOptions(options)
                    .setContentLength(file.length());


            PutObjectOutput output = tos.putObject(putObjectInput);
            log.info("Local image uploaded successfully. key={} etag={} crc64={}", key, output.getEtag(), output.getHashCrc64ecma());
            return output;
        } catch (TosClientException e) {
            throw new IOException("TOS client error during local upload: " + e.getMessage(), e);
        } catch (TosServerException e) {
            String err = String.format("TOS server error during local upload. StatusCode=%d, Code=%s, Message=%s, RequestID=%s",
                    e.getStatusCode(), e.getCode(), e.getMessage(), e.getRequestID());
            throw new IOException(err, e);
        } catch (Throwable t) {
            throw new IOException("Unexpected error during local upload: " + t.getMessage(), t);
        }
    }
}
