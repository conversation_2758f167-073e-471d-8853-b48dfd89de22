package com.chaty.service;

import java.io.File;
import java.util.List;

import com.chaty.dto.RemoteFileDTO;
import com.chaty.dto.RemoteFileWithRemarkDTO;
import com.chaty.dto.SaveRemoteFileDTO;

public interface RemoteFileService {
    
    List<RemoteFileDTO> list(String path, String type);

    List<RemoteFileWithRemarkDTO> listWithRemark(String path, String type);

    void upload(String path, File file);

    void save2Remote(SaveRemoteFileDTO params);

    boolean createFolder(String path, String folderName);
}
