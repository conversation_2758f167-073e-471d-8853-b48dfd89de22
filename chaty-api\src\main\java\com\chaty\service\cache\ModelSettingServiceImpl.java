package com.chaty.service.cache;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.dto.ModelSettingDTO;
import com.chaty.entity.ModelSetting;
import com.chaty.exception.BaseException;
import com.chaty.mapper.ModelRequestMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class ModelSettingServiceImpl extends ServiceImpl<ModelRequestMapper, ModelSetting>
        implements ModelSettingService {

    private static final String KEY_PREFIX = "MR:ID:";     // 单条缓存：MR:ID:{id}
    private static final String KEY_ALL_IDS = "MR:ALL_IDS"; // 维护全量 id 的集合（可选）

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    // ====== 启动时全量刷新到 Redis ======
    @PostConstruct
    public void initCache() {
        refreshAllToRedis();
    }

    // ====== Redis helpers ======
    private String k(Integer id) {
        return KEY_PREFIX + id;
    }

    private void writeToRedis(ModelSetting e) {
        if (e == null || e.getId() == null) return;
        String json = JSONUtil.toJsonStr(e);
        stringRedisTemplate.opsForValue().set(k(e.getId()), json);
        stringRedisTemplate.opsForSet().add(KEY_ALL_IDS, String.valueOf(e.getId()));
    }

    private void deleteFromRedis(Integer id) {
        if (id == null) return;
        stringRedisTemplate.delete(k(id));
        stringRedisTemplate.opsForSet().remove(KEY_ALL_IDS, String.valueOf(id));
    }

    private ModelSetting readFromRedis(Integer id) {
        if (id == null) return null;
        try {
            String json = stringRedisTemplate.opsForValue().get(k(id));
            if (StrUtil.isBlank(json)) return null;
            return JSONUtil.toBean(json, ModelSetting.class);
        } catch (Exception ignore) {
            return null;
        }
    }

    private void refreshAllToRedis() {
        List<ModelSetting> all = this.list(); // DB 全量
        // 先清空 ID 集合，逐条重建
        stringRedisTemplate.delete(KEY_ALL_IDS);
        if (all != null) {
            for (ModelSetting e : all) {
                writeToRedis(e);
            }
        }
    }

    // ====== 业务实现 ======

    @Override
    public ModelSetting addModelRequest(ModelSettingDTO modelRequestDTO) {
        ModelSetting modelSetting = new ModelSetting();
        BeanUtils.copyProperties(modelRequestDTO, modelSetting);
        boolean ok = save(modelSetting);
        if (ok) {
            // 回写生成的 id
            modelRequestDTO.setId(modelSetting.getId());
            // 同步 Redis
            writeToRedis(modelSetting);
        }
        return modelSetting;
    }

    @Override
    public boolean deleteModelRequest(Integer id) {
        boolean ok = removeById(id);
        // 同步 Redis
        deleteFromRedis(id);
        return ok;
    }

    @Override
    public ModelSetting updateModelRequest(ModelSettingDTO modelRequestDTO) {
        ModelSetting modelSetting = new ModelSetting();
        BeanUtils.copyProperties(modelRequestDTO, modelSetting);
        boolean ok = updateById(modelSetting);
        // 用 DB 最新数据覆盖缓存，避免局部字段丢失
        ModelSetting fresh = getById(modelSetting.getId());
        if (fresh != null) writeToRedis(fresh);
        return modelSetting;
    }

    @Override
    public IPage<ModelSettingDTO> selectPage(ModelSettingDTO param) {
        LambdaQueryWrapper<ModelSetting> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(param.getId()), ModelSetting::getId, param.getId());
        queryWrapper.like(StrUtil.isNotBlank(param.getName()), ModelSetting::getName, param.getName());
        queryWrapper.like(StrUtil.isNotBlank(param.getContent()), ModelSetting::getContent, param.getContent());
        queryWrapper.like(StrUtil.isNotBlank(param.getRemark()), ModelSetting::getRemark, param.getRemark());
        queryWrapper.eq(Objects.nonNull(param.getCreateTime()), ModelSetting::getCreateTime, param.getCreateTime());
        queryWrapper.eq(Objects.nonNull(param.getUpdateTime()), ModelSetting::getUpdateTime, param.getUpdateTime());
        queryWrapper.orderByDesc(ModelSetting::getWeight);
        queryWrapper.orderByDesc(ModelSetting::getCreateTime);

        if (param.getPage() == null) {
            throw new BaseException("Pagination parameters are required");
        }

        IPage<ModelSetting> page = page(param.getPage().page(ModelSetting.class), queryWrapper);
        return page.convert(entity -> {
            ModelSettingDTO dto = new ModelSettingDTO();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        });
    }

    @Override
    public int getMaxEmptyWeight() {
        LambdaQueryWrapper<ModelSetting> wrapper = Wrappers.<ModelSetting>lambdaQuery()
                .select(ModelSetting::getWeight)
                .orderByDesc(ModelSetting::getWeight)
                .last("LIMIT 1");
        ModelSetting top = getOne(wrapper);
        Integer maxWeight = (top != null && top.getWeight() != null) ? top.getWeight() : 0;
        return maxWeight + 1;
    }

    /** 优先 Redis，未命中回源 DB 并回填 Redis */
    @Override
    public ModelSetting getByIdCache(Integer id) {
        if (id == null) return null;
        // 1) Redis
        ModelSetting cached = readFromRedis(id);
        if (cached != null) return cached;

        // 2) DB 并回填
        ModelSetting db = getById(id);
        if (db != null) writeToRedis(db);
        return db;
    }
}
