package com.chaty.util;


import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.beans.PropertyDescriptor;
import java.util.HashSet;
import java.util.Set;

public class MyStringUtils {

    public static String replaceChineseChar2EnglishCharInPrompt(String str) {
        if (str == null) {
            return "";
        }
        str = str.replace("（", "(");  // 中文左括号替换为英文左括号
        str = str.replace("）", ")");  // 中文右括号替换为英文右括号
        str = str.replace("，", ",");  // 中文逗号替换为英文逗号
        str = str.replace("。", ".");  // 中文句号替换为英文句号
        str = str.replace("：", ":");  // 中文冒号替换为英文冒号
        str = str.replace("；", ";");  // 中文分号替换为英文分号
        str = str.replace("‘", "'");  // 中文单引号（左）替换为英文单引号
        str = str.replace("’", "'");  // 中文单引号（右）替换为英文单引号
        str = str.replace("“", "\""); // 中文双引号（左）替换为英文双引号
        str = str.replace("”", "\""); // 中文双引号（右）替换为英文双引号
        str = str.replace("、", ",");  // 中文顿号替换为英文逗号
        return str;
    }

    public static String replaceBracketContent(String input, String replacement) {
        // 使用正则表达式匹配括号及其中的内容
        return input.replaceAll("\\(.*?\\)", "(" + replacement + ")");
    }

    public static String[] getNullPropertyNames(Object source) {
        BeanWrapper src = new BeanWrapperImpl(source);
        PropertyDescriptor[] pds = src.getPropertyDescriptors();
        Set<String> emptyNames = new HashSet<>();
        for (PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null ) {
                emptyNames.add(pd.getName());
            }
        }
        return emptyNames.toArray(new String[0]);
    }
}
