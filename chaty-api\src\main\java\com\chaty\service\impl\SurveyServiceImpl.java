package com.chaty.service.impl;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.dto.SurveyDTO;
import com.chaty.entity.Survey;
import com.chaty.mapper.SurveyMapper;
import com.chaty.service.SurveyService;

@Service
public class SurveyServiceImpl extends ServiceImpl<SurveyMapper, Survey> implements SurveyService {

    @Override
    public IPage<Survey> getPage(SurveyDTO params) {
        LambdaQueryWrapper<Survey> wrapper = Wrappers.lambdaQuery(Survey.class)
                .orderByDesc(Survey::getCreateTime);
        return baseMapper.selectPage(params.getPage().page(Survey.class), wrapper);
    }

}
