package com.chaty.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.common.BaseResponse;
import com.chaty.entity.TestSetImageEntity;
import com.chaty.service.TestSetImageService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/** 测试集图片信息 CRUD（仅DB，不走Redis） */
@RestController
@RequestMapping("/api/testsetImage")
public class TestSetImageController {

    @Resource
    private TestSetImageService service;

    /** 分页 */
    @GetMapping("/page")
    public BaseResponse<IPage<TestSetImageEntity>> page(
            @RequestParam(required = false) Integer pageNumber,
            @RequestParam(required = false) Integer pageSize,
            @RequestParam(required = false) Long testSetId,
            @RequestParam(required = false) String questionType,
            @RequestParam(required = false) String fromRecordId
    ) {
        return BaseResponse.ok(service.page(pageNumber, pageSize, testSetId, questionType, fromRecordId));
    }

    /** 详情 */
    @GetMapping("/{id}")
    public BaseResponse<TestSetImageEntity> get(@PathVariable Long id) {
        return BaseResponse.ok(service.getById(id));
    }

    /** 新增 */
    @PostMapping("/add")
    public BaseResponse<Long> add(@RequestBody TestSetImageEntity param) {
        return BaseResponse.ok(service.add(param));
    }

    /** 更新 */
    @PostMapping("/update")
    public BaseResponse<Long> update(@RequestBody TestSetImageEntity param) {
        return BaseResponse.ok(service.updateOne(param));
    }

    /** 删除 */
    @DeleteMapping("/{id}")
    public BaseResponse<?> delete(@PathVariable Long id) {
        service.deleteById(id);
        return BaseResponse.ok("删除成功");
    }
}
