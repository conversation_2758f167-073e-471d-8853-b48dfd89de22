package com.chaty.service;

import com.chaty.api.lianke.LiankeConfig;
import com.chaty.dto.PrinterPropsDTO;

import java.util.List;

public interface PrinterService {

    /**
     * 远程调用打印机
     */
    String print(PrinterPropsDTO props);

    /**
     * 远程调用打印机
     */
    String printByLink(PrinterPropsDTO props);

    /**
     * 获取打印机列表
     * @return
     */
    List<LiankeConfig.Device> getDevices();

    List<LiankeConfig.Device> refreshPinterDevices();

    void changePinterDevicesName(String deviceId, String newName);

    void refreshPinterConfig(String deviceId);
}
