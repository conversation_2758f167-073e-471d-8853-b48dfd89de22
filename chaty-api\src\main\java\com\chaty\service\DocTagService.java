package com.chaty.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.dto.DocTagDTO;
import com.chaty.entity.DocTag;

public interface DocTagService extends IService<DocTag> {

    void addTag(DocTagDTO params);

    void deleteTag(DocTagDTO params);
    
    Map<String, List<DocTagDTO>> listByDocs(DocTagDTO params);

    List<DocTag> list(DocTagDTO params);

    void setTag(DocTagDTO params);

    void deleteDocTag(DocTagDTO params);
    
}
