package com.chaty.util;

import cn.hutool.core.util.IdUtil;
import com.itextpdf.io.image.ImageData;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.pdf.*;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Image;
import org.springframework.beans.factory.annotation.Value;

import java.io.File;
import java.io.IOException;
import java.util.Base64;
import java.util.List;

public class MultiImageToPDF {


    public static void main(String[] args) {
        // 这里放置你的 Base64 图片字符串数组
        String[] base64Images = {
                "你的Base64图片字符串1",
                "你的Base64图片字符串2",
                "你的Base64图片字符串3"
        };

        String outputPdfPath = "output.pdf"; // 输出的 PDF 文件名

        convertMultipleBase64ToPDF(base64Images, outputPdfPath);
    }

    /**
     * 将多个 Base64 编码的图片转换为一个 PDF，每张图片占一页
     *
     * @param base64Images  Base64 编码的图片数组
     * @param outputPdfPath 输出的 PDF 文件路径
     */
    public static void convertMultipleBase64ToPDF(String[] base64Images, String outputPdfPath) {
        try {
            // 创建 PDF 文档
            PdfWriter writer = new PdfWriter(outputPdfPath);
            PdfDocument pdfDocument = new PdfDocument(writer);
            Document document = new Document(pdfDocument);

            for (String base64Image : base64Images) {
                if (base64Image == null || base64Image.trim().isEmpty()) {
                    continue; // 忽略空的 Base64 图片
                }

                // 解码 Base64 字符串为字节数组
                byte[] imageBytes = Base64.getDecoder().decode(base64Image);

                // 创建 Image 对象
                ImageData imageData = ImageDataFactory.create(imageBytes);
                Image image = new Image(imageData);

                // 创建新页面
                pdfDocument.addNewPage();

                // 设置图片大小，使其适应页面
                image.scaleToFit(pdfDocument.getDefaultPageSize().getWidth() - 40,
                        pdfDocument.getDefaultPageSize().getHeight() - 40);

                // 居中显示图片
                image.setFixedPosition(20, (pdfDocument.getDefaultPageSize().getHeight() - image.getImageHeight()) / 2);

                // 添加图片到 PDF
                document.add(image);
            }

            // 关闭文档
            document.close();

            System.out.println("PDF 生成成功: " + outputPdfPath);
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("PDF 生成失败");
        }
    }

    /**
     * 将多张图片合并为 PDF，每张图片占一页
     *
     * @param imageBytesList 图片的字节数组列表
     * @param path           ctxPath 文件路径
     */
    public static String generatePdfFromImages(List<byte[]> imageBytesList, String path) throws IOException {
        String filename = String.format("%s.%s", IdUtil.fastSimpleUUID(), "pdf");
        String outputPdfPath = path + File.separator + filename;
        PdfWriter writer = new PdfWriter(outputPdfPath);
        PdfDocument pdfDocument = new PdfDocument(writer);
        Document document = new Document(pdfDocument);

        for (byte[] imageBytes : imageBytesList) {
            ImageData imageData = ImageDataFactory.create(imageBytes);
            Image image = new Image(imageData);

            // 添加新页面
            pdfDocument.addNewPage();

            image.scaleToFit(pdfDocument.getDefaultPageSize().getWidth(), pdfDocument.getDefaultPageSize().getHeight());

            image.setFixedPosition(0, 0); // 图片从最上面开始，x=0, y=0

            document.add(image);
        }

        document.close();
        return filename;
    }
}
