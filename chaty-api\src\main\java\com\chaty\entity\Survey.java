package com.chaty.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

@Data
public class Survey {

    private String id;
    private String name;
    private String phone;
    private String email;
    private String school;
    private String subject;
    private String grade;
    private String scenarios;
    private String features;
    private String comments;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    @TableLogic
    private Boolean deleted;
    
}
