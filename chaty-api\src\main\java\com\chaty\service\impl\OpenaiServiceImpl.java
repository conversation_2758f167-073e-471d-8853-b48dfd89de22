package com.chaty.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.chaty.entity.GptAskLogEntity;
import com.chaty.exception.RetryException;
import org.springframework.http.MediaType;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import com.chaty.api.openai.Message;
import com.chaty.api.openai.OpenaiApi;
import com.chaty.dto.ChatCompletionDTO;
import com.chaty.dto.FunctionDTO;
import com.chaty.enums.AIModelConsts;
import com.chaty.enums.CompletionEnums;
import com.chaty.exception.BaseException;
import com.chaty.service.BasicAiService;
import com.chaty.service.OCRService;
import com.chaty.service.OpenaiService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import static com.chaty.util.ModelRequestUtil.mergeModelRequest;

@Slf4j
@Service("openaiService")
public class OpenaiServiceImpl implements OpenaiService, BasicAiService, OCRService {

    @Resource
    private OpenaiApi openaiApi;
    @Resource
    private WebClient openaiWebClient;
    @Resource
    private GptAskLogServiceImpl gptAskLogService;

    private Map<String, Object> models = MapUtil
            .builder(new HashMap<String, Object>())
            .put("gpt-4", "gpt-4")
            .put("gpt-3.5-turbo", "gpt-3.5-turbo")
            .put("gpt-4-turbo", "gpt-4-1106-preview")
            .put("gpt-4-vision-preview", "gpt-4-turbo-2024-04-09")
            .put("gpt-4o", "gpt-4o")
            .put("gpt-4o-mini", "gpt-4o-mini")
            .put("gpt-4o-2024-08-06", "gpt-4o-2024-08-06")
            .put("gemini-1.5-pro", "gemini-1.5-pro")
            .put("gemini-1.5-flash", "gemini-1.5-flash")
            .put("gemini-2.0-flash-exp", "gemini-2.0-flash-exp")
            .build();

    @Override
    public List<Message> completionForMessage(String model, List<Message> messages, float temperature) {
        Map<String, Object> params = new HashMap<>();
        params.put("model", models.get(model));
        params.put("temperature", temperature);
        params.put("messages", messages);

        Map<String, Object> res = openaiApi.chatCompletionsV1(params);
        return JSONUtil.parseObj(res).getJSONArray("choices").stream()
                .map(item -> {
                    return JSONUtil.parseObj(item).getBean("message", Message.class);
                }).collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> chatForCompletion(ChatCompletionDTO param, GptAskLogEntity gptAskLogEntity) {
        Map<String, Object> apiParam = convert2CompletionParam(param);
        // 记录问答开始时间
        gptAskLogEntity.setStartTime(System.currentTimeMillis());
        Map<String, Object> resp = openaiApi.chatCompletionsV1(apiParam);
        // 异常处理
        resp.put("$response", JSONUtil.getByPath(JSONUtil.parseObj(resp), "choices[0].message.content"));
        resp.put("$function_call", JSONUtil.getByPath(JSONUtil.parseObj(resp), "choices[0].message.function_call"));
        resp.put("$logprobs", JSONUtil.getByPath(JSONUtil.parseObj(resp), "choices[0].logprobs.content"));

        gptAskLogService.add(BasicAiService.getGptAskLogEntityInAsk(apiParam, resp, gptAskLogEntity));
        return resp;
    }

    @Override
    public Map<String, Object> getFinalCompletion(ChatCompletionDTO param) {
        return convert2CompletionParam(param);
    }

    @Override
    public Boolean isSupport(String model) {
        return models.containsKey(model);
    }

    @Override
    public Flux<String> streamCompletetion(ChatCompletionDTO param) {
        Map<String, Object> apiParam = convert2CompletionParam(param);
        apiParam.put("stream", true);
        return openaiWebClient.post()
                .uri("/v1/chat/completions")
                .contentType(MediaType.APPLICATION_JSON)
                .body(Mono.just(apiParam), Map.class)
                .retrieve()
                .bodyToFlux(String.class)
                .map(s -> {
                    // log.info("request openai chat completion, received steam data: {}", s);
                    if (JSONUtil.isTypeJSONObject(s)) {
                        JSONObject parsed = JSONUtil.parseObj(s);
                        String content = JSONUtil.getByPath(parsed, "choices[0].delta.content", "");
                        parsed.set("$content", content);
                        return parsed.toString();
                    } else if (Objects.equals(s, "[DONE]")) {
                        Map<String, Object> res = new HashMap<>();
                        res.put("$end", true);
                        res.put("$content", "");
                        return JSONUtil.toJsonStr(res);
                    } else {
                        throw new RuntimeException("unexpected response");
                    }
                });
    }

    private Map<String, Object> convert2CompletionParam(ChatCompletionDTO param) {
        Map<String, Object> apiParam = new HashMap<>();
        apiParam.put("model", models.get(param.getModel()));
        apiParam.put("messages", param.getMessages());
        if (Objects.nonNull(param.getLogprobs())) {
            apiParam.put("logprobs", param.getLogprobs());
        }
        if (Objects.nonNull(param.getTopp())) {
            apiParam.put("top_p", param.getTopp());
        }
        if (Objects.nonNull(param.getTemperature())) {
            apiParam.put("temperature", param.getTemperature());
        }
        if (Objects.nonNull(param.getFunctions())) {
            apiParam.put("functions", parseFunctions(param.getFunctions()));
        }
        if (Objects.nonNull(param.getMaxTokens())) {
            apiParam.put("max_tokens", param.getMaxTokens());
        }
        if (Objects.nonNull(param.getResponseFormat())) {
            apiParam.put("response_format", param.getResponseFormat());
        }
        if (Objects.nonNull(param.getSeed())) {
            apiParam.put("seed", param.getSeed());
        }


        if (Objects.nonNull(param.getModelRequestObj())) {
            apiParam = mergeModelRequest(apiParam, param);
        }
        return apiParam;
    }

    public static Object parseFunctions(List<FunctionDTO> functions) {
        return functions.stream().map(func -> BeanUtil.beanToMap(func)).collect(Collectors.toList());
    }

    @Override
    public String ocrForText(String url) {
        Map<String, Object> apiParam = convert2CompletionParam(
                CompletionEnums.AI_OCR.getVisionCompletion(AIModelConsts.GPT_4O_20240806, url));
        Map<String, Object> resp = openaiApi.chatCompletionsV1(apiParam);
        Object res = JSONUtil.getByPath(JSONUtil.parseObj(resp), "choices[0].message.content");

        log.info("open ai ocr result: {}", res);
        if (Objects.isNull(res)) {
            throw new BaseException("OCR识别失败");
        }
        return (String) res;
    }

    @Retryable(value = RetryException.class, backoff = @Backoff(delay = 1000))
    @Override
    public String ocrForHandwritingText(String base64) {
        return ocrForText(base64);
    }


    @Retryable(value = RetryException.class, backoff = @Backoff(delay = 1000))
    @Override
    public String ocrForArithmetic(String base64) throws InterruptedException {
        return "";
    }
}
