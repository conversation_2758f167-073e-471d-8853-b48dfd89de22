<#assign docInfo = configs[0].getDocInfo()>

\documentclass[a4paper]{article}
\usepackage[paperwidth=${docInfo.width}mm, paperheight=${docInfo.height}mm]{geometry}

\usepackage[absolute, overlay]{textpos}
\usepackage{CJKutf8}
\usepackage{fancyhdr}
\usepackage{pifont}
\usepackage{color}
\usepackage{graphicx}
\usepackage{pdfpages}
\usepackage{pbox}
\pagestyle{fancy}

\fancyhf{}
\renewcommand{\headrulewidth}{0pt}

\setlength{\TPHorizModule}{1mm}
\setlength{\TPVertModule}{1mm}
\DeclareUnicodeCharacter{0964}{}



\begin{document}

<#function latexEscape str>
    <#return str?replace("#", "\\#")?replace("$", "\\$")?replace("%", "\\%")?replace("&", "\\&")?replace("_", "+")?replace("{", "\\{")?replace("}", "\\}")?replace("~", "\\~")?replace("^", "\\^")?replace("\\", "\\textbackslash")?replace("[", "\\texttt{[}")?replace("]", "\\texttt{]}")>
</#function>

<#assign qsStatsIdx = 0>
<#assign stats = qsStats.stats>

<#list configs as config>

<#assign areas = config.getAreasObj()>
<#assign configObj = config.getConfigObj()>
<#assign flagSize = configObj.flagSize>
<#assign scoreArea = configObj.scoreArea>
<#assign totalNum = grading.totalNum>
<#assign fixedNum = grading.fixedNum>
<#assign fixedRate = 100>
<#if fixedNum != 0>
<#assign fixedRate = (totalNum - fixedNum) / totalNum * 100>
</#if>
<#assign areax = scoreArea.x!0>
<#assign areay = scoreArea.y!0>
<#assign scoreColor = configObj.scoreColor!'red'>
<#if scoreColor == 'byScore'>
    <#assign scoreColor = (correctRate gte 60)?string('green','red')>
</#if>

<#if config_index == 0>

\begin{textblock}{1000}(${areax / 300 * 25.4}, ${areay / 300 * 25.4})
    \fontsize{${fontSize!flagSize}}{10pt} \selectfont
    \begin{CJK*}{UTF8}{gbsn}
        \textcolor{${scoreColor}}{\textbf{${fixedRate?string("#.##")}\%}}
    \end{CJK*}
\end{textblock}

</#if>

<#list areas as area>
<#if !(area.enabled?? && area.enabled == false)>
<#assign questions = area.questions>

<#list questions as qs>
<#assign flagArea = qs.flagArea>
<#assign num = stats[qsStatsIdx].num>
<#assign total = stats[qsStatsIdx].total>
<#assign rate = 100>
<#if num != 0>
<#assign rate = (num - total) / num * 100>
</#if>

\begin{textblock}{1000}(${flagArea.x / 300 * 25.4}, ${flagArea.y / 300 * 25.4})
\fontsize{10pt}{100pt} \selectfont
\begin{CJK*}{UTF8}{gbsn}

    \textcolor{${(rate gte 60)?string('green','red')}}{\fontsize{${fontSize!flagSize}}{40pt}\selectfont \textbf{${rate?string("#.##")}\%}}

\end{CJK*}
\end{textblock}

<#assign qsStatsIdx = qsStatsIdx + 1>
</#list>

</#if>
</#list>

% 开启预览
\includepdf[pages=-, frame=true, scale=1, pagecommand={}]{${config.docurl?split("/")[2]}}

</#list>

\end{document}

