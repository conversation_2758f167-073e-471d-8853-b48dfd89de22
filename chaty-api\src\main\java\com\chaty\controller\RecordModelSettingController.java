package com.chaty.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.common.BaseResponse;
import com.chaty.dto.RecordModelSettingDTO;
import com.chaty.entity.RecordModelSettingEntity;
import com.chaty.service.cache.ModelSettingService;
import com.chaty.service.cache.RecordModelSettingService;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/** 题型-模型映射 CRUD */
@RestController
@RequestMapping("/api/recordModelSetting")
public class RecordModelSettingController {

    @Resource
    private RecordModelSettingService service;

    @Resource
    private ModelSettingService modelSettingService;

    /** 分页查询；支持 type/fileId/configPackageId/questionType 过滤 */
    @GetMapping("/page")
    public BaseResponse<IPage<RecordModelSettingDTO>> page(
            @RequestParam(required = false) Integer pageNumber,
            @RequestParam(required = false) Integer pageSize,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String fileId,
            @RequestParam(required = false) String configPackageId,
            @RequestParam(required = false) Long testSetId,
            @RequestParam(required = false) String questionType
    ) {
        IPage<RecordModelSettingEntity> page = service.page(
                pageNumber, pageSize,
                type, fileId, configPackageId,testSetId,
                questionType
        );

        IPage<RecordModelSettingDTO> pageRes = page.convert(item -> {
            RecordModelSettingDTO dto = new RecordModelSettingDTO();
            BeanUtils.copyProperties(item, dto);
            dto.setModelSetting(modelSettingService.getById(item.getModelSettingId()));
            return dto;
        });
        return BaseResponse.ok(pageRes);
    }

    /** 新增 */
    @PostMapping("/add")
    public BaseResponse<Long> add(@RequestBody RecordModelSettingEntity param) {
        return BaseResponse.ok(service.add(param));
    }

    /** 更新 */
    @PostMapping("/update")
    public BaseResponse<Long> update(@RequestBody RecordModelSettingEntity param) {
        return BaseResponse.ok(service.updateOne(param));
    }

    /** 删除 */
    @DeleteMapping("/{id}")
    public BaseResponse<?> delete(@PathVariable Long id) {
        service.deleteById(id);
        return BaseResponse.ok("删除成功");
    }
}
