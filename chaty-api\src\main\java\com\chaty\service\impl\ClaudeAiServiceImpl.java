package com.chaty.service.impl;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;

import com.chaty.entity.GptAskLogEntity;
import org.apache.ibatis.javassist.bytecode.ByteArray;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import com.chaty.api.claude.ClaudeApi;
import com.chaty.dto.ChatCompletionDTO;
import com.chaty.service.BasicAiService;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import static com.chaty.util.ModelRequestUtil.mergeModelRequest;

@Slf4j
@Service
public class ClaudeAiServiceImpl implements BasicAiService {

    @Resource
    private ClaudeApi claudeApi;
    @Resource
    private WebClient claudeaiWebClient;
    @Resource
    private GptAskLogServiceImpl gptAskLogService;

    private Map<String, Object> models = MapUtil
            .builder(new HashMap<String, Object>())
            .put("Claude 3 Opus", "claude-3-opus-20240229")
            .put("Claude 3 Sonnet", "claude-3-sonnet-20240229")
            // .put("Claude 3 Haiku", "gpt-4-1106-preview")
            .build();

    @Override
    public Map<String, Object> chatForCompletion(ChatCompletionDTO param, GptAskLogEntity gptAskLogEntity) {
        Map<String, Object> apiParam = convert2CompletionParam(param);
        Map<String, Object> resp = claudeApi.chatCompletionsV1(apiParam);
        // 记录问答开始时间
        gptAskLogEntity.setStartTime(System.currentTimeMillis());
        // 异常处理
        resp.put("$response", JSONUtil.getByPath(JSONUtil.parseObj(resp), "content[0].text"));
        // resp.put("$function_call", JSONUtil.getByPath(JSONUtil.parseObj(resp), "choices[0].message.function_call"));

        gptAskLogService.add(BasicAiService.getGptAskLogEntityInAsk(apiParam, resp, gptAskLogEntity));
        return resp;
    }

    @Override
    public Map<String, Object> getFinalCompletion(ChatCompletionDTO param) {
        return convert2CompletionParam(param);
    }

    @Override
    public Flux<String> streamCompletetion(ChatCompletionDTO param) {
        Map<String, Object> apiParam = convert2CompletionParam(param);
        apiParam.put("stream", true);
        return claudeaiWebClient.post()
                .uri("/v1/messages")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(apiParam)
                .retrieve()
                .onStatus(HttpStatus::is4xxClientError, clientResponse -> {
                    clientResponse.bodyToMono(String.class).subscribe(body -> {
                        log.error("Error body: {}", body);
                    });
                    log.error("request claude ai chat completion failed, {}", clientResponse);
                    return Mono.error(new RuntimeException("request claude ai chat completion failed"));
                })
                .bodyToFlux(String.class)
                .map(s -> {
                    // log.info("request claude ai chat completion, received steam data: {}", s);
                    if (JSONUtil.isTypeJSONObject(s)) {
                        JSONObject parsed = JSONUtil.parseObj(s);
                        String content = "";
                        String type = parsed.getStr("type");
                        if (Objects.equals(type, "content_block_delta")) {
                            content = JSONUtil.getByPath(parsed, "delta.text", "");
                        }
                        if (Objects.equals(type, "error")) {
                            log.error("request claude ai chat completion failed, {}", parsed);
                            throw new RuntimeException(parsed.getByPath("error.message", String.class));
                        }
                        if (Objects.equals(type, "message_stop")) {
                            parsed.set("$end", true);
                        }
                        parsed.set("$content", content);
                        return JSONUtil.toJsonStr(parsed);
                    } else {
                        throw new RuntimeException("unexpected response");
                    }
                });
    }

    private Map<String, Object> convert2CompletionParam(ChatCompletionDTO param) {
        Map<String, Object> apiParam = new HashMap<>();
        apiParam.put("model", models.get(param.getModel()));
        apiParam.put("temperature", param.getTemperature());
        apiParam.put("max_tokens", Optional.ofNullable(param.getMaxTokens()).orElse(4096));
        apiParam.put("stream", param.getStream());
        Map<String, Object> finalApiParam = apiParam;
        Optional.ofNullable(param.getTopp()).ifPresent(topp -> finalApiParam.put("top_p", topp));
        Optional.ofNullable(param.getTopk()).ifPresent(topk -> finalApiParam.put("top_k", topk.intValue()));
        apiParam.put("messages", param.getMessages());

        if (Objects.nonNull(param.getModelRequestObj())) {
            apiParam = mergeModelRequest(apiParam, param);
        }
        return apiParam;
    }

    @Override
    public Boolean isSupport(String model) {
        return models.containsKey(model);
    }

}