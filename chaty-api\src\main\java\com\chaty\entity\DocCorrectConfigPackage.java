package com.chaty.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

@TableName("doc_correct_config_package")
@Data
public class DocCorrectConfigPackage {

    @TableId
    private String id;

    private String name;

    @TableLogic
    private Integer deleted;

    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NEVER)
    private String creator;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private String config;

    private String tenantId;

    private String remark;

    private Double width;

    private Double height;

    private String topic;

    /**
     * 是否完成：0-未完成，1-已完成
     */
    private Integer isFinish;

    private String firstImageUrl;
}

