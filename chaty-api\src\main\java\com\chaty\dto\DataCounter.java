package com.chaty.dto;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.ReentrantLock;

import cn.hutool.json.JSONObject;
import lombok.Data;

/**
 * 数据计数器类，用于统计和计算一系列数据的累计值、平均值等统计信息
 * 可以管理多个数据统计组，每组都有自己的统计数据
 */
public class DataCounter {

    /**
     * 数据统计组列表，存储多个统计单元
     */
    private final List<DataStats> stats;

    /**
     * 当前操作的统计组索引
     */
    private int index;

    /**
     * 所有数据的总和
     */
    private BigDecimal total;

    /**
     * 添加一个可重入锁，用于保护数据操作
     */
    private final ReentrantLock lock = new ReentrantLock();

    /**
     * 构造函数，初始化数据计数器
     */
    public DataCounter() {
        this.stats = new ArrayList<>();
        this.index = 0;
        this.total = BigDecimal.ZERO;
    }

    /**
     * 批改失败的总数
     */
    public void countFailOne() {
        BigDecimal data = BigDecimal.ONE;
        try {
            lock.lock();
            // 获取当前索引对应的统计组
            DataStats dataStats = getStats();
            // 该组计数加1
            dataStats.num++;
            // 该组批改失败的数量加1
            dataStats.failCount++;
            // 累加数据值到该组总和
            dataStats.total = dataStats.total.add(data);
            // 重新计算该组平均值
            dataStats.avg = dataStats.total.divide(BigDecimal.valueOf(dataStats.num), BigDecimal.ROUND_HALF_UP);
            // 将数据添加到该组的数据列表
            dataStats.datas.add(data);
            // 总数值增加 - 使用读写锁保护
            total = total.add(data);
            // 索引加1，准备操作下一组
            index++;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 计数值为1，常用于简单计数场景
     */
    public void countOne() {
        count(BigDecimal.ONE);
    }

    /**
     * 计数值为0，可用于记录零值数据点
     */
    public void countZero() {
        count(BigDecimal.ZERO);
    }

    /**
     * 添加一个指定值的计数
     * 
     * @param data 要计数的数值
     */
    public void count(BigDecimal data) {
        try {
            lock.lock();
            // 获取当前索引对应的统计组
            DataStats dataStats = getStats();
            // 该组计数加1
            dataStats.num++;
            // 累加数据值到该组总和
            dataStats.total = dataStats.total.add(data);
            // 重新计算该组平均值
            dataStats.avg = dataStats.total.divide(BigDecimal.valueOf(dataStats.num), BigDecimal.ROUND_HALF_UP);
            // 将数据添加到该组的数据列表
            dataStats.datas.add(data);
            // 总数值增加 - 使用读写锁保护
            total = total.add(data);
            // 索引加1，准备操作下一组
            index++;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 重置索引值为0
     * 用于重新开始一轮统计或切换到第一个统计组
     */
    public void resetIndex() {
        try {
            lock.lock();
            index = 0;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取当前索引对应的统计组
     * 如果索引超出现有统计组范围，则创建新的统计组
     */
    public DataStats getStats() {
        if (index < stats.size()) {
            return stats.get(index);
        } else {
            DataStats dataStats = new DataStats();
            stats.add(dataStats);
            return dataStats;
        }
    }

    /**
     * 获取所有数据的总和
     * 
     * @return 总和值
     */
    public BigDecimal getTotal() {
        return total;
    }

    /**
     * 获取统计组的数量
     * 
     * @return 统计组数量
     */
    public int getNum() {
        return stats.size();
    }

    /**
     * 获取所有数据的平均值
     * 
     * @return 平均值
     */
    public BigDecimal getAvg() {
        return total.divide(BigDecimal.valueOf(getNum()), BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 将统计信息转换为字符串表示
     */
    public String toStr() {
        JSONObject obj = new JSONObject();
        obj.set("total", total);
        obj.set("num", getNum());
        obj.set("avg", getAvg());
        obj.set("stats", stats);
        return obj.toString();
    }

    /**
     * 数据统计单元类，用于存储单个统计组的数据
     */
    @Data
    public static class DataStats {
        /**
         * 统计组中的数据数量
         */
        private int num;

        /**
         * 统计组中的数据总和
         */
        private BigDecimal total;

        /**
         * 统计组中的数据平均值
         */
        private BigDecimal avg;

        /**
         * 统计组中的所有数据列表
         * 因为在同步块内使用，所以不需要线程安全集合
         */
        private List<BigDecimal> datas;

        /**
         * 批改失败的数量，不计入总数量
         */
        private Integer failCount;

        /**
         * 构造函数，初始化统计单元
         */
        public DataStats() {
            this.num = 0;
            this.total = BigDecimal.ZERO;
            this.avg = BigDecimal.ZERO;
            this.datas = new ArrayList<>();
            this.failCount = 0;
        }
    }
}