package com.chaty.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.checkerframework.checker.units.qual.N;

import java.io.Serializable;

@TableName("user")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class User implements Serializable {
    private static final long serialVersionUID = 1L;

    private String id;

    private String username;

    private String email;

    private String nickname;

    private String password;

    private Integer status;

    private Integer role;

    private Integer deleted;

    @TableField("class_id")
    private String classId;

    // 学号
    @TableField("student_id")
    private String studentId;

    /**
     * 默认学校
     */
    @TableField("default_school")
    private String defaultSchool;
}
