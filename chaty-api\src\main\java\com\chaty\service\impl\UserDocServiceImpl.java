package com.chaty.service.impl;

import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.dto.DocCorrectRecordDTO;
import com.chaty.dto.DocCorrectTaskDTO;
import com.chaty.dto.UserDocDTO;
import com.chaty.entity.DocCorrectRecord;
import com.chaty.entity.DocCorrectTask;
import com.chaty.entity.User;
import com.chaty.entity.UserDoc;
import com.chaty.enums.UserRoleConsts;
import com.chaty.exception.BaseException;
import com.chaty.mapper.DocCorrectRecordMapper;
import com.chaty.mapper.DocCorrectTaskMapper;
import com.chaty.mapper.UserDocMapper;
import com.chaty.mapper.UserMapper;
import com.chaty.security.AuthUtil;
import com.chaty.service.DocCorrectRecordService;
import com.chaty.service.UserDocService;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;

@Service
public class UserDocServiceImpl extends ServiceImpl<UserDocMapper, UserDoc> implements UserDocService {

    @Resource
    private UserDocMapper userDocMapper;
    @Resource
    private DocCorrectTaskMapper docCorrectTaskMapper;
    @Resource
    private DocCorrectRecordMapper docCorrectRecordMapper;
    @Resource
    private DocCorrectRecordService docCorrectRecordService;
    @Resource
    private UserMapper userMapper;

    @Transactional
    @Override
    public void assignedDocs(UserDocDTO params) {
        String userId = params.getUserId();
        String[] userIds = userId.split(",");
        List<UserDoc> userDocs = params.getUserDocs();
        List<String> docIds = new ArrayList<>();
        List<UserDoc> addUserDocs = new ArrayList<>();

        for (UserDoc userDoc : userDocs) {
            if (Objects.isNull(userDoc.getDocId())) {
                // 查询并分配整份试卷
                docCorrectRecordMapper.selectList(Wrappers.lambdaQuery(DocCorrectRecord.class)
                        .eq(DocCorrectRecord::getTaskId, userDoc.getTaskId())
                        .eq(DocCorrectRecord::getDeleted, false))
                        .forEach(record -> {
                            docIds.add(record.getId());
                            List<UserDoc> add = Arrays.stream(userIds).map(uid -> {
                                UserDoc ud = new UserDoc();
                                ud.setUserId(uid);
                                ud.setDocId(record.getId());
                                ud.setTaskId(userDoc.getTaskId());
                                return ud;
                            }).collect(Collectors.toList());
                            addUserDocs.addAll(add);
                        });
            } else {
                docIds.add(userDoc.getDocId());
                List<UserDoc> add = Arrays.stream(userIds).map(uid -> {
                    UserDoc ud = new UserDoc();
                    ud.setUserId(uid);
                    ud.setDocId(userDoc.getDocId());
                    ud.setTaskId(userDoc.getTaskId());
                    return ud;
                }).collect(Collectors.toList());
                addUserDocs.addAll(add);
            }
        }
        // 删除用户已有的重新添加
        userDocMapper.delete(Wrappers.lambdaUpdate(UserDoc.class)
                .in(UserDoc::getUserId, userIds)
                .in(UserDoc::getDocId, docIds));
        this.saveBatch(addUserDocs);
    }

    @Override
    public IPage<DocCorrectRecordDTO> getUserDoc(DocCorrectRecordDTO param) {
        User loginUser = AuthUtil.getLoginUser();
        QueryWrapper<DocCorrectRecord> queryWrapper = Wrappers.<DocCorrectRecord>query()
                .eq(!Objects.equals(loginUser.getRole(), UserRoleConsts.ADMIN), "ud.user_id", loginUser.getId())
                .eq(Objects.nonNull(param.getTaskId()), "dcr.task_id", param.getTaskId())
                .eq("dct.status", 3)
                .like(StringUtils.hasText(param.getDocname()), "dcr.docname", param.getDocname())
                .orderByAsc("dcr.docname");
        if (Objects.nonNull(param.getTagIds())) {
            queryWrapper.inSql("dcr.id", 
                "select dtr.doc_id from doc_tag_record dtr where dtr.deleted = 0 and dtr.doc_id is not null and dtr.tag_id in ('" + ArrayUtil.join(param.getTagIds(), "','") + "') group by dtr.doc_id having count(distinct dtr.tag_id) = " + param.getTagIds().length);
        }
        return docCorrectRecordMapper.pageByUser(param.getPage().page(), queryWrapper);
    }

    @Override
    public IPage<DocCorrectTaskDTO> getUserTask(DocCorrectTaskDTO param) {
        User loginUser = AuthUtil.getLoginUser();
        QueryWrapper<DocCorrectTask> queryWrapper = Wrappers.<DocCorrectTask>query()
                .like(StrUtil.isNotBlank(param.getName()), "dct.name", param.getName())
                .eq(Objects.nonNull(param.getStatus()), "dct.status", param.getStatus())
                .eq("dct.deleted", false)
                .orderByAsc("dct.status")
                .orderByDesc("dct.create_time");
        if (Objects.nonNull(param.getTagIds())) {
            queryWrapper.inSql("dct.id", 
                    "select dtr.task_id from doc_tag_record dtr where dtr.deleted = 0 and dtr.task_id is not null and dtr.tag_id in ('" + ArrayUtil.join(param.getTagIds(), "','") + "') group by dtr.task_id having count(distinct dtr.tag_id) = " + param.getTagIds().length);
        }
        if (Objects.equals(loginUser.getRole(), UserRoleConsts.ADMIN)) {
            return docCorrectTaskMapper.page(param.getPage().page(), queryWrapper);
        } else {
            return docCorrectTaskMapper.pageByUser(param.getPage().page(), loginUser.getId(), queryWrapper);
        }
    }

    @Override
    public List<String> getDocIds(String userId, String taskId) {
        return userDocMapper.selectList(Wrappers.lambdaQuery(UserDoc.class)
            .eq(UserDoc::getUserId, userId)
            .eq(UserDoc::getTaskId, taskId))
            .stream().map(UserDoc::getDocId).collect(Collectors.toList());
    }

    @Override
    public List<String> getTaskIds(String userId) {
        return userDocMapper.selectList(Wrappers.lambdaQuery(UserDoc.class)
            .eq(UserDoc::getUserId, userId).groupBy(UserDoc::getTaskId))
            .stream().map(UserDoc::getTaskId).collect(Collectors.toList());
    }

    @Override
    public void deleteUserDocs(UserDocDTO params) {
        User loginUser = AuthUtil.getLoginUser();   
        userDocMapper.delete(Wrappers.lambdaQuery(UserDoc.class)
            .eq(UserDoc::getUserId, loginUser.getId())
            .nested(i -> i
                .in(Objects.nonNull(params.getTaskIds()), UserDoc::getTaskId, params.getTaskIds())
                .or()
                .in(Objects.nonNull(params.getDocIds()), UserDoc::getDocId, params.getDocIds())));
    }

    @Transactional
    @Override
    public void autoAssign(UserDocDTO params) {
        String taskId = params.getTaskId();
        if (Objects.isNull(taskId)) {
            throw new BaseException("taskId不能为空");
        }
        // 查询试卷
        List<DocCorrectRecord> records = docCorrectRecordService.selectByTaskId(taskId, null);
        List<String> userNames = records.stream()
                .map(DocCorrectRecord::getIdentify)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (userNames.isEmpty()) {
            return;
        }
        Map<String, User> userMap = userMapper.selectList(Wrappers
                .lambdaQuery(User.class)
                .eq(User::getDeleted, 0)
                .in(User::getUsername, userNames))
                .stream()
                .collect(Collectors.toMap(User::getUsername, u -> u));
        // 分配试卷
        List<UserDoc> addUserDocs = new ArrayList<>();
        records.forEach(record -> {
            if (Objects.isNull(record.getIdentify())) {
                return;
            }
            User user = userMap.get(record.getIdentify());
            if (Objects.isNull(user)) {
                return;
            }
            // 删除已分配的试卷
            userDocMapper.delete(Wrappers.lambdaQuery(UserDoc.class)
                .eq(UserDoc::getUserId, user.getId())
                .eq(UserDoc::getDocId, record.getId()));
            // 分配记录
            UserDoc ud = new UserDoc();
            ud.setUserId(user.getId());
            ud.setDocId(record.getId());
            ud.setTaskId(taskId);
            addUserDocs.add(ud);
        });
        // 新增
        if (!addUserDocs.isEmpty()) {
            this.saveBatch(addUserDocs);
        }
    }

}
