package com.chaty.util;

import com.chaty.dto.EssayWordDTO;
import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.html2pdf.attach.impl.layout.HtmlPageBreak;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.IBlockElement;
import com.itextpdf.layout.element.IElement;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;

@Slf4j
public class HtmlTemplateProcessor {

    public static void processHtmlTemplate(String templateName, EssayWordDTO data, String htmlOutPath) {
        try {
//            // 读取HTML模板文件
//            String templatePath = "templates/" + templateName;
//            InputStream templateStream = HtmlTemplateProcessor.class.getClassLoader().getResourceAsStream(templatePath);
//            if (templateStream == null) {
//                throw new FileNotFoundException("Template file not found: " + templatePath);
//            }
//
//            // 使用 BufferedReader 读取输入流
//            StringBuilder htmlContent = new StringBuilder();
//            try (BufferedReader reader = new BufferedReader(new InputStreamReader(templateStream, StandardCharsets.UTF_8))) {
//                String line;
//                while ((line = reader.readLine()) != null) {
//                    htmlContent.append(line).append("\n");
//                }
//            }
//            String htmlTemplate = htmlContent.toString();
            String htmlTemplate = loadTemplate("/root/service/chaty/tmp/essayReport.html");
            // 替换HTML中的占位符
            String filledHtml = replacePlaceholders(htmlTemplate, data);

            // 将替换后的HTML内容写入新的文件
            try (FileWriter writer = new FileWriter(htmlOutPath)) {
                writer.write(filledHtml);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }

        } catch (IOException e) {
            log.error("Error processing HTML template", e);
        }
    }

    public static String loadTemplate(String templatePath) throws IOException {
        // 使用 FileInputStream 从指定路径读取模板文件
        File templateFile = new File(templatePath);
        if (!templateFile.exists()) {
            throw new FileNotFoundException("Template file not found: " + templatePath);
        }

        // 使用 BufferedReader 读取文件内容
        StringBuilder htmlContent = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(templateFile), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                htmlContent.append(line).append("\n");
            }
        }

        return htmlContent.toString();
    }

    public static String replacePlaceholders(String template, EssayWordDTO data) {
        // 替换HTML模板中的占位符为实际数据
        String result = template;

        result = result.replace("{{gradeName}}", data.getGradeName());
        result = result.replace("{{studentName}}", data.getStudentName());
        result = result.replace("{{grade}}", String.valueOf(data.getGrade()));
        result = result.replace("{{essayTitle}}", data.getEssayTitle());
        result = result.replace("{{name}}", data.getName());
        result = result.replace("{{fullText}}", data.getFullText());
        result = result.replace("{{structuralAnalysis}}", data.getStructuralAnalysis());
        result = result.replace("{{spellingErrors}}", data.getSpellingErrors());
        result = result.replace("{{syntaxError}}", data.getSyntaxError());
        result = result.replace("{{improperUseOfWords}}", data.getImproperUseOfWords());
        result = result.replace("{{writingTips}}", data.getWritingTips());
        result = result.replace("{{improvementGrade}}", String.valueOf(data.getImprovementGrade()));
        result = result.replace("{{essayImprovement}}", data.getEssayImprovement());

        return result;
    }

    public static void convertHtmlToPdf(String htmlPath, String pdfOutPath) {
        try {
            // 设置 PDF 文件输出流
            PdfWriter writer = new PdfWriter(pdfOutPath);

            ConverterProperties props = new ConverterProperties();

            // 设置 PDF 文档并设置为横向布局
            List<IElement> elements = HtmlConverter.convertToElements(new FileInputStream(htmlPath), props);
            PdfDocument pdf = new PdfDocument(new PdfWriter(pdfOutPath));
            Document document = new Document(pdf, PageSize.A4.rotate(), false);
            for (IElement element : elements) {
                // 分页符
                if (element instanceof HtmlPageBreak) {
                    document.add((HtmlPageBreak) element);

                    //普通块级元素
                } else {
                    document.add((IBlockElement) element);
                }
            }
            document.close();
            HtmlConverter.convertToPdf(new FileInputStream(htmlPath), pdf);

            // 关闭文档
            document.close();

        } catch (Exception e) {
            log.error("Error converting HTML to PDF", e);
        }
    }
}
