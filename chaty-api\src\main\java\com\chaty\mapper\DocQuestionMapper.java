package com.chaty.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.chaty.entity.DocQuestion;

@Mapper
public interface DocQuestionMapper {

    List<DocQuestion> list(DocQuestion param);

    @Insert("INSERT INTO doc_question (id, question, correct_answer, height) VALUES (#{id}, #{question}, #{correctAnswer}, #{height})")
    void insertOne(DocQuestion entity);

    void updateById(DocQuestion entity);

    @Update("update doc_question set deleted = 1 where id = #{id}")
    void deleteById(@Param("id") Integer id);

    @Select("select * from doc_question where id = #{id}")
    DocQuestion selectById(@Param("id") Integer id);

}
