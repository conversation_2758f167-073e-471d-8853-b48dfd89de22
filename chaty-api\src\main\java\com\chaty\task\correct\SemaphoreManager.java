package com.chaty.task.correct;

import com.google.common.util.concurrent.RateLimiter;

import java.util.concurrent.Semaphore;

public class SemaphoreManager {
    private static volatile SemaphoreManager instance;

    private final Semaphore baiduGlobalSemaphore;
    private final RateLimiter tencentHandWriteenGlobalRateLimiter;
    private final Semaphore tencentArithmeticGlobalSemaphore;

    private final RateLimiter baiduHandWrittenrateLimiter;
    private final RateLimiter aliHandWrittenrateLimiter;
    private final RateLimiter aliHandNormalLimiter;

    private SemaphoreManager() {
        this.baiduGlobalSemaphore = new Semaphore(10);
        this.tencentHandWriteenGlobalRateLimiter = RateLimiter.create(9.0) ;
        this.tencentArithmeticGlobalSemaphore = new Semaphore(10);
        this.baiduHandWrittenrateLimiter = RateLimiter.create(9.0);
        this.aliHandWrittenrateLimiter = RateLimiter.create(9.0);
        this.aliHandNormalLimiter = RateLimiter.create(9.0);
    }

    public static SemaphoreManager getInstance() {
        if (instance == null) {
            synchronized (SemaphoreManager.class) {
                if (instance == null) {
                    instance = new SemaphoreManager();
                }
            }
        }
        return instance;
    }

    public RateLimiter getBaiduHandWrittenrateLimiter() {
        return baiduHandWrittenrateLimiter;
    }

    public Semaphore getBaiduGlobalSemaphore() {
        return baiduGlobalSemaphore;
    }

    public Semaphore getTencentArithmeticGlobalSemaphore() {
        return tencentArithmeticGlobalSemaphore;
    }

    public RateLimiter getTencentHandWriteenGlobalRateLimiter() {
        return tencentHandWriteenGlobalRateLimiter;
    }

    public RateLimiter getAliHandWrittenrateLimiter() {return aliHandWrittenrateLimiter;}

    public RateLimiter getAliHandNormalLimiter() {return aliHandNormalLimiter;}
}
