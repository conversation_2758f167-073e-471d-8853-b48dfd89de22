package com.chaty.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Data;

@Data
@TableName("doc_correct_file")
public class DocCorrectFile {
    @TableId(type = IdType.AUTO)
    private String id;

    private String name;

    private String url;

    private Integer status;

    @TableLogic
    private Integer deleted;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private String creator;

    private String modelValue;

    private String tenantId;

    private Integer isEssay;

    private String classId;

    private Integer isCorrectFinish;

    private String remark;

    private Date lastCorrectTime;

    private Date finishCorrectTime;

    private String configPackageId;

    private Integer recordSize;

    private Boolean exportCompleted;

    private String exportFolderPath;

    private String templateFileId;

    private Double averageTimeConsumption;

    private Long totalTimeConsumption;

    private Long totalCount;

    private Long nullCount;

    private Integer modelRequestId;
}
