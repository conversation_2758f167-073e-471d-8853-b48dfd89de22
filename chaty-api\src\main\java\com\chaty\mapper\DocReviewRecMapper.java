package com.chaty.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chaty.entity.DocReviewRec;


@Mapper
public interface DocReviewRecMapper {

    List<DocReviewRec> list(DocReviewRec param);

    @Insert("INSERT INTO doc_review_rec (id, doc_review_id, library_id) VALUES (#{id}, #{docReviewId}, #{libraryId})")
    void insertOne(DocReviewRec entity);

    void updateById(DocReviewRec entity);

    @Update("update doc_review_rec set deleted = 1 where id = #{id}")
    void deleteById(@Param("id") String id);

    @Select("select * from doc_review_rec where id = #{id}")
    DocReviewRec selectById(@Param("id") String id);

    Page<DocReviewRec> selectPage(IPage<?> page, DocReviewRec params);  

}
