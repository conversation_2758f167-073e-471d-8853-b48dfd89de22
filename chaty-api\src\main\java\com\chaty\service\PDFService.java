package com.chaty.service;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.chaty.dto.EssayWordDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.chaty.exception.BaseException;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RuntimeUtil;

public interface PDFService {

    interface TexCmd {
        String XELATEX = "xelatex";
        String PDFLATEX = "pdflatex";
        String LATEXMK = "latexmk";
    }

    static Logger log = LoggerFactory.getLogger(PDFService.class);

    Map<String, Object> createPDF(Map<String, Object> params);

    Map<String, Object> solveAllPDF(Map<String, Object> params);

    Map<String, Object> coursenotePDF(Map<String, Object> params);

    Map<String, Object> docReviewPDF(Map<String, Object> params);

    Map<String, Object> reviewStatsPDF(Map<String, Object> params);

    void writeWords2PDF(List<String> paths, String outPath);

    default String writePDF(String cmd, String texContent, Map<String, Object> properties) {
        String filePath = MapUtil.getStr(properties, "filePath");
        String texPath = MapUtil.getStr(properties, "texPath");
        String fileCtxPath = MapUtil.getStr(properties, "fileCtxPath");
        String texEnvPath = MapUtil.getStr(properties, "texEnvPath");

        try {
            String filename = IdUtil.fastSimpleUUID();
            String texFilePath = String.format("%s" + File.separator + "%s.tex", texPath, filename);
            File execEnv = FileUtil.file(texEnvPath);
            FileUtil.writeBytes(texContent.getBytes(StandardCharsets.UTF_8), texFilePath);
            Process process = RuntimeUtil.exec(null, execEnv, cmd, "-interaction=nonstopmode", "-output-directory", filePath, texFilePath);
//            Process process = RuntimeUtil.exec( "D:\\latex\\miktex\\bin\\x64\\pdflatex.exe", "-interaction=nonstopmode", "-output-directory", filePath, texFilePath);
            String pdfres = RuntimeUtil.getResult(process);
            log.debug("PDF生成日志：{}", pdfres);
            return String.format("%s/%s.pdf", fileCtxPath, filename);
        } catch (Exception e) {
            throw new BaseException("PDF文件生成失败!", e);
        }
    }

    default String writePDF(String texContent, Map<String, Object> properties) {
        return writePDF(TexCmd.XELATEX, texContent, properties);
    }

    Map<String, Object> createDoc(String texCmd, String template, Map<String, Object> params);

    Map<String, Object> createEssayReport(List<EssayWordDTO> params);

    Map<String, Object> createEssayAnalyticalReport(String content);

}
