package com.chaty.service.impl;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.GeneralHandwritingOCRRequest;
import com.tencentcloudapi.ocr.v20181119.models.GeneralHandwritingOCRResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import com.chaty.api.mathpix.MathPixApi;
import com.chaty.exception.BaseException;
import com.chaty.exception.RetryException;
import com.chaty.service.OCRService;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service("mathPixOCRService")
public class MathPixOCRServiceImpl implements OCRService {

    @Value("${api.mathpix.app-id}")
    private String appid;
    @Value("${api.mathpix.app-key}")
    private String appkey;

    @Resource
    private MathPixApi mathPixApi;

    @Retryable(value = RetryException.class, backoff = @Backoff(delay = 1000))
    @Override
    public String ocrForText(String url) {
        Map<String, Object> params = createParams(url);
        Map<String, Object> response = mathPixApi.text(appid, appkey, params);

        verifyResponse(response);

        log.info("mathpix ocr response: {}", JSONUtil.toJsonStr(response));
        return String.valueOf(response.get("text"));
    }

    private Map<String, Object> createParams(String url) {
        Map<String, Object> params = new HashMap<>();
        params.put("src", url);
        params.put("formats", Arrays.asList("text", "latex_styled"));
        return params;
    }

    private void verifyResponse(Map<String, Object> response) {
        if (response.containsKey("error_info")) {
            Map<String, Object> errorInfo = (Map<String, Object>) response.get("error_info");

            if (errorInfo.get("id").equals("image_download_error")) {
                throw new RetryException("图片下载失败");
            }

            if (errorInfo.get("id").equals("image_no_content")) {
                log.warn("图片无内容: {}", response);
                return;
            }

            String message = (String) errorInfo.get("message");
            String code = (String) errorInfo.get("code");
            log.error("图片识别失败: {}", JSONUtil.toJsonStr(response));
            throw new BaseException(String.format("图片识别失败: %s", message));
        }
    }

    @Retryable(value = RetryException.class, backoff = @Backoff(delay = 1000))
    @Override
    public String ocrForHandwritingText(String base64) {
        return "";
    }

    @Retryable(value = RetryException.class, backoff = @Backoff(delay = 1000))
    @Override
    public String ocrForArithmetic(String base64) throws InterruptedException {
        return "";
    }
}
