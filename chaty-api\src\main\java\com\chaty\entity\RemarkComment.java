package com.chaty.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

@Data
@TableName("remark_comment")
public class RemarkComment {

    /** 主键，自增 */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 评语内容 */
    private String content;

    /** 命中概率（权重） */
    private Integer hitProb;

    /** 创建时间，插入时填充 */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新时间，插入与更新时填充 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
