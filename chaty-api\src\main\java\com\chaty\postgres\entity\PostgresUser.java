package com.chaty.postgres.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * User 实体类
 */
@Data
@TableName("\"user\"")
public class PostgresUser {

    /**
     * 主键 ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * Alist 路径
     */
    @TableField("alist_path")
    private String alistPath;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 账号状态
     */
    private Integer status;

    /**
     * 角色类型
     */
    private Integer role;

    /**
     * JP 自动缩放
     */
    @TableField("jp_auto_scale")
    private Integer jpAutoScale;

    /**
     * 文件排序顺序
     */
    @TableField("file_sort")
    private Short fileSort;
}
