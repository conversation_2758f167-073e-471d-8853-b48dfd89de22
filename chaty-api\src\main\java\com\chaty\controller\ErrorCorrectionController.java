package com.chaty.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.common.BaseResponse;
import com.chaty.dto.ErrorCorrectionDTO;
import com.chaty.entity.ErrorCorrection;
import com.chaty.service.ErrorCorrectionService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/api/errorCorrection")
public class ErrorCorrectionController {

    @Resource
    private ErrorCorrectionService errorCorrectionService;

    @PostMapping("/page")
    public BaseResponse<?> page(@RequestBody ErrorCorrectionDTO param) {
        IPage<ErrorCorrection> res = errorCorrectionService.page(param);
        return BaseResponse.ok("查询成功", res);
    }

    @PostMapping("/add")
    public BaseResponse<?> add(@RequestBody ErrorCorrection param) {
        errorCorrectionService.add(param);
        return BaseResponse.ok("添加成功");
    }

    @PostMapping("/update")
    public BaseResponse<?> update(@RequestBody ErrorCorrection param) {
        errorCorrectionService.updateById(param);
        return BaseResponse.ok("修改成功");
    }

    @PostMapping("/delete")
    public BaseResponse<?> delete(Long id) {
        errorCorrectionService.deleteById(id);
        return BaseResponse.ok("删除成功");
    }
}
