package com.chaty.service.impl;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;

import com.chaty.exception.RetryException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import com.chaty.exception.BaseException;
import com.chaty.service.OCRService;
import com.chaty.util.FileUtil;

import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.RuntimeUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service("paddleOCRService")
public class PaddleOCRServiceImpl implements OCRService {

    @Value("${ocr.paddle.script}")
    public String script;

    @Resource
    private FileUtil fileUtil;

    private final String OCR_RES_REGEX = "(?<=result:\\n)[\\d\\D]*";

    @Override
    public String ocrForText(String url) {
        String path = fileUtil.url2Path(url);
        log.info("paddle ocr for text: {}", path);

        try {
            Process process = RuntimeUtil.exec("python3", script, path);
            String out = RuntimeUtil.getResult(process);
            log.debug("PaddleOCR res: {} \n{}", path, out);
            List<String> res = ReUtil.findAll(OCR_RES_REGEX, out, 0);
            if (Objects.nonNull(res) && !res.isEmpty()) {
                return res.get(0);
            }
            return "";
        } catch (Exception e) {
            log.error("PaddleOCR for text error: {}", path, e);
            throw new BaseException("paddle ocr text error", e);
        }
    }


    @Retryable(value = RetryException.class, backoff = @Backoff(delay = 1000))
    @Override
    public String ocrForHandwritingText(String base64) {
        return "";
    }

    @Retryable(value = RetryException.class, backoff = @Backoff(delay = 1000))
    @Override
    public String ocrForArithmetic(String base64) throws InterruptedException {
        return "";
    }
}
