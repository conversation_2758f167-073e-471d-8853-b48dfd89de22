package com.chaty.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.dto.DocCorrectTaskDTO;
import com.chaty.dto.DocReviewDTO;
import com.chaty.dto.ErrorCorrectionDTO;
import com.chaty.entity.DocReview;
import com.chaty.entity.ErrorCorrection;

import java.util.List;
import java.util.Map;

public interface ErrorCorrectionService {

    ErrorCorrection add(ErrorCorrection param);

    void updateById(ErrorCorrection param);


    void deleteById(Long id);

    IPage<ErrorCorrection> page(ErrorCorrectionDTO param);
}
