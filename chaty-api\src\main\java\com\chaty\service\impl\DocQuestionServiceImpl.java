package com.chaty.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.chaty.entity.DocQuestion;
import com.chaty.mapper.DocQuestionMapper;
import com.chaty.service.DocQuestionService;

@Service
public class DocQuestionServiceImpl implements DocQuestionService {

    @Resource
    private DocQuestionMapper docQuestionMapper;

    @Override
    public void add(DocQuestion param) {
        docQuestionMapper.insertOne(param);
    }

    @Override
    public void deleteById(Integer id) {
        docQuestionMapper.deleteById(id);
    }

    @Override
    public List<DocQuestion> list(DocQuestion param) {
        return docQuestionMapper.list(param);
    }

    @Override
    public DocQuestion selectById(Integer id) {
        return docQuestionMapper.selectById(id);
    }

    @Override
    public void updateById(DocQuestion param) {
        docQuestionMapper.updateById(param);
    }

}
