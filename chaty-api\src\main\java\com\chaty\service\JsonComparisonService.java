package com.chaty.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * JSON对比服务
 * 用于第二轮请求中直接对比学生答案和正确答案的JSON
 */
@Slf4j
@Service
public class JsonComparisonService {

    /**
     * 对比学生答案JSON和正确答案JSON
     *
     * @param studentAnswerJson 第一轮识别出的学生答案JSON，格式如：{"题目1": "B", "题目2": "B", "题目3": "A"}
     * @param correctAnswerJson 正确答案JSON，格式如：{"题目1": "B", "题目2": "B", "题目3": "A"}
     * @return 对比结果JSON，格式如：{"题目1": true, "题目2": true, "题目3": false}
     */
    public JSONObject compareAnswers(JSONObject studentAnswerJson, JSONObject correctAnswerJson) {
        JSONObject comparisonResult = new JSONObject();

        if (correctAnswerJson == null || correctAnswerJson.isEmpty()) {
            log.warn("正确答案JSON为空，无法进行对比");
            return comparisonResult;
        }

        // 以正确答案的字段为准进行对比
        for (String questionKey : correctAnswerJson.keySet()) {
            String correctAnswer = correctAnswerJson.getStr(questionKey);
            String studentAnswer = studentAnswerJson != null ? studentAnswerJson.getStr(questionKey) : null;

            // 对比答案值，使用特殊判断逻辑
            boolean isCorrect = compareAnswerValues(correctAnswer, studentAnswer);
            comparisonResult.set(questionKey, isCorrect);

            log.debug("题目对比 - {}: 正确答案={}, 学生答案={}, 结果={}",
                questionKey, correctAnswer, studentAnswer, isCorrect);
        }

        return comparisonResult;
    }

    /**
     * 比较答案值，包含特殊的判断逻辑
     *
     * @param correctAnswer 正确答案
     * @param studentAnswer 学生答案
     * @return 是否正确
     */
    private boolean compareAnswerValues(String correctAnswer, String studentAnswer) {
        // 如果任一为null，直接比较
        if (correctAnswer == null || studentAnswer == null) {
            return Objects.equals(correctAnswer, studentAnswer);
        }

        // 去除空格后比较
        String trimmedCorrect = correctAnswer.trim();
        String trimmedStudent = studentAnswer.trim();

        // 直接相等的情况
        if (Objects.equals(trimmedCorrect, trimmedStudent)) {
            return true;
        }

        // 特殊判断逻辑：T和√和[■][×]比较要给true
        if (isTrueAnswer(trimmedCorrect) && isTrueAnswer(trimmedStudent)) {
            return true;
        }

        // 特殊判断逻辑：F和×和[√][■]比较要给true
        if (isFalseAnswer(trimmedCorrect) && isFalseAnswer(trimmedStudent)) {
            return true;
        }

        return false;
    }

    /**
     * 判断是否为"真"类型的答案
     *
     * @param answer 答案字符串
     * @return 是否为真类型答案
     */
    private boolean isTrueAnswer(String answer) {
        if (answer == null) {
            return false;
        }
        return "T".equals(answer) || "√".equals(answer) || "[■][×]".equals(answer);
    }

    /**
     * 判断是否为"假"类型的答案
     *
     * @param answer 答案字符串
     * @return 是否为假类型答案
     */
    private boolean isFalseAnswer(String answer) {
        if (answer == null) {
            return false;
        }
        return "F".equals(answer) || "×".equals(answer) || "[√][■]".equals(answer);
    }

    /**
     * 从areaObj中提取正确答案JSON
     * 
     * @param areaObj 区域对象，包含questions数组
     * @return 正确答案JSON，格式如：{"题目1": "B", "题目2": "B", "题目3": "A"}
     */
    public JSONObject extractCorrectAnswersFromAreaObj(JSONObject areaObj) {
        JSONObject correctAnswers = new JSONObject();
        
        JSONArray questions = areaObj.getJSONArray("questions");
        if (questions == null || questions.isEmpty()) {
            log.warn("areaObj中没有找到questions数组");
            return correctAnswers;
        }
        
        for (int i = 0; i < questions.size(); i++) {
            JSONObject question = questions.getJSONObject(i);
            String questionKey = "题目" + (i + 1);
            String correctAnswer = question.getStr("answer");
            
            if (correctAnswer != null) {
                correctAnswers.set(questionKey, correctAnswer);
            }
        }
        
        log.debug("从areaObj提取的正确答案: {}", correctAnswers);
        return correctAnswers;
    }

    /**
     * 从第一轮结果中提取学生答案JSON
     * 
     * @param firstRoundResult 第一轮请求的结果
     * @return 学生答案JSON，格式如：{"题目1": "B", "题目2": "B", "题目3": "A"}
     */
    public JSONObject extractStudentAnswersFromFirstRound(JSONArray firstRoundResult) {
        JSONObject studentAnswers = new JSONObject();
        
        if (firstRoundResult == null || firstRoundResult.isEmpty()) {
            log.warn("第一轮结果为空，无法提取学生答案");
            return studentAnswers;
        }
        
        for (int i = 0; i < firstRoundResult.size(); i++) {
            JSONObject reviewedItem = firstRoundResult.getJSONObject(i);
            String questionKey = "题目" + (i + 1);
            String studentAnswer = reviewedItem.getStr("studentAnswer");
            
            if (studentAnswer != null) {
                studentAnswers.set(questionKey, studentAnswer);
            }
        }
        
        log.debug("从第一轮结果提取的学生答案: {}", studentAnswers);
        return studentAnswers;
    }

    /**
     * 将对比结果转换为标准的reviewed格式
     * 
     * @param comparisonResult 对比结果JSON
     * @param areaObj 区域对象
     * @param firstRoundResult 第一轮结果
     * @return 标准的reviewed数组
     */
    public JSONArray convertComparisonResultToReviewed(JSONObject comparisonResult, 
                                                       JSONObject areaObj, 
                                                       JSONArray firstRoundResult) {
        JSONArray reviewed = new JSONArray();
        JSONArray questions = areaObj.getJSONArray("questions");
        
        if (questions == null) {
            return reviewed;
        }
        
        for (int i = 0; i < questions.size(); i++) {
            String questionKey = "题目" + (i + 1);
            JSONObject reviewedItem = new JSONObject();
            
            // 从第一轮结果获取学生答案
            String studentAnswer = "";
            if (firstRoundResult != null && i < firstRoundResult.size()) {
                JSONObject firstRoundItem = firstRoundResult.getJSONObject(i);
                studentAnswer = firstRoundItem.getStr("studentAnswer", "");
            }
            
            // 从对比结果获取是否正确
            Boolean isCorrect = comparisonResult.getBool(questionKey, false);
            
            // 构建reviewed项
            reviewedItem.set("studentAnswer", studentAnswer);
            reviewedItem.set("isCorrect", isCorrect ? "Y" : "N");
            reviewedItem.set("review", "JSON对比结果");
            reviewedItem.set("credibility", 1.0f); // JSON对比的可信度设为1.0
            
            reviewed.add(reviewedItem);
        }
        
        log.debug("转换后的reviewed结果: {}", reviewed);
        return reviewed;
    }
}
