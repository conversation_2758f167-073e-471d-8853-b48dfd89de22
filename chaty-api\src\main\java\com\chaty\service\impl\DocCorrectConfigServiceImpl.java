package com.chaty.service.impl;

import java.util.Objects;

import javax.annotation.Resource;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.chaty.util.PDFUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.dto.DocCorrectConfigDTO;
import com.chaty.entity.DocCorrectConfig;
import com.chaty.mapper.DocCorrectConfigMapper;
import com.chaty.security.AuthUtil;
import com.chaty.service.DocCorrectConfigService;

import cn.hutool.core.util.StrUtil;

@Service
@Slf4j
public class DocCorrectConfigServiceImpl extends ServiceImpl<DocCorrectConfigMapper, DocCorrectConfig>
            implements DocCorrectConfigService {
    @Value("${file.local.path}")
    public String path;
    @Value("${file.local.ctxpath}")
    public String ctxPath;

    @Resource
    private DocCorrectConfigMapper docCorrectConfigMapper;

    @Override
    public String add(DocCorrectConfigDTO param) {
        String ctxUrl = param.getDocurl();
        if (StrUtil.isNotBlank(ctxUrl)) {
            try {
                String configStr = param.getConfig();
                JSONObject config = JSONUtil.parseObj(configStr);
                if (!config.containsKey("scoreArea") || config.get("scoreArea") == null) {
                    String filePath = ctxUrl.replace(ctxPath, path);
                    JSONObject pdfInfo = PDFUtil.getPDFInfo(filePath);
                    float height = pdfInfo.getInt("height");
                    Integer x = 50;
                    Integer y = Math.round(height / 6.0f);
                    JSONObject scoreArea = new JSONObject();
                    scoreArea.set("x", x);
                    scoreArea.set("y", y);
                    scoreArea.set("width", 10);
                    scoreArea.set("height", 10);
                    scoreArea.set("rotate", 0);
                    scoreArea.set("scaleX", 1);
                    scoreArea.set("scaleY", 1);
                    config.set("scoreArea", scoreArea);
                    param.setConfig(JSONUtil.toJsonStr(config));
                }
            }catch (Exception e) {
                log.error("设置默认分数高度失败：e: {}", e.getMessage());
            }
        }
        docCorrectConfigMapper.insert(param);
        return param.getId();
    }

    @Override
    public void delete(String id) {
        docCorrectConfigMapper.deleteById(id);
    }

    @Override
    public IPage<DocCorrectConfig> page(DocCorrectConfigDTO param) {
        LambdaQueryWrapper<DocCorrectConfig> queryWrapper = Wrappers.<DocCorrectConfig>lambdaQuery()
                .like(StrUtil.isNotBlank(param.getName()), DocCorrectConfig::getName, param.getName())
                .eq(StrUtil.isNotBlank(param.getDocType()), DocCorrectConfig::getDocType, param.getDocType())
//                .eq(DocCorrectConfig::getCreator, AuthUtil.getLoginUser().getId())
                .orderByDesc(DocCorrectConfig::getCreateTime);
        return docCorrectConfigMapper.selectPage(param.getPage().page(), queryWrapper);
    }

    @Transactional
    @Override
    public String update(DocCorrectConfigDTO param) {
        DocCorrectConfig existed = getById(param.getId());
        if (Objects.nonNull(existed)) {
            param.setId(existed.getId());
            docCorrectConfigMapper.updateById(param);
        } else {
            param.setId(null);
            return add(param);
        }
        return param.getId();
    }

    @Override
    public DocCorrectConfig getById(String id) {
        if (StrUtil.isBlank(id)) {
            return null;
        }
        return docCorrectConfigMapper.selectById(id);
    }

    public DocCorrectConfig getByName(String name) {
        return docCorrectConfigMapper.selectOne(Wrappers.<DocCorrectConfig>lambdaQuery()
                .eq(DocCorrectConfig::getDeleted, 0)
                .eq(DocCorrectConfig::getName, name)
                .orderByDesc(DocCorrectConfig::getCreateTime)
                .last("limit 1"));
    }

}
