package com.chaty.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("ftp_files")
public class FtpFiles {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    private String filename;

    private String path;

    private String modified;
    
    private Integer size;
    
    private LocalDateTime createTime;
    
    private LocalDateTime updateTime;
    
    private String remark;
    
    private String fileId;
    
    private String configPackageId;
    
    private String type;
    
    private String name;
} 