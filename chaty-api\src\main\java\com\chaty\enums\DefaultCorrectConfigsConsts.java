package com.chaty.enums;

public interface DefaultCorrectConfigsConsts {

    Float topP = 0.001F;

    Integer seed = 42;

    Integer maxTokens = 4 * 1024;

    String essayDefaultRequirement = "要求使用正确的句型，词汇准确，语法无误，段落结构清晰，内容与题目相关，并能够充分表达个人观点。";

    String essayRequirement = "字数要求：%s。内容要求：%s";


    String[] availableOcrServices = {"aliOcrService", "openaiService", "tencentOCRService", "baiduOCRService", "paddleOCRService", "mathPixOCRService"};
//    String[] availableOcrServices = { "openaiService"};
}
