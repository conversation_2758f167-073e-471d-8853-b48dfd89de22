package com.chaty.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.common.BaseResponse;
import com.chaty.dto.FtpMessageTitleDTO;
import com.chaty.service.FtpMessageTitleService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/api/ftpMessageTitle")
public class FtpMessageTitleController {

    @Resource
    private FtpMessageTitleService ftpMessageTitleService;

    @PostMapping("/add")
    public BaseResponse<?> addTitle(@RequestBody FtpMessageTitleDTO dto) {
        FtpMessageTitleDTO result = ftpMessageTitleService.addTitle(dto);
        return BaseResponse.ok(result);
    }

    @GetMapping("/delete")
    public BaseResponse<?> deleteTitle(@RequestParam Integer id) {
        ftpMessageTitleService.deleteTitle(id);
        return BaseResponse.ok("删除成功");
    }

    @PostMapping("/update")
    public BaseResponse<?> updateTitle(@RequestBody FtpMessageTitleDTO dto) {
        FtpMessageTitleDTO result = ftpMessageTitleService.updateTitle(dto);
        return BaseResponse.ok(result);
    }

    @PostMapping("/select")
    public BaseResponse<IPage<FtpMessageTitleDTO>> selectPage(@RequestBody FtpMessageTitleDTO param) {
        IPage<FtpMessageTitleDTO> page = ftpMessageTitleService.selectPageWithAuth(param);
        return BaseResponse.ok(page);
    }

    @PostMapping("/selectName")
    public BaseResponse<IPage<FtpMessageTitleDTO>> selectName(@RequestBody FtpMessageTitleDTO param) {
        IPage<FtpMessageTitleDTO> page = ftpMessageTitleService.selectPage(param);
        return BaseResponse.ok(page);
    }
} 