package com.chaty.service.impl;

import java.util.Arrays;
import java.util.concurrent.Semaphore;

import javax.annotation.PostConstruct;

import com.chaty.task.correct.SemaphoreManager;
import com.google.common.util.concurrent.RateLimiter;
import com.tencentcloudapi.ecc.v20181213.EccClient;
import com.tencentcloudapi.ecc.v20181213.models.CorrectData;
import com.tencentcloudapi.ecc.v20181213.models.ECCRequest;
import com.tencentcloudapi.ecc.v20181213.models.ECCResponse;
import com.tencentcloudapi.ocr.v20181119.models.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import com.chaty.exception.BaseException;
import com.chaty.exception.RetryException;
import com.chaty.service.OCRService;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.ocr.v20181119.OcrClient;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Primary
@Service("tencentOCRService")
public class TencentOCRServiceImpl implements OCRService {

    @Value("${api.temcentcloud.endpoint:ocr.tencentcloudapi.com}")
    public String endpoint;
    @Value("${api.temcentcloud.secretid}")
    public String secretid;
    @Value("${api.temcentcloud.secretkey}")
    public String secretKey;
    @Value("${api.temcentcloud.region:ap-shanghai}")
    public String region;

    private HttpProfile httpProfile;

    @PostConstruct
    public void postConstruct() {
        log.info("tencent cloud api configuration: \n endpoint: {} \n secretid: {} \n secretKey: {} \n region: {} \n",
                endpoint, secretid, secretKey, region);
    }

    private OcrClient createOcrClient() {
        Credential cred = new Credential(secretid, secretKey);
        httpProfile = new HttpProfile();
        httpProfile.setEndpoint(endpoint);
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        return new OcrClient(cred, region, clientProfile);
    }

    @Retryable(value = RetryException.class, backoff = @Backoff(delay = 1000))
    @Override
    public String ocrForText(String url) {
        try {
            OcrClient client = createOcrClient();
            // 通用印刷体识别（高精度版）
            GeneralAccurateOCRRequest req = new GeneralAccurateOCRRequest();
            req.setImageUrl(url);
            GeneralAccurateOCRResponse resp = client.GeneralAccurateOCR(req);
            return Arrays.stream(resp.getTextDetections()).map(d -> d.getDetectedText()).reduce("", (a, b) -> a + b);
        } catch (TencentCloudSDKException e) {
            log.error("tencent cloud ocr api error: {}", e.getMessage());

            if (e.getMessage().contains("照片中未检测到文本")) {
                log.warn("照片中未检测到文本: {}", url);
                return "";
            }

            throw new BaseException("OCR 识别失败", e);
        }
    }

    @Retryable(value = RetryException.class, backoff = @Backoff(delay = 1000))
    @Override
    public String ocrForHandwritingText(String base64) throws InterruptedException {
        SemaphoreManager semaphoreManager = SemaphoreManager.getInstance();
        RateLimiter rateLimiter = semaphoreManager.getTencentHandWriteenGlobalRateLimiter();
        try {
            rateLimiter.acquire();

            OcrClient client = createOcrClient();
            // 通用手写体识别
            GeneralHandwritingOCRRequest req = new GeneralHandwritingOCRRequest();
            if (base64.startsWith("http")) {
                req.setImageUrl(base64);
            } else {
                req.setImageBase64(base64);
            }
//            req.setScene("only_hw");  // 只识别手写体，过滤掉印刷体

            GeneralHandwritingOCRResponse resp = client.GeneralHandwritingOCR(req);
            return Arrays.stream(resp.getTextDetections())
                    .map(d -> d.getDetectedText())
                    .reduce("", (a, b) -> a + b); // 合并所有识别出的文本
        } catch (TencentCloudSDKException e) {
            log.error("tencent cloud handwriting ocr api error: {} , error code: {}", e.getMessage(), e.getErrorCode());

            if (e.getMessage().contains("照片中未检测到文本")) {
                log.warn("照片中未检测到手写体文本: {}", base64);
                return "";
            }

            throw new BaseException("手写体 OCR 识别失败", e);
        } finally {
        }
    }

    @Retryable(value = RetryException.class, backoff = @Backoff(delay = 1000))
    @Override
    public String ocrForArithmetic(String base64) throws InterruptedException {
        SemaphoreManager semaphoreManager = SemaphoreManager.getInstance();
        Semaphore semaphore = semaphoreManager.getTencentArithmeticGlobalSemaphore();
        try {
            semaphore.acquire();

            OcrClient client = createOcrClient();
            ArithmeticOCRRequest req = new ArithmeticOCRRequest();
            req.setImageBase64(base64);
            ArithmeticOCRResponse resp = client.ArithmeticOCR(req);
            return Arrays.stream(resp.getTextDetections())
                    .map(d -> d.getDetectedText())
                    .reduce("", (a, b) -> a + (a.isEmpty() ? "" : "\n") + b); // 每个识别文本之间添加换行符
        } catch (TencentCloudSDKException e) {
            log.error("tencent cloud handwriting ocr api error: {} , error code: {}", e.getMessage(), e.getErrorCode());

            if (e.getMessage().contains("照片中未检测到文本")) {
                log.warn("照片中未检测到手写体文本: {}", base64);
                return "";
            }

            throw new BaseException("手写体 OCR 识别失败", e);
        } finally {
            semaphore.release();
        }
    }

}