<#if reviewContext.docType == "A3">
\documentclass[12pt,landscape]{article}
\usepackage[a3paper]{geometry}
<#else>
\documentclass[a4paper]{article}
</#if>
\usepackage[absolute, overlay]{textpos}
\usepackage{CJKutf8}
\usepackage{fancyhdr}
\usepackage{pifont}
\usepackage{color}
\usepackage{graphicx}
\usepackage{pdfpages}
\usepackage{pbox}
\pagestyle{fancy}
\fancyhf{}
\renewcommand{\headrulewidth}{0pt}

\setlength{\TPHorizModule}{1mm}
\setlength{\TPVertModule}{1mm}

\begin{document}

<#list reviewedGroup as reviewedRes>

<#if reviewContext.isScore>
<#function getScore res>
    <#if (res.status?? && !res.status) || (res.review?? && res.review.isTrue)>
        <#return res.score?number?default(0)>
    <#else>
        <#return 0>
    </#if>
</#function>
<#assign scoreSum = 0>
<#assign scoreTotal = 0>
<#assign correctRate = 100>
<#list reviewedRes as res>
    <#assign scoreSum = scoreSum + getScore(res)>
    <#assign scoreTotal = scoreTotal + res.score?number?default(0)>
</#list>
<#if (scoreTotal > 0)>
    <#assign correctRate = scoreSum?double / scoreTotal?double * 100>
</#if>


% 如果分数大于等于60%，就是绿色，分数小于60%，就是红色
% 这部分可以显示90/100这样子
\begin{textblock}{1000}(${reviewContext.scoreArea.x / 300 * 25.4}, ${reviewContext.scoreArea.y / 300 * 25.4})
    \fontsize{100pt}{100pt} \selectfont
    \textcolor{${(correctRate gte 60)?string('green','red')}}{${scoreSum}/${scoreTotal}}
\end{textblock}

</#if>

<#if reviewedRes?has_content>
<#list reviewedRes as reviewed>

<#if (reviewed.status && reviewed.review.isTrue) || (!reviewed.defaultReview?has_content && reviewed.isDefaultReview) || !reviewed.status>
<#else>
\begin{textblock}{1000}(${reviewed.reviewArea.x / 300 * 25.4}, ${reviewed.reviewArea.y / 300 * 25.4})
\fbox{
\pbox[t]{${(reviewed.reviewArea.width / 300 * 25.4) - 20}mm}{
\begin{CJK*}{UTF8}{gbsn}
<#if reviewed.status && reviewed.review.isTrue>
<#if reviewed.isDefaultReview>
<#else>
<#if reviewed.review.review?has_content>
\fontsize{${reviewContext.fontSize}}{50pt}\selectfont ${reviewed.review.review}
</#if>
</#if>
<#elseif !reviewed.status>
${'\\textcolor{red}{\\fontsize{' + reviewContext.fontSize + '}{50pt}\\selectfont ' + reviewed.error + '}'}
<#else>
<#if reviewed.isDefaultReview>
${'\\textcolor{red}{\\fontsize{' + reviewContext.fontSize + '}{50pt}\\selectfont ' + reviewed.defaultReview + '}'}
<#else>
<#if reviewed.review.review?has_content>
${'\\textcolor{red}{\\fontsize{' + reviewContext.fontSize + '}{50pt}\\selectfont ' + reviewed.review.review + '}'}
</#if>
</#if>
</#if>
\end{CJK*}
}
}
\end{textblock}
</#if>

\begin{textblock}{1000}(${reviewed.checkArea.x / 300 * 25.4}, ${reviewed.checkArea.y / 300 * 25.4})
\begin{minipage}{${reviewed.checkArea.width / 300 * 25.4}mm}
<#if (reviewed.status && reviewed.review.isTrue) || !reviewed.status>
\textcolor{green}{\fontsize{${reviewContext.signSize}}{50pt}\selectfont \ding{51}}
<#elseif !reviewed.status>
<#else>
\textcolor{red}{\fontsize{${reviewContext.signSize}}{50pt}\selectfont \ding{55}}
</#if>
\end{minipage}
\end{textblock}

</#list>
</#if>

<#if reviewContext.isPreview>

<#assign fileurlParts = reviewedDoc[reviewedRes_index].fileurl?split("/")>
\includepdf[pages=-, frame=true, scale=1, pagecommand={}]{${fileurlParts[2]}}

<#else>

\null
\clearpage

</#if>

</#list>

\end{document}