package com.chaty.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.chaty.entity.GptAskLogEntity;
import com.chaty.enums.GptAskLogType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;

import com.chaty.dto.ChatCompletionDTO;
import com.chaty.dto.DocReviewedDTO;
import com.chaty.entity.DocQuestion;
import com.chaty.entity.DocReviewed;
import com.chaty.enums.CompletionEnums;
import com.chaty.exception.BaseException;
import com.chaty.exception.RetryException;
import com.chaty.mapper.DocQuestionMapper;
import com.chaty.mapper.DocReviewedMapper;
import com.chaty.service.BasicAiService;
import com.chaty.service.DocReviewedService;
import com.chaty.service.OCRService;
import com.chaty.service.PDFService;
import com.chaty.service.PDFService.TexCmd;
import com.chaty.util.CVUtil;
import com.chaty.util.LatexUtil;
import com.chaty.util.PDFUtil;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class DocReviewedServiceImpl implements DocReviewedService {

    @Value("${file.local.path}")
    public String path;
    @Value("${file.local.ctxpath}")
    public String ctxPath;
    @Value("${server.url}")
    public String serverUrl;

    @Resource
    private DocReviewedMapper docReviewedMapper;
    @Resource
    private TaskExecutor taskExecutor;
    @Resource
    private Map<String, OCRService> ocrServices;
    @Resource
    private DocQuestionMapper docQuestionMapper;
    @Resource
    private Set<BasicAiService> basicAiServices;
    @Resource
    private PDFService pdfService;
    @Resource
    private ApplicationContext applicationContext;

    @Override
    public DocReviewed add(DocReviewed param) {
        docReviewedMapper.insertOne(param);
        return param;
    }

    @Override
    public void deleteById(Integer id) {
        docReviewedMapper.deleteById(id);
    }

    @Override
    public List<DocReviewed> list(DocReviewed param) {
        return docReviewedMapper.list(param);
    }

    @Override
    public DocReviewed selectById(Integer id) {
        return docReviewedMapper.selectById(id);
    }

    @Override
    public void updateById(DocReviewed param) {
        docReviewedMapper.updateById(param);
    }

    @Override
    public Map<String, Object> review(DocReviewedDTO param) {
        Map<String, Object> res = new HashMap<>();

        String batchId = IdUtil.fastSimpleUUID();
        DocReviewed update = new DocReviewed();
        update.setBatchId(batchId);
        update.setIds(param.getIds());
        docReviewedMapper.updateById(update);

        param.setBatchId(batchId);
        param.getIds().forEach(id -> {
            taskExecutor.execute(() -> doReview(param, id));
        });

        res.put("batchId", batchId);
        return res;
    }

    public void doReview(DocReviewedDTO context, Integer id) {
        DocReviewed doc = docReviewedMapper.selectById(id);
        if (doc == null) {
            log.warn("doc not found, id: {}", id);
            return;
        }

        // 更新为批改中
        doc.setStatus(2);
        docReviewedMapper.updateById(doc);

        DocReviewed reviewed = new DocReviewed();
        reviewed.setId(id);

        try {
            String fileurl = doc.getFileurl();
            String filename = fileurl.substring(ctxPath.length() + 1);
            String imgname = IdUtil.fastSimpleUUID() + ".jpg";

            // 生成图片
            PDFUtil.convert2Img(String.format("%s/%s", path, filename), String.format("%s/%s", path, imgname));
            // 找到所有的方块对应的位置
            List<Map<String, Integer>> points = CVUtil.findRectInImg(String.format("%s/%s", path, imgname));
            if (points.size() % 3 != 0) {
                throw new BaseException("试卷格式不正确，无法正确识别题目!");
            }
            int questionSize = points.size() / 3;
            List<Map<String, Object>> reviewedQuestions = new ArrayList<>();
            // 遍历所有的方块
            for (int i = 0; i < questionSize; i++) {
                int index = i * 3;
                Map<String, Integer>[] questionPoints = new Map[] {
                        points.get(index), points.get(index + 1), points.get(index + 2) };
                Map<String, Object> reviewQuestion = reviewQuestion(context, filename, imgname, questionPoints);
                reviewedQuestions.add(reviewQuestion);
            }
            reviewed.setReviewed(JSONUtil.toJsonStr(reviewedQuestions));
            // TODO 生成文档
            Map<String, Object> docRes = createReviewDoc(context, Collections.singletonList(reviewed));
            
            reviewed.setReviewedDoc((String) docRes.get("fileUrl"));
            reviewed.setStatus(3);
        } catch (Exception e) {
            log.error("review error, id: {}", id, e);
            reviewed.setStatus(4);
            reviewed.setError(e.getMessage());
        }

        docReviewedMapper.updateById(reviewed);
    }

    public Map<String, Object> reviewQuestion(DocReviewedDTO context,
            String filename, String imgname, Map<String, Integer>[] points) {
        Map<String, Object> res = new HashMap<>();
        res.put("status", 1); // 1-正常 2-异常
        res.put("points", points);
        int x = 500, w = 1520;

        try {
            // ocr识别题号
            String questionImg = ocrArea(context, filename, x, points[0].get("y") - 10, w, 90);
            String questionNo = ocrServices.get(context.getOcrService())
                    .ocrForText(String.format("%s%s/%s", serverUrl, ctxPath, questionImg));
            questionNo = ReUtil.getGroup0("[0-9]+", questionNo);
            log.info("识别题号: {}", questionNo);
            res.put("id", questionNo);
            // 查询题目
            DocQuestion docQuestion = docQuestionMapper.selectById(Integer.parseInt(questionNo));
            if (Objects.isNull(docQuestion)) {
                throw new BaseException("题目不存在!");
            }
            // ocr 答案
            String ocrimg = ocrArea(context, filename, x, points[1].get("y") - 10, w, points[2].get("y") - points[1].get("y") + 100);
            // 批改
            if (context.getAimodel().equals("gpt-4-vision-preview")) {
                visionAiReview(context, docQuestion, ocrimg, res);
            } else {
                String answer = ocrServices.get(context.getOcrService())
                        .ocrForText(String.format("%s%s/%s", serverUrl, ctxPath, ocrimg));
                aiReview(context, docQuestion, answer, res);
            }
        } catch (Exception e) {
            log.error("批改异常", e);
            res.put("status", 2); // 异常
        }

        return res;
    }

    public String ocrArea(DocReviewedDTO context, String filename, int x, int y, int w, int h) {
        String ocrimg = IdUtil.fastSimpleUUID() + ".jpg";
        String filepath = String.format("%s/%s", path, filename);
        String ocrimgpath = String.format("%s/%s", path, ocrimg);
        PDFUtil.extractImageFromPDF(filepath, ocrimgpath, x, y, w, h);
        ThreadUtil.sleep(1000);

        return ocrimg;
    }

    public void aiReview(DocReviewedDTO context, DocQuestion question, String answer, Map<String, Object> res) {
        // 请求参数
        ChatCompletionDTO completion = CompletionEnums.AI_REVIEW.getCompletion(context.getAimodel(),
            exactQuestion(question.getQuestion()), question.getCorrectAnswer(), answer);
        GptAskLogEntity gptAskLogEntity = new GptAskLogEntity();
        gptAskLogEntity.setAimodel(context.getAimodel());
        gptAskLogEntity.setType(GptAskLogType.AiReview);
        Map<String, Object> cresp = BasicAiService.findSupport(basicAiServices, context.getAimodel())
                .chatForCompletion(completion, gptAskLogEntity);
        // TOOD 异常处理
        JSONObject jsonResp = JSONUtil.parseObj(cresp);
        if (CompletionEnums.AI_REVIEW.getFuncName()
                .equals(jsonResp.getByPath("'$function_call'.name", String.class))) {
            String reviewed = jsonResp.getByPath("'$function_call'.arguments", String.class);
            reviewed = reviewed.replaceAll("(?<!\\\\)\\\\(?!\\\\)", "\\\\\\\\")
                    .replaceAll("\\\\\n", "\\\\n");
            log.info("parsed reviewed: {}", reviewed);
            JSONObject reviewedObj = JSONUtil.parseObj(reviewed);
            res.put("isTrue", reviewedObj.get("isTrue"));
            res.put("review", LatexUtil.escapeLatex(reviewedObj.getStr("review")));
        } else {
            res.put("status", 2);
            res.put("error", jsonResp.get("$response"));
        }
    }

    public Map<String, Object> createReviewDoc(DocReviewedDTO context, List<DocReviewed> revieweds) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("context", context);
        params.put("revieweds", revieweds);
        List<JSONArray> questions = revieweds.stream().map(reviewed -> {
            return JSONUtil.parseArray(reviewed.getReviewed());  
        }).collect(Collectors.toList());
        params.put("questions", questions);
        return pdfService.createDoc(TexCmd.PDFLATEX, "docreview1", params);
    }

    public void visionAiReview(DocReviewedDTO context, DocQuestion question, String ocrimg, Map<String, Object> res) {
        String imgUrl = String.format("%s%s/%s", serverUrl, ctxPath, ocrimg);
        // 请求参数
        ChatCompletionDTO completion = CompletionEnums.VISION_DOVREVIEW.getVisionCompletion(
                context.getAimodel(), imgUrl,
                question.getCorrectAnswer(), exactQuestion(question.getQuestion()));
        GptAskLogEntity gptAskLogEntity = new GptAskLogEntity();
        gptAskLogEntity.setAimodel(context.getAimodel());
        gptAskLogEntity.setType(GptAskLogType.AiReview);
        Map<String, Object> cresp = BasicAiService.findSupport(basicAiServices, context.getAimodel())
                .chatForCompletion(completion, gptAskLogEntity);
        // TOOD 异常处理
        JSONObject jsonResp = JSONUtil.parseObj(cresp);
        String response = jsonResp.get("$response", String.class);
        log.info("visionReview response: {}", response);
        
        // 结果解析
        String regex = "(?<=(### 是否正确|### 描述学生答案)\\n)([\\s\\S]*?)(?=(\\n\\n|$))";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(response);
        List<String> matchGroups = new ArrayList<String>();
        while (matcher.find()) {
            matchGroups.add(matcher.group());
        }
        if (matchGroups.size() == 2) {
            res.put("isTrue", Objects.equals(matchGroups.get(0), "1") ? true : false);
            res.put("review", LatexUtil.escapeLatex(matchGroups.get(1)));
        } else {
            res.put("status", 2);
            res.put("error", response);
        }
    }

    @Override
    public Map<String, Object> batchReviewDoc(DocReviewedDTO param) {
        return createReviewDoc(param, param.getDocs());
    }

    /**
     * 问题前面的"()"的内容去除
     * @param rawQuestion 问题
     * @return
     */
    public String exactQuestion(String rawQuestion) {
        return ReUtil.delAll("^\\\\\\(\\([^(\\)\\\\\\))]*\\)\\\\\\)", rawQuestion);
    }

}
