package com.chaty.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chaty.dto.ComprehensiveParamDTO;
import com.chaty.dto.ComprehensiveResultDTO;
import com.chaty.dto.DocCorrectFileDTO;
import com.chaty.entity.*;
import com.chaty.mapper.*;
import com.chaty.security.AuthUtil;
import com.chaty.service.ComprehensiveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ComprehensiveServiceImpl implements ComprehensiveService {


    @Autowired
    private DocCorrectFileMapper docCorrectFileMapper;

    @Autowired
    private DocCorrectConfigPackageMapper docCorrectConfigPackageMapper;

    @Resource
    private DocCorrectTaskMapper docCorrectTaskMapper;

    @Resource
    private ClassMapper classMapper;

    @Resource
    private SchoolMapper schoolMapper;

    /**
     * 对两个表进行分页查询并合并，按更新时间排序
     *
     * @param param 查询参数
     * @return 合并后的分页结果
     */
    @Override
    public IPage<ComprehensiveResultDTO> combinedPage(ComprehensiveParamDTO param) {
        if (StrUtil.isNotBlank(param.getConfigIds())) {
            // 只批改试卷
            param.setFileType("paper");
        }
        // 构建查询条件
        LambdaQueryWrapper<DocCorrectFile> fileWrapper = new LambdaQueryWrapper<DocCorrectFile>()
                .like(param.getName() != null, DocCorrectFile::getName, param.getName())
                .eq(param.getStatus() != null, DocCorrectFile::getStatus, param.getStatus())
                .eq(DocCorrectFile::getDeleted, 0)
//                .eq(DocCorrectFile::getCreator, AuthUtil.getLoginUser().getId())
                .orderByAsc(DocCorrectFile::getStatus)
                .orderByDesc(DocCorrectFile::getCreateTime, DocCorrectFile::getId);

        if ("essay".equals(param.getFileType())) {
            fileWrapper.eq(DocCorrectFile::getIsEssay, 1);
        }

        LambdaQueryWrapper<DocCorrectConfigPackage> packageWrapper = new LambdaQueryWrapper<DocCorrectConfigPackage>()
                .like(param.getName() != null, DocCorrectConfigPackage::getName, param.getName())
//                .eq(DocCorrectConfigPackage::getCreator, AuthUtil.getLoginUser().getId())
                .orderByDesc(DocCorrectConfigPackage::getCreateTime);

        // 查询两个表的数据，去掉分页条件
        List<DocCorrectFile> fileList = new ArrayList<>();
        if (!"config".equals(param.getFileType())) {
            fileList = docCorrectFileMapper.selectList(fileWrapper);
        }

        List<String> fileIds = fileList.stream()
                .map(DocCorrectFile::getId)
                .collect(Collectors.toList());
        Map<String, List<String>> configIdsByFile;
        if (!fileIds.isEmpty()) {
            // 批量查每个 fileId 对应的所有 task，只取 fileId 和 configId
            List<DocCorrectTask> tasks = docCorrectTaskMapper.selectList(
                    Wrappers.<DocCorrectTask>lambdaQuery()
                            .in(DocCorrectTask::getFileId, fileIds)
                            .select(DocCorrectTask::getFileId, DocCorrectTask::getConfigId)
            );
            // 分组映射成 Map<fileId, List<configId>>
            configIdsByFile = tasks.stream()
                    .collect(Collectors.groupingBy(
                            DocCorrectTask::getFileId,
                            Collectors.mapping(DocCorrectTask::getConfigId, Collectors.toList())
                    ));
        } else {
            configIdsByFile = new HashMap<>();
        }

        List<DocCorrectConfigPackage> packageList = new ArrayList<>();
        if ("all".equals(param.getFileType()) || "config".equals(param.getFileType())) {
            packageList = docCorrectConfigPackageMapper.selectList(packageWrapper);
        }

        // 查询班级信息
        List<SchoolClass> classList;
        List<School> schoolList = new ArrayList<>();
        if (!fileList.isEmpty()) {
            List<String> classIds = fileList.stream()
                    .map(DocCorrectFile::getClassId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            if (!classIds.isEmpty()) {
                classList = classMapper.selectBatchIds(classIds);
                if (!classList.isEmpty()) {
                    List<String> schoolIds = classList.stream()
                            .map(SchoolClass::getSchoolId)
                            .distinct()
                            .collect(Collectors.toList());
                    if (!schoolIds.isEmpty()) {
                        schoolList = schoolMapper.selectBatchIds(schoolIds);
                    }
                }
            } else {
                classList = new ArrayList<>();
            }
        } else {
            classList = new ArrayList<>();
        }
        // 合并两个表的数据
        List<ComprehensiveResultDTO> combinedList = new ArrayList<>();

        // 合并 DocCorrectFile 的结果
        List<School> finalSchoolList = schoolList;
        combinedList.addAll(fileList.stream().map(file -> {
            ComprehensiveResultDTO dto = new ComprehensiveResultDTO();
            dto.setId(file.getId());
            dto.setName(file.getName());
            dto.setUrl(file.getUrl());
            dto.setStatus(file.getStatus());
            dto.setIsEssay(file.getIsEssay());
            dto.setCreator(file.getCreator());
            dto.setCreateTime(file.getCreateTime());
            dto.setUpdateTime(file.getUpdateTime());  // 假设更新时间为创建时间，具体按实际需求调整
            dto.setRemark(file.getRemark());
            dto.setIsCorrectFinish(file.getIsCorrectFinish());
            dto.setType(0);  // 0 表示 DocCorrectFile
            dto.setClassId(file.getClassId());
            dto.setLastCorrectTime(file.getLastCorrectTime());
            dto.setRecordSize(file.getRecordSize());
            dto.setConfigPackageId(file.getConfigPackageId());
            dto.setModelRequestId(file.getModelRequestId());
            if (StrUtil.isNotBlank(file.getClassId())) {
                SchoolClass schoolClass = classList.stream()
                        .filter(c -> c.getClassId().equals(file.getClassId()))
                        .findFirst()
                        .orElse(null);
                if (Objects.nonNull(schoolClass)) {
                    dto.setClassName(schoolClass.getClassName());
                    dto.setSchoolName(finalSchoolList.stream()
                            .filter(s -> s.getSchoolId().equals(schoolClass.getSchoolId()))
                            .map(School::getSchoolName)
                            .findFirst()
                            .orElse(""));
                }
            }

            List<String> cids = configIdsByFile.getOrDefault(file.getId(), Collections.emptyList());
            String json = JSONUtil.toJsonStr(cids);
            dto.setConfigIds(json);
            return dto;
        }).collect(Collectors.toList()));

        // 合并 DocCorrectConfigPackage 的结果
        combinedList.addAll(packageList.stream().map(pkg -> {
            ComprehensiveResultDTO dto = new ComprehensiveResultDTO();
            dto.setId(pkg.getId());
            dto.setName(pkg.getName());
            dto.setConfig(pkg.getConfig());
            dto.setCreator(pkg.getCreator());
            dto.setCreateTime(pkg.getCreateTime());
            dto.setUpdateTime(pkg.getUpdateTime());  // 假设更新时间为创建时间，具体按实际需求调整
            dto.setType(1);  // 1 表示 DocCorrectConfigPackage
            dto.setRemark(pkg.getRemark());
            return dto;
        }).collect(Collectors.toList()));

        // 按更新时间排序
        combinedList.sort(
                Comparator.comparing(
                        ComprehensiveResultDTO::getCreateTime,
                        Comparator.nullsLast(Comparator.naturalOrder())
                ).reversed()
        );


        // 进行分页
        int totalSize = combinedList.size();
        int pageSize = param.getPage().getPageSize();    // 每页条数
        int pageNumber = param.getPage().getPageNumber();  // 当前请求页码，1-based

        int totalPage = (totalSize + pageSize - 1) / pageSize;
        if (pageNumber > totalPage) {
            pageNumber = totalPage;
        }
        if (pageNumber < 1) {
            pageNumber = 1;
        }

        int start = (pageNumber - 1) * pageSize;
        int end = Math.min(start + pageSize, totalSize);
        List<ComprehensiveResultDTO> pageRecords = combinedList.subList(start, end);


        // 将合并后的分页结果封装到 Page 对象中返回
        Page<ComprehensiveResultDTO> resultPage = new Page<>(param.getPage().getPageNumber(), param.getPage().getPageSize());
        resultPage.setRecords(pageRecords);
        resultPage.setTotal(totalSize);  // 返回合并后的记录总数

        return resultPage;
    }

    @Override
    public IPage<ComprehensiveResultDTO> pageByConfigIds(ComprehensiveParamDTO param) {
        String configsIds = param.getConfigIds();
        JSONArray configIdsArray = JSONUtil.parseArray(configsIds);
        List<String> configIds = configIdsArray.stream()
                .map(Object::toString)
                .collect(Collectors.toList());


        List<DocCorrectTask> tasks = docCorrectTaskMapper.selectList(
                Wrappers.<DocCorrectTask>lambdaQuery()
                        .in(DocCorrectTask::getConfigId, configIds)
                        .select(DocCorrectTask::getFileId, DocCorrectTask::getConfigId)
        );

        // 找出每个fileId用到的configId，看看是不是和configIds一致
        Map<String, Set<String>> fileIdToConfigIdsMap = new HashMap<>();
        for (DocCorrectTask task : tasks) {
            String fileId = task.getFileId();
            String configId = task.getConfigId();
            fileIdToConfigIdsMap
                    .computeIfAbsent(fileId, k -> new HashSet<>())
                    .add(configId);
        }


        // 构建查询条件
        LambdaQueryWrapper<DocCorrectFile> fileWrapper = new LambdaQueryWrapper<DocCorrectFile>()
                .in(DocCorrectFile::getId, fileIdToConfigIdsMap.keySet())
                .orderByAsc(DocCorrectFile::getStatus)
                .orderByDesc(DocCorrectFile::getCreateTime, DocCorrectFile::getId);
        List<DocCorrectFile> fileList = docCorrectFileMapper.selectList(fileWrapper);



        // 查询班级信息
        List<SchoolClass> classList;
        List<School> schoolList = new ArrayList<>();
        if (!fileList.isEmpty()) {
            List<String> classIds = fileList.stream()
                    .map(DocCorrectFile::getClassId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            if (!classIds.isEmpty()) {
                classList = classMapper.selectBatchIds(classIds);
                if (!classList.isEmpty()) {
                    List<String> schoolIds = classList.stream()
                            .map(SchoolClass::getSchoolId)
                            .distinct()
                            .collect(Collectors.toList());
                    if (!schoolIds.isEmpty()) {
                        schoolList = schoolMapper.selectBatchIds(schoolIds);
                    }
                }
            } else {
                classList = new ArrayList<>();
            }
        } else {
            classList = new ArrayList<>();
        }
        // 合并两个表的数据
        List<ComprehensiveResultDTO> combinedList = new ArrayList<>();

        // 合并 DocCorrectFile 的结果
        List<School> finalSchoolList = schoolList;
        combinedList.addAll(fileList.stream().map(file -> {
            ComprehensiveResultDTO dto = new ComprehensiveResultDTO();
            dto.setId(file.getId());
            dto.setName(file.getName());
            dto.setUrl(file.getUrl());
            dto.setStatus(file.getStatus());
            dto.setIsEssay(file.getIsEssay());
            dto.setCreator(file.getCreator());
            dto.setCreateTime(file.getCreateTime());
            dto.setUpdateTime(file.getUpdateTime());  // 假设更新时间为创建时间，具体按实际需求调整
            dto.setRemark(file.getRemark());
            dto.setIsCorrectFinish(file.getIsCorrectFinish());
            dto.setType(0);  // 0 表示 DocCorrectFile
            dto.setClassId(file.getClassId());
            dto.setLastCorrectTime(file.getLastCorrectTime());
            dto.setRecordSize(file.getRecordSize());
            dto.setConfigPackageId(file.getConfigPackageId());
            dto.setModelRequestId(file.getModelRequestId());
            if (StrUtil.isNotBlank(file.getClassId())) {
                SchoolClass schoolClass = classList.stream()
                        .filter(c -> c.getClassId().equals(file.getClassId()))
                        .findFirst()
                        .orElse(null);
                if (Objects.nonNull(schoolClass)) {
                    dto.setClassName(schoolClass.getClassName());
                    dto.setSchoolName(finalSchoolList.stream()
                            .filter(s -> s.getSchoolId().equals(schoolClass.getSchoolId()))
                            .map(School::getSchoolName)
                            .findFirst()
                            .orElse(""));
                }
            }

            dto.setConfigIds(configsIds);
            return dto;
        }).collect(Collectors.toList()));


        // 按更新时间排序
        combinedList.sort(
                Comparator.comparing(
                        ComprehensiveResultDTO::getCreateTime,
                        Comparator.nullsLast(Comparator.naturalOrder())
                ).reversed()
        );


        // 进行分页
        int totalSize = combinedList.size();
        int pageSize = param.getPage().getPageSize();    // 每页条数
        int pageNumber = param.getPage().getPageNumber();  // 当前请求页码，1-based

        int totalPage = (totalSize + pageSize - 1) / pageSize;
        if (pageNumber > totalPage) {
            pageNumber = totalPage;
        }
        if (pageNumber < 1) {
            pageNumber = 1;
        }

        int start = (pageNumber - 1) * pageSize;
        int end = Math.min(start + pageSize, totalSize);
        List<ComprehensiveResultDTO> pageRecords = combinedList.subList(start, end);



        // 将合并后的分页结果封装到 Page 对象中返回
        Page<ComprehensiveResultDTO> resultPage = new Page<>(param.getPage().getPageNumber(), param.getPage().getPageSize());
        resultPage.setRecords(pageRecords);
        resultPage.setTotal(totalSize);  // 返回合并后的记录总数

        return resultPage;
    }
}
