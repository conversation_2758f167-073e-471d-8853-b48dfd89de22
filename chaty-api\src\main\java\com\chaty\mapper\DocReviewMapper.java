package com.chaty.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.chaty.entity.DocReview;

@Mapper
public interface DocReviewMapper {

    List<DocReview> list(DocReview param);

    @Insert("INSERT INTO doc_review (id, filename, fileurl, identify, status, error_text, review_doc, batch_id, reviewed) VALUES (#{id}, #{filename}, #{fileurl}, #{identify}, #{status}, #{errorText}, #{reviewDoc}, #{batchId}, #{reviewed})")
    void insertOne(DocReview entity);

    void insertBatch(List<DocReview> entities);

    void updateById(DocReview entity);

    @Update("update doc_review set deleted = 1 where id = #{id}")
    void deleteById(@Param("id") String id);

    @Select("select * from doc_review where id = #{id}")
    DocReview selectById(@Param("id") String id);

}
