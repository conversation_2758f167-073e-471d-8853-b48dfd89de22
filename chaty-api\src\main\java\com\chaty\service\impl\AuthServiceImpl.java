package com.chaty.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import com.chaty.entity.School;
import org.springframework.stereotype.Service;

import com.chaty.dto.AdminRoleDTO;
import com.chaty.dto.SchoolDTO;
import com.chaty.entity.User;
import com.chaty.exception.BaseException;
import com.chaty.security.AuthUtil;
import com.chaty.service.AuthService;
import com.chaty.service.SchoolUserService;
import com.chaty.service.admin.AdminRoleUserService;

import cn.hutool.core.map.MapUtil;

import static com.chaty.security.AuthUtil.getLoginSchool;

@Service
public class AuthServiceImpl implements AuthService {

    @Resource
    private AdminRoleUserService adminRoleUserService;
    @Resource
    private SchoolUserService schoolUserService;

    @Override
    public Map<String, Object> getAuthData() {
        User loginUser = AuthUtil.getLoginUser();
        if (loginUser == null) {
            throw new BaseException("用户未登录");
        }
        List<AdminRoleDTO> roles = adminRoleUserService.getRoleList(loginUser.getId());
        List<SchoolDTO> schools = schoolUserService.getSchoolList(loginUser.getId());
        if (schools.isEmpty()) {
            throw new BaseException("用户未关联任何学校");
        }
        // 设置登录用户的学校
        SchoolDTO loginSchool = getLoginSchool();
        if (loginSchool == null) {
            String defaultSchool = loginUser.getDefaultSchool();
            SchoolDTO school = schools.get(0);
            if (defaultSchool != null) {
                school = schools.stream()
                        .filter(s -> s.getSchoolId().equals(defaultSchool))
                        .findFirst()
                        .orElse(null);
            }
            AuthUtil.setLoginSchool(school);
            loginSchool = school;
        }


        return MapUtil.builder(new HashMap<String, Object>())
                .put("roles", roles)
                .put("school", loginSchool)
                .build();
    }

    @Override
    public void loginWithSchool(String schoolId) {
        User loginUser = AuthUtil.getLoginUser();
        if (loginUser == null) {
            throw new BaseException("用户未登录");
        }
        List<SchoolDTO> schools = schoolUserService.getSchoolList(loginUser.getId());
        SchoolDTO school = schools.stream()
                .filter(s -> s.getSchoolId().equals(schoolId))
                .findFirst()
                .orElseThrow(() -> new BaseException("无法登录到改组织"));
        AuthUtil.setLoginSchool(school);
    }

    @Override
    public List<SchoolDTO> getSchools() {
        User loginUser = AuthUtil.getLoginUser();
        if (loginUser == null) {
            throw new BaseException("用户未登录");
        }
        return schoolUserService.getSchoolList(loginUser.getId());
    }

}
