\documentclass[a4paper,12pt]{article}
\usepackage{graphicx}
\usepackage{caption}
\usepackage{geometry}
\usepackage{xeCJK}  % 添加 xeCJK 宏包以支持中文

\geometry{left=2cm, right=2cm, top=2cm, bottom=2cm}

\begin{document}

\section*{${nickname!""}错题整理 Date:${date}}  % 添加中文标题

<#list imgs as img>
\begin{figure}[h!]
    \centering
    \includegraphics[width=0.7\textwidth]{${img?split("/")[2]}}
\end{figure}
</#list>

\end{document}
