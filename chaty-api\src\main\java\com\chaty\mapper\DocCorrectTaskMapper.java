package com.chaty.mapper;

import java.sql.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.chaty.dto.DocCorrectResultDTO;
import com.chaty.dto.DocCorrectTaskDTO;
import com.chaty.entity.DocCorrectResult;
import com.chaty.entity.DocCorrectTask;

@Mapper
public interface DocCorrectTaskMapper extends BaseMapper<DocCorrectTask> {

    @Select("select dct.*, dcc.name as config_name from doc_correct_task dct left join doc_correct_config dcc on dct.config_id = dcc.id ${ew.customSqlSegment}")
    public IPage<DocCorrectTaskDTO> page(IPage<?> page, @Param(Constants.WRAPPER) Wrapper<DocCorrectTask> wrapper);

    @Update("update doc_correct_task set status = 3 where id = #{id} and status <> 3 and (select count(1) from doc_correct_record where task_id = #{id} AND status in (2,3)) = 0")
    int tryComplete(@Param("id") String id);

    @Select("select count(1) as count, dct.status from doc_correct_task dct where dct.deleted = 0 and dct.creator = #{userId} group by dct.status")
    List<DocCorrectTaskDTO> countByStatus(String userId);

    @Select("select dct.*, dcc.name as config_name from (select DISTINCT (task_id) as task_id from user_doc WHERE user_id = #{userId}) ud left join doc_correct_task dct on ud.task_id = dct.id left join doc_correct_config dcc on dct.config_id = dcc.id ${ew.customSqlSegment}")
    public IPage<DocCorrectTaskDTO> pageByUser(IPage<?> page, @Param("userId") String userId, @Param(Constants.WRAPPER) Wrapper<DocCorrectTask> wrapper);

    @Select("select dcr.task_id as taskId, sum(dcr.scored) as totalScore, sum(dcr.score) as totalScoree from doc_correct_result dcr where dcr.deleted = 0 and dcr.task_id in ('${taskIds}') group by dcr.task_id")
    public List<Map<String, Object>> taskStats(@Param("taskIds") String taskIds);

    @Select("select dcr.area_idx, dcr.qs_idx, sum(dcr.scored) as total_score, sum(dcr.is_correct) as correct_num from doc_correct_result dcr where dcr.deleted = 0 and (#{taskId} is null or dcr.task_id = #{taskId}) and (#{configId} is null or dcr.config_id = #{configId}) group by dcr.area_idx, dcr.qs_idx order by dcr.area_idx, dcr.qs_idx")
    public List<DocCorrectResultDTO> selectQsStats(@Param("taskId") String taskId, @Param("configId") String configId);

    @Select("SELECT COUNT(1) AS taskCount " +
            "FROM doc_correct_task dct " +
            "WHERE dct.deleted = 0 " +
            "AND dct.create_time = #{todayDate}")  // 按今天日期过滤
    public Integer countTodayTasksByUser(@Param("userId") String userId, @Param("todayDate") Date todayDate);

    @Select("SELECT DISTINCT task_id FROM doc_correct_record")
    List<String> getDistinctTaskIds();
}
