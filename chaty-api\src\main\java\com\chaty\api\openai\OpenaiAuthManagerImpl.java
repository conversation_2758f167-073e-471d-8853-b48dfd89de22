package com.chaty.api.openai;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;
import java.util.Set;
import java.util.concurrent.ConcurrentLinkedQueue;

import javax.annotation.PostConstruct;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import com.chaty.enums.AIModelConsts;
import com.chaty.exception.BaseException;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
@ConfigurationProperties(prefix = "api.openai")
public class OpenaiAuthManagerImpl implements OpenaiAuthManager {

    /**
     * 限流时间
     */
    @Setter
    private long ratelimitTime = 60 * 1000;

    /**
     * 未授权时间
     */
    @Setter
    private long unauthorizedTime = 60 * 60 * 1000;

    /**
     * 降级模型
     */
    @Setter
    private String fallbackModel = AIModelConsts.GPT_4O_MINI;

    @Setter
    private List<OpenaiAuthProperties> proxyed;

    /**
     * 模型授权
     */
    private Map<String, Queue<OpenaiAuth>> modelAuth = new HashMap<>();

    @PostConstruct
    public void init() {
        log.info("初始化 OpenAI 授权: {}", proxyed);
        Map<String, Queue<OpenaiAuth>> authMap = new HashMap<>();
        proxyed.forEach(proxy -> {
            Set<String> models = proxy.getModels();
            for (String model : models) {
                Queue<OpenaiAuth> keyQueue = authMap.get(model);
                if (Objects.isNull(keyQueue)) {
                    keyQueue = new ConcurrentLinkedQueue<>();
                    authMap.put(model, keyQueue);
                }
                Set<String> keys = proxy.getKeys();
                for (String key : keys) {
                    keyQueue.offer(new OpenaiAuth(key, proxy.getUrl(), model));
                }
            }
        });
        this.modelAuth = authMap;
        log.info("OpenAI 授权初始化成功!");
    }

    @Override
    public synchronized OpenaiAuth nextApiKey(String model) {
        Queue<OpenaiAuth> authQueue = modelAuth.get(model);
        OpenaiAuth auth = null;
        if (Objects.nonNull(authQueue)) {
            for (int i = 0; i < authQueue.size(); i++) {
                auth = authQueue.poll();
                if (auth.verify()) {
                    authQueue.offer(auth);
                    return auth;
                }
            }
        }

        // 所有的 apikey 都被降级，通过降级模型请求
        if (Objects.equals(fallbackModel, model)) {
            log.info("模型 {} 可用的 key 为空，使用降级模型 {}", model, fallbackModel);
            return nextApiKey(fallbackModel);
        } else {
            throw new BaseException("模型 {} 无可用的 key");
        }
    }

    @Override
    public void onRateLimited(String key, String model) {
        degradeAuth(key, model, ratelimitTime);
    }

    @Override
    public void onUnauthorized(String key, String model) {
        degradeAuth(key, model, unauthorizedTime);
    }

    private void degradeAuth(String key, String model, long time) {
        Queue<OpenaiAuth> queue = modelAuth.get(model);
        queue.forEach(auth -> {
            if (Objects.equals(auth.getKey(), key)) {
                auth.setValidTime(System.currentTimeMillis() + time);
            }
        });
    }

}
