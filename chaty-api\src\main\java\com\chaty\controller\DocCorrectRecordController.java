package com.chaty.controller;

import java.io.IOException;
import java.sql.Wrapper;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.chaty.completion.CompletionService;
import com.chaty.dto.ChatCompletionDTO;
import com.chaty.dto.DocCorrectRecordAdjustDTO;
import com.chaty.dto.UpdateOffsetReq;
import com.chaty.entity.DocCorrectRecord;
import com.chaty.mapper.DocCorrectRecordMapper;
import com.chaty.util.FileUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.common.BaseResponse;
import com.chaty.dto.DocCorrectRecordDTO;
import com.chaty.service.DocCorrectRecordService;

@RequestMapping("/api/docCorrectRecord")
@RestController
public class DocCorrectRecordController {

    private static final Log log = LogFactory.getLog(DocCorrectRecordController.class);
    @Resource
    public DocCorrectRecordService docCorrectRecordService;
    @Resource
    public CompletionService completionService;
    @Autowired
    private DocCorrectRecordMapper docCorrectRecordMapper;
    @Resource
    private FileUtil fileUtil;

    @PostMapping("/page")
    public BaseResponse<?> page(@RequestBody DocCorrectRecordDTO param) {
        IPage<DocCorrectRecordDTO> res = docCorrectRecordService.page(param);
        return BaseResponse.ok("查询成功", res);
    }

    @PostMapping("/add")
    public BaseResponse<?> add(@RequestBody DocCorrectRecordDTO param) {
        docCorrectRecordService.add(param);
        return BaseResponse.ok("添加成功");
    }

    @PostMapping("/update")
    public BaseResponse<?> update(@RequestBody DocCorrectRecordDTO param) {
        param = docCorrectRecordService.checkHasChange(param);
        docCorrectRecordService.update(param);
        return BaseResponse.ok("修改成功");
    }



    @PostMapping("/delete")
    public BaseResponse<?> delete(String id) {
        docCorrectRecordService.delete(id);
        return BaseResponse.ok("删除成功");
    }

    @PostMapping("/download/reviewed")
    public BaseResponse<?> downloadReviewed(@RequestBody DocCorrectRecordDTO param) {
        Map<String, Object> res = docCorrectRecordService.createReviewedDoc(param);
        return BaseResponse.ok(res);
    }

    @PostMapping("/download/essayReport")
    public BaseResponse<?> downloadEssayReport(@RequestBody DocCorrectRecordDTO param) {
        Map<String, Object> res = docCorrectRecordService.createEssayReport(param);
        return BaseResponse.ok(res);
    }

    @PostMapping("/download/essayAnalyticalReport")
    public BaseResponse<?> essayAnalyticalReport(@RequestBody DocCorrectRecordDTO param) {
        Map<String, Object> res = docCorrectRecordService.createEssayAnalyticalReport(param);
        return BaseResponse.ok(res);
    }

    /**
     * 一键纠正
     *
     * @param taskId
     * @return
     */
    @GetMapping("/qs/correctbyBatch")
    public BaseResponse<?> correctQs(String taskId, Boolean isUpdateHasChanged, Integer qsIdx, Boolean isRight) {
        // 打印入参
        log.info("taskId: " + taskId + ", isUpdateHasChanged: " + isUpdateHasChanged + ", qsIdx: " + qsIdx + ", isRight: " + isRight);
        Integer cnt = docCorrectRecordService.correctQs(taskId, isUpdateHasChanged, qsIdx, isRight);
        return BaseResponse.ok("批改成功", cnt);
    }

    @GetMapping("/area/getChatCompletionDTO")
    public BaseResponse<?> getChatCompletionDTO(@RequestParam("recordId") String recordId,
                                                @RequestParam("areaIdx") Integer areaIdx,
                                                @RequestParam(value = "modal", required = false) String modal) {
        JSONObject res = completionService.getChatCompletionDTO(recordId, areaIdx, modal);
        log.info("getChatCompletionDTO " + res);
        return BaseResponse.ok("获取成功", res);
    }

    @GetMapping("/ration180")
    public BaseResponse<?> correctQs(@RequestParam("id") String id) throws IOException {
        if (StrUtil.isBlank(id)) {
            return BaseResponse.error("id不能为空");
        }
        DocCorrectRecord dbDocCorrectRecord = docCorrectRecordMapper.selectById(id);
        if (Objects.isNull(dbDocCorrectRecord)) {
            return BaseResponse.error("未查到数据");
        }
        if (Boolean.TRUE.equals(dbDocCorrectRecord.getNeedRation())) {
            dbDocCorrectRecord.setNeedRation(false);
            dbDocCorrectRecord.setDocurl(dbDocCorrectRecord.getOriginPdfUrl());
            dbDocCorrectRecord.setOriginPdfUrl(null);
        } else if(Boolean.FALSE.equals(dbDocCorrectRecord.getNeedRation())) {
            dbDocCorrectRecord.setNeedRation(true);
            String rationPdfUrl = fileUtil.rotatePdf180(dbDocCorrectRecord.getDocurl());
            dbDocCorrectRecord.setOriginPdfUrl(dbDocCorrectRecord.getDocurl());
            dbDocCorrectRecord.setDocurl(rationPdfUrl);
        }
        docCorrectRecordMapper.updateById(dbDocCorrectRecord);
        return BaseResponse.ok("批改成功", dbDocCorrectRecord);
    }
}