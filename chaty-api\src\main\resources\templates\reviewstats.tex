\documentclass{article}
\usepackage[utf8]{inputenc}
\usepackage{ctex}

\title{${reviewContext.docname}}
\author{SolveGPT}
\date{\today}

\begin{document}

\maketitle

<#list reviewContext.questions as question>

<#if question.name?has_content>
\section{${question.name}}
<#else>
\section{${"题目" + (question?index + 1)}}
</#if>
\small{${question.question}}

\small{${question.correctAnswer}}

<#if reviewedRes[question?index]?has_content>
<#assign questionData = reviewedRes[question?index]>
<#assign count = questionData.count>
<#assign correct = questionData.correct!0>

${"\\Large{批改作业$" + count + "$份，正确份数$" + correct + "$份，正确率$" + (correct/count*100)?string("0.00") + "\\%$.}"}
<#else>
\Large{批改作业$0$份，正确份数$0$份，正确率$100%$.}
</#if>

</#list>

\end{document}