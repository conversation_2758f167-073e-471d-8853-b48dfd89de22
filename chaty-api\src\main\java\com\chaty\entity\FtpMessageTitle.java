package com.chaty.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("ftp_message_title")
public class FtpMessageTitle {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    private String title;
    
    private String remark;
    
    private String path;
    
    private Integer weight;
    
    private LocalDateTime createTime;
    
    private LocalDateTime updateTime;
} 