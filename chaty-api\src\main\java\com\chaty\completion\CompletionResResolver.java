package com.chaty.completion;

import java.util.List;
import java.util.Map;

import cn.hutool.json.JSONArray;
import com.chaty.dto.CorrectQsDTO;
import com.chaty.dto.DocCorrectRecordDTO;

import cn.hutool.json.JSONObject;
import com.chaty.dto.PaperTopicDTO;
import com.tencentcloudapi.ecc.v20181213.models.CorrectData;

public interface CompletionResResolver {

    JSONObject resolveDocAreaRes(DocCorrectRecordDTO record, Map<String, Object> completionRes);

    JSONObject resolveDocAreaResponseFormatRes(DocCorrectRecordDTO record, Map<String, Object> completionRes);
    JSONObject resolveDocAreaResponseFirstRequestRes(DocCorrectRecordDTO record, Map<String, Object> completionRes);

    boolean isSupported(String aimodel, Boolean jsonSchema);

    List<CorrectQsDTO> resolveRxtraQsRes(Map<String, Object> completionRes);

    List<CorrectQsDTO> resolveExtraQsResWithRespFormat(Map<String, Object> completionRes);

    JSONObject resolveDocWriteQsRes(DocCorrectRecordDTO record, Map<String, Object> completionRes, Integer allScore, CorrectData tencentCorrectData, CorrectData tencentCorrectDataRewrite);
    JSONObject resolveDocWriteQsHuaShiRes(DocCorrectRecordDTO record, Integer allScore, JSONObject huaShiApiResult);

    String resolveEssayAnalyticalReportDocRes(Map<String, Object> completionRes);

    JSONObject resolveScoreRes(DocCorrectRecordDTO record, Map<String, Object> completionRes, Float allScore);

    String resolveStudentNameRes(Map<String, Object> completionRes);

    String resolveStudentNumberRes(Map<String, Object> completionRes);

    PaperTopicDTO resolvePaperTopicRes(Map<String, Object> completionRes);

    static void getNormalQsResponse(JSONArray reviewed, JSONObject qsResObj) {
        JSONObject qsReviewed = new JSONObject();
        if (qsResObj.containsKey("学生答案")) {
            qsReviewed.set("studentAnswer", qsResObj.getStr("学生答案"));
        }
        if (qsResObj.containsKey("是否正确")) {
            qsReviewed.set("isCorrect", qsResObj.getBool("是否正确") ? "Y" : "N");
        }
        if (qsResObj.containsKey("评价")) {
            qsReviewed.set("review", qsResObj.getStr("评价"));
        }
        if (qsResObj.containsKey("可信度")) {
            String credStr = qsResObj.getStr("可信度");
            float credibility;
            try {
                credibility = Float.parseFloat(credStr);
            } catch (NumberFormatException e) {
                credibility = 1f;
            }
            qsReviewed.set("credibility", credibility);
        }
        reviewed.add(qsReviewed);
    }
}
