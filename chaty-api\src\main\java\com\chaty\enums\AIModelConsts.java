package com.chaty.enums;

import java.util.Set;

import cn.hutool.core.collection.CollUtil;

public interface AIModelConsts {

    String GPT_4_V = "gpt-4-vision-preview";

    String GPT_4O = "gpt-4o";

    String GPT_4O_MINI = "gpt-4o-mini";

    String GPT_4O_20240806 = "gpt-4o-2024-08-06";

    String GPT_4O_20240806_3 = "gpt-4o-2024-08-06_3";  // 批改模型，三倍批改

    String GEMINI_15_PRO = "gemini-1.5-pro";

    String GEMINI_15_FLASH = "gemini-1.5-flash";

    String GEMINI_20_FALSH_EXP = "gemini-2.0-flash-exp";

    String doubao_15_pro = "doubao-1-5-vision-pro-32k-250115";

//    String doubao_15 = "doubao-1-5-pro-32k-250115";

    String doubao_15_pro_25032 = "doubao-1.5-vision-pro-250328";


    String doubao_1_5_thinking_pro_m_250415 = "doubao-1-5-thinking-pro-m-250415";

    String doubao_1_5_thinking_pro_250415 = "doubao-1-5-thinking-pro-250415";

    String doubao_vision_pro_32k_241028 = "doubao-vision-pro-32k-241028";

    String doubao_vision_lite_32k_241015 = "doubao-vision-lite-32k-241015";

    String doubao_15_ui_tars_250328 = "doubao-1.5-ui-tars-250328";

    String doubao_embedding_vision_250328 = "doubao-embedding-vision-250328";

    String doubao_15_vision_lite_250315 = "doubao-1.5-vision-lite-250315";

    String doubao_15_thinking_vision_pro_250428 = "doubao-1-5-thinking-vision-pro-250428";

    String doubao_Seed_16_thinking = "doubao-seed-1-6-thinking-250615";

    String doubao_Seed_16 = "doubao-seed-1-6-250615";

    String doubao_Seed_16_flash = "doubao-seed-1-6-flash-250615";



    /**
     * 这是创建和解析的
     */
    Set<String> visionModels = CollUtil.newHashSet( GPT_4_V, GPT_4O, GPT_4O_MINI, GPT_4O_20240806, GPT_4O_20240806_3, GEMINI_15_PRO, GEMINI_15_FLASH, GEMINI_20_FALSH_EXP, "Claude 3 Opus", "Claude 3 Sonnet");



    Set<String> creatorImageDouBaoModels = CollUtil.newHashSet(doubao_15_pro,  doubao_15_pro_25032, doubao_1_5_thinking_pro_m_250415, doubao_1_5_thinking_pro_250415, doubao_vision_pro_32k_241028, doubao_vision_lite_32k_241015,
            doubao_15_ui_tars_250328,doubao_embedding_vision_250328,doubao_15_vision_lite_250315,doubao_15_thinking_vision_pro_250428,doubao_Seed_16_thinking,doubao_Seed_16,doubao_Seed_16_flash);

    //doubao_1.5-vision-pro-32k-250115 默认用function calling模式
    Set<String> functionCallModels = CollUtil.newHashSet("gpt-4", "gpt-3.5-turbo", "gpt-4-turbo", "ERNIE-Bot");

    Set<String> aiReviewModels = CollUtil.newHashSet("gpt-4", "gpt-3.5-turbo", "gpt-4-turbo", "ERNIE-Bot", doubao_15_pro, doubao_15_pro_25032,doubao_1_5_thinking_pro_m_250415, doubao_1_5_thinking_pro_250415, doubao_vision_pro_32k_241028, doubao_vision_lite_32k_241015,
            doubao_15_ui_tars_250328,doubao_embedding_vision_250328,doubao_15_vision_lite_250315,doubao_15_thinking_vision_pro_250428,doubao_Seed_16_thinking,doubao_Seed_16,doubao_Seed_16_flash);

    /**
     * 使用function_call解析的豆包模型
     */
    Set<String> douBaoResolveModels = CollUtil.newHashSet();

    /**
     * 不使用function_call，直接从回答里解析
     */
    Set<String> douBaoJSONModels = CollUtil.newHashSet(doubao_15_pro_25032,  doubao_15_pro,doubao_1_5_thinking_pro_m_250415, doubao_1_5_thinking_pro_250415, doubao_vision_pro_32k_241028, doubao_vision_lite_32k_241015,
            doubao_15_ui_tars_250328,doubao_embedding_vision_250328,doubao_15_vision_lite_250315,doubao_15_thinking_vision_pro_250428,doubao_Seed_16_thinking,doubao_Seed_16,doubao_Seed_16_flash);

    /**
     * 使用function_call，直接要求回答json格式
     */
    Set<String> douBaoAskFromFunctionCallModels = CollUtil.newHashSet();

    Set<String> claudeModels = CollUtil.newHashSet("Claude 3 Opus", "Claude 3 Sonnet");

    Set<String> openaiVisionModels = CollUtil.newHashSet(GPT_4O, GPT_4_V, GPT_4O_MINI, GPT_4O_20240806, GPT_4O_20240806_3, GEMINI_15_PRO, GEMINI_15_FLASH, GEMINI_20_FALSH_EXP);

    String GPT_DEFAULT = "doubao-1-5-vision-pro-32k-250115";

    String GPT_DEFAULT_LABEL = "doubao-1-5-vision-pro-32k-250115(直接输出json)";

}
