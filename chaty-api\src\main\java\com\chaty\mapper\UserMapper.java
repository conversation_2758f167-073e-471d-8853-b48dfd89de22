package com.chaty.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chaty.dto.UserDTO;
import com.chaty.entity.User;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface UserMapper extends BaseMapper<User> {

    @Insert("insert into user (id, username, email, nickname, password, status, class_id, student_id) values (#{id}, #{username}, #{email}, #{nickname}, #{password}, #{status}, #{classId}, #{studentId})")
    int insert(User entity);

    @Update("update user set nickname = #{nickname} , student_id = #{studentId} where id = #{id}")
    int updateByNicknameAndStudentId(String nickname, String studentId, String id);

    User selectById(String id);

    List<User> list(User params);

    int deleteById(String id);

    @Select("select * from user where deleted = 0 and (username = #{username} or email = #{username})")
    User selectByUsername(String username);

    @Select("select * from user where deleted = 0 and class_id = #{classId}")
    List<User> selectByClassId(String classId);

    @Select("select role, count(*) as count from user where deleted = 0 group by role")
    List<UserDTO> userCount();

    /**
     * 统计某个班级下未删除用户的去重nickname数量
     */
    @Select("SELECT COUNT(DISTINCT nickname) FROM user WHERE class_id = #{classId} AND deleted = 0")
    int countDistinctNicknameByClassId(String classId);

}
