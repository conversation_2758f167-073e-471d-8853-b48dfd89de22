package com.chaty.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.chaty.dto.ClassInfoDTO;
import com.chaty.dto.SchoolDTO;
import com.chaty.dto.SchoolUserDTO;
import com.chaty.entity.SchoolUser;

@Mapper
public interface SchoolUserMapper extends BaseMapper<SchoolUser> {

    @Select("select su.*, s.school_name, u.username from school_user su left join school s on su.school_id = s.school_id left join user u on su.user_id = u.id ${ew.customSqlSegment}")
    IPage<SchoolUserDTO> findPage(IPage<?> page, @Param(Constants.WRAPPER) Wrapper<?> wrapper);

    @Select("select s.* from school_user su left join school s on su.school_id = s.school_id ${ew.customSqlSegment}")
    List<SchoolDTO> getSchoolList(@Param(Constants.WRAPPER) Wrapper<?> wrapper);

    @Select("SELECT u.nickname, c.class_name, s.school_name FROM user u JOIN class c ON u.class_id = c.class_id JOIN school s ON c.school_id = s.school_id WHERE u.nickname = #{nickname}")
    List<ClassInfoDTO> selectClassByNickname(@Param("nickname") String nickname);

}
