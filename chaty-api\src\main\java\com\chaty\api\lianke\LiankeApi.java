package com.chaty.api.lianke;

import cn.hutool.json.JSONObject;
import feign.Headers;
import feign.Param;
import feign.RequestLine;

import java.io.File;

public interface LiankeApi {

    @RequestLine("POST /api/print/job")
    @Headers({
            "Content-Type: multipart/form-data",
            "ApiKey: {apiKey}"
    })
    LiankeBaseResponse<PrintJobResponse> printJob(@Param("apiKey") String apiKey, PrintJobRequest printJobRequest);


    @RequestLine("GET /api/external_api/printer_list?deviceId={deviceId}&deviceKey={deviceKey}&printerType=1")
    @Headers({
            "Content-Type: application/json",
            "ApiKey: {apiKey}"
    })
    LiankeBaseResponse<JSONObject> printerList(@Param("deviceKey") String deviceKey, @Param("deviceId") String deviceId, @Param("apiKey") String apiKey);

    @RequestLine("GET /api/print/printer_params?printerModel={printerModel}")
    @Headers({
            "Content-Type: application/json",
            "ApiKey: {apiKey}"
    })
    LiankeBaseResponse<JSONObject> printerParams(@Param("printerModel") String printerModel, @Param("apiKey") String apiKey);
}
