package com.chaty.api.lianke;

import cn.hutool.json.JSONObject;

import java.util.List;
import java.util.Map;

public class LiankeLoginResponse {
    private LiankeBaseResponse<JSONObject> data;
    private Map<String, List<String>> headers;

    // getter 和 setter
    public LiankeBaseResponse<JSONObject> getData() {
        return data;
    }

    public void setData(LiankeBaseResponse<JSONObject> data) {
        this.data = data;
    }

    public Map<String, List<String>> getHeaders() {
        return headers;
    }

    public void setHeaders(Map<String, List<String>> headers) {
        this.headers = headers;
    }
}
