package com.chaty.service.impl;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.chaty.completion.CompletionService;
import com.chaty.dto.*;
import com.chaty.entity.*;
import com.chaty.mapper.DocCorrectFileMapper;
import com.chaty.tenant.IgnoreTenant;
import com.chaty.service.PrinterService;
import com.chaty.service.RemoteFileService;
import com.chaty.util.FileUtil;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.enums.CorrectEnums.CorrectRecordStatus;
import com.chaty.enums.UserRoleConsts;
import com.chaty.mapper.DocCorrectConfigMapper;
import com.chaty.mapper.DocCorrectRecordMapper;
import com.chaty.mapper.DocCorrectTaskMapper;
import com.chaty.security.AuthUtil;
import com.chaty.service.DocCorrectRecordService;
import com.chaty.service.PDFService;
import com.chaty.service.PDFService.TexCmd;
import com.chaty.task.correct.CorrectCacheService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class DocCorrectRecordServiceImpl extends ServiceImpl<DocCorrectRecordMapper, DocCorrectRecord>
        implements DocCorrectRecordService {

    @Resource
    private DocCorrectRecordMapper docCorrectRecordMapper;
    @Resource
    private PDFService pdfService;
    @Resource
    private DocCorrectTaskMapper docCorrectTaskMapper;
    @Resource
    private DocCorrectConfigMapper docCorrectConfigMapper;
    @Resource
    private CorrectCacheService correctCacheService;
    @Resource
    private RemoteFileService remoteFileService;
    @Resource
    private PrinterService printerService;
    @Resource
    private CompletionService completionService;
    private DocCorrectFileMapper docCorrectFileMapper;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public void add(DocCorrectRecordDTO param) {
        docCorrectRecordMapper.insert(param);
    }

    @Override
    public void delete(String id) {
        docCorrectRecordMapper.deleteById(id);
    }

    @Override
    public IPage<DocCorrectRecordDTO> page(DocCorrectRecordDTO param) {
        if (Objects.isNull(param.getPage())) {
            PageDTO<DocCorrectRecord> page = new PageDTO<>();
            page.setPageSize(9999);
            page.setPageNumber(1);
            page.setSearchCount(false);
            param.setPage(page);
        }
        LambdaQueryWrapper<DocCorrectRecord> queryWrapper = Wrappers.<DocCorrectRecord>lambdaQuery()
                .eq(Objects.nonNull(param.getTaskId()), DocCorrectRecord::getTaskId, param.getTaskId())
                .like(StrUtil.isNotBlank(param.getDocname()), DocCorrectRecord::getDocname, param.getDocname())
                .eq(Objects.nonNull(param.getHasChange()), DocCorrectRecord::getHasChange, param.getHasChange())
                .orderByDesc(Boolean.TRUE.equals(param.getOrderByTaskId()), DocCorrectRecord::getTaskId)
                .orderByAsc(DocCorrectRecord::getDocname);
        return docCorrectRecordMapper.selectPage(param.getPage().page(), queryWrapper)
                .convert(r -> {
                    Map<String, Object> recordMap = correctCacheService.getRecordCache(r.getId());
                    DocCorrectRecordDTO dto = BeanUtil.copyProperties(r, DocCorrectRecordDTO.class);
                    if (recordMap != null) {
                        dto.setAreaNum((Integer) recordMap.get("areaNum"));
                        dto.setAreaCorrected((Integer) recordMap.get("areaCorrected"));
                    }
                    return dto;
                });
    }

    @Override
    public void update(DocCorrectRecordDTO param) {
        docCorrectRecordMapper.updateById(param);
    }

    @Override
    public DocCorrectRecordDTO checkHasChange(DocCorrectRecordDTO param) {
        if (Objects.isNull(param.getTaskId()) || StrUtil.isBlank(param.getReviewed())) {
            return param;
        }
        try {
            int recordHasChange = 0;
            JSONArray areas = JSONUtil.parseArray(param.getReviewed());
            for (int i = 0; i < areas.size(); i++) {
                JSONObject area = areas.getJSONObject(i);
                JSONArray qsReviewed = area.getJSONArray("qsReviewed");
                for (int j = 0; j < qsReviewed.size(); j++) {
                    JSONObject qs = qsReviewed.getJSONObject(j);
                    int hasChange = (int) qs.getOrDefault("hasChange", 0);
                    if (hasChange == 1) {
                        recordHasChange = 1;
                        break;
                    }
                }
                if (recordHasChange == 1) {
                    break;
                }
            }
            param.setHasChange(recordHasChange);
            return param;
        } catch (Exception e) {
            return param;
        }
    }


    @Override
    public void setRecordWait(List<String> ids, String taskId) {
        Wrapper<DocCorrectRecord> wrapper = Wrappers.<DocCorrectRecord>lambdaUpdate()
                .in(CollUtil.isNotEmpty(ids), DocCorrectRecord::getId, ids)
                .eq(StrUtil.isNotBlank(taskId), DocCorrectRecord::getTaskId, taskId);
        DocCorrectRecord update = new DocCorrectRecord();
        update.setStatus(CorrectRecordStatus.WAIT);
        int updateCount = docCorrectRecordMapper.update(update, wrapper);
    }

    // 任务背景：一键重测多个任务 会调用多次一键重测接口，导致每份试卷都进行了任务发布，形成了一个高并发的场景
    // 批改试卷是通过发布Event(发布event之前已经将doc_correct_record表的status改为wait)触发MessageProducer的onRecordCorrect方法（async 防止阻塞主线程），然后调用该方法进行每个子试卷的发布
    // 这里查询所有status为wait的试卷进行返回（需要加锁，不让同时多任务，会导致任务重复被加入到队伍处理队列），返回后的数据会加入redis的批改队列
    @Transactional
    @Override
    @IgnoreTenant
    public List<DocCorrectRecord> listWait(Integer limit) {
        String lockKey = "doc_correct_task:lock";
        String requestId = UUID.randomUUID().toString();
        int maxRetries = 1000; // 最大重试次数
        long retryInterval = 500; // 重试间隔(毫秒)

        for (int i = 0; i < maxRetries; i++) {
            try {
                // 尝试获取锁，设置5秒过期防止死锁
                Boolean locked = redisTemplate.opsForValue().setIfAbsent(lockKey, requestId, 500, TimeUnit.SECONDS);

                if (locked) {
                    // 获取锁成功，执行任务处理
                    try {
                        return doListWait(limit);
                    } finally {
                        // 释放锁
                        if (requestId.equals(redisTemplate.opsForValue().get(lockKey))) {
                            redisTemplate.delete(lockKey);
                        }
                    }
                }

                // 获取锁失败，等待后重试
                if (i < maxRetries - 1) {
                    log.info("获取锁失败，等待{}ms后重试 ({}/{})", retryInterval, i+1, maxRetries);
                    Thread.sleep(retryInterval);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("获取锁过程被中断", e);
                break;
            }
        }

        log.warn("达到最大重试次数，获取锁失败");
        return Collections.emptyList();
    }

    private List<DocCorrectRecord> doListWait(Integer limit) {
        // 原方法的核心逻辑
        Wrapper<DocCorrectRecord> wrapper = Wrappers.<DocCorrectRecord>lambdaQuery()
                .eq(DocCorrectRecord::getStatus, CorrectRecordStatus.WAIT)
                .orderByAsc(DocCorrectRecord::getCreateTime)
                .last(Objects.nonNull(limit), "limit " + limit);
        List<DocCorrectRecord> records = docCorrectRecordMapper.selectList(wrapper);

        if (records != null && !records.isEmpty()) {
            List<String> updateRecords = records.stream().map(DocCorrectRecord::getId).collect(Collectors.toList());
            LambdaUpdateWrapper<DocCorrectRecord> updateWrapper = Wrappers.<DocCorrectRecord>lambdaUpdate()
                    .in(DocCorrectRecord::getId, updateRecords)
                    .eq(DocCorrectRecord::getStatus, CorrectRecordStatus.WAIT)
                    .set(DocCorrectRecord::getStatus, CorrectRecordStatus.PROCESSING);
            docCorrectRecordMapper.update(updateWrapper);
        }

        return records;
    }

    @Override
    public void reset2Wait() {
        LambdaUpdateWrapper<DocCorrectRecord> wrapper = Wrappers.<DocCorrectRecord>lambdaUpdate()
                .eq(DocCorrectRecord::getStatus, CorrectRecordStatus.PROCESSING)
                .set(DocCorrectRecord::getStatus, CorrectRecordStatus.WAIT);
        docCorrectRecordMapper.update(wrapper);
    }

    @Override
    public Map<String, Object> createReviewedDoc(DocCorrectRecordDTO param) {
        Boolean isBatch = param.getIsBatch();
        Boolean isPreview = param.getIsPreview();
        String taskId = param.getTaskId();
        String recordId = param.getId();
        String recordName = "";
        log.info("createReviewedDoc, taskId: {}, recordId: {}, isBatch: {}, isPreview: {}",
                taskId, recordId, isBatch, isPreview);

        // 查询批改记录
        DocCorrectTaskDTO task = Optional.ofNullable(docCorrectTaskMapper.selectById(taskId))
                .map(t -> BeanUtil.copyProperties(t, DocCorrectTaskDTO.class))
                .orElseThrow(() -> new RuntimeException("未查询到批改任务信息"));
        recordName = task.getName();
        DocCorrectConfigDTO config = Optional.ofNullable(docCorrectConfigMapper.selectById(task.getConfigId()))
                .map(c -> BeanUtil.copyProperties(c, DocCorrectConfigDTO.class))
                .orElseThrow(() -> new RuntimeException("未查询到试卷配置信息"));
        List<DocCorrectRecordDTO> records = listByIdOrTaskId(recordId, taskId);
        records.forEach(r -> {
            r.setConfig(config);
            r.setTask(task);
        });

        JSONObject configObj  = config.getConfigObj();
        JSONArray scoreTypes = new JSONArray();
        if (configObj.get("scoreTypes") != null) {
            scoreTypes = configObj.getJSONArray("scoreTypes");
        }
        // 生成批改结果
        Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>())
                .put("records", records)
                .put("pageNum", 1)
                .put("isPreview", isPreview)
                .put("mergeScore", false)
                .put("showQsScore", param.getShowQsScore())
                .put("allScoreTypes", scoreTypes.toList(String.class))
                .build();
        Map<String, Object> res = pdfService.createDoc(TexCmd.PDFLATEX, "doccorrect", params);
        // 保存到远程文件夹
        save2Remote(String.format("%s 批改结果(%s).pdf", recordName, param.getIsPreview() ? "含原卷" : "不含原卷"), res, param.getRemoteFileProps());
        // 远程打印
        doPrint(res, param.getPrinterProps());
        return res;
    }

    @Override
    public Map<String, Object> createEssayReport(DocCorrectRecordDTO param) {
        String taskId = param.getTaskId();
        String recordId = param.getId();
        String recordName = "";


        // 查询批改记录
        DocCorrectTaskDTO task = Optional.ofNullable(docCorrectTaskMapper.selectById(taskId))
                .map(t -> BeanUtil.copyProperties(t, DocCorrectTaskDTO.class))
                .orElseThrow(() -> new RuntimeException("未查询到批改任务信息"));
        recordName = task.getName();
        List<DocCorrectRecordDTO> records = listByIdOrTaskId(recordId, taskId);
        List<EssayWordDTO> params = new ArrayList<>();
        // 获取配置
        DocCorrectConfigDTO config = Optional.ofNullable(docCorrectConfigMapper.selectById(task.getConfigId()))
                .map(c -> BeanUtil.copyProperties(c, DocCorrectConfigDTO.class))
                .orElseThrow(() -> new RuntimeException("未查询到试卷配置信息"));
        JSONArray areas = config.getAreasObj();
        Float score = 0F;
        for (int areaIdx = 0; areaIdx < areas.size(); areaIdx++) {
            JSONObject areaObj = areas.getJSONObject(areaIdx);
            Integer areaType = areaObj.getInt("areaType", 1);
            if (areaType == 3) {
                JSONArray questions = areaObj.getJSONArray("questions");
                if (!questions.isEmpty()) {
                    JSONObject qs = questions.getJSONObject(0);
                    score = qs.getFloat("score", 0F);
                }
            }
        }
        JSONObject essayConfigObj  = null;
        if (!areas.isEmpty()) {
            JSONArray questions = areas.getJSONObject(0).getJSONArray("questions");
            if (!questions.isEmpty()) {
                essayConfigObj = questions.getJSONObject(0).getJSONObject("qsInfo");
            }
        }
        String gradeName = "初一";
        String essayTitle = "";
        try {
            if (essayConfigObj != null) {
                gradeName = essayConfigObj.getStr("grade");
            }
            if (StrUtil.isBlank(gradeName)) {
                gradeName = "初一";
            }
        } catch (Exception e) {
            log.error("获取班级名称失败", e);
        }
        try {
            if (essayConfigObj != null) {
                essayTitle = essayConfigObj.getStr("essayTitle");
            }
        } catch (Exception e) {
            log.error("获取班级名称失败", e);
        }
        if (score == 0F) {
            throw new RuntimeException("未查询到作文分数");
        }
        Float finalScore = score;
        String finalGradeName = gradeName;
        String finalEssayTitle = essayTitle;
        records.forEach(r -> {
            EssayWordDTO dto = new EssayWordDTO();
            dto.initByRecord(r, finalScore, finalGradeName, finalEssayTitle);
            params.add(dto);
        });
        // 生成批改结果
        Map<String, Object> res = pdfService.createEssayReport(params);
        // 保存到远程文件夹
        save2Remote(String.format("%s 批改结果(%s).pdf", recordName, "作文报告"), res, param.getRemoteFileProps());
        // 远程打印
        doPrint(res, param.getPrinterProps());
        return res;
    }

    @Override
    public Map<String, Object> createEssayAnalyticalReport(DocCorrectRecordDTO param) {
        String taskId = param.getTaskId();
        String recordId = param.getId();
        String recordName = "";

        // 查询批改记录
        DocCorrectTaskDTO task = Optional.ofNullable(docCorrectTaskMapper.selectById(taskId))
                .map(t -> BeanUtil.copyProperties(t, DocCorrectTaskDTO.class))
                .orElseThrow(() -> new RuntimeException("未查询到批改任务信息"));
        recordName = task.getName();
        List<DocCorrectRecordDTO> records = listByIdOrTaskId(recordId, taskId);
        List<EssayWordDTO> params = new ArrayList<>();
        // 获取配置
        DocCorrectConfigDTO config = Optional.ofNullable(docCorrectConfigMapper.selectById(task.getConfigId()))
                .map(c -> BeanUtil.copyProperties(c, DocCorrectConfigDTO.class))
                .orElseThrow(() -> new RuntimeException("未查询到试卷配置信息"));
        JSONArray areas = config.getAreasObj();
        param.setConfig(config);
        JSONObject essayConfigObj  = null;
        if (!areas.isEmpty()) {
            JSONArray questions = areas.getJSONObject(0).getJSONArray("questions");
            if (!questions.isEmpty()) {
                essayConfigObj = questions.getJSONObject(0).getJSONObject("qsInfo");
            }
        }
        String gradeName = "初一";
        try {
            if (essayConfigObj != null) {
                gradeName = essayConfigObj.getStr("grade");
            }
            if (StrUtil.isBlank(gradeName)) {
                gradeName = "初一";
            }
        } catch (Exception e) {
            log.error("获取班级名称失败", e);
        }
        // 得分情况设置
        String scoreSituation = "| 分数档  | 学生人数 |\n" +
                "| ------------- | ------------- |\n" +
                "| 80-100分  | %s  |\n" +
                "| 60-79分  | %s  |\n" +
                "| 40-59分  | %s  |\n" +
                "| 20-39分  | %s  |\n" +
                "| 0-19分  | %s  |\n" +
                "\n" +
                "- **平均分：** %s\n" +
                "- **中位数：** %s\n" +
                "- **方差：** %s\n";
        List<Float> scores = new ArrayList<>();
        for(DocCorrectRecordDTO record : records) {
            JSONArray reviewedObj = record.getReviewedObj();
            if (reviewedObj != null && !reviewedObj.isEmpty()) {
                JSONArray reviewed = reviewedObj.getJSONObject(0).getJSONArray("reviewed");
                JSONObject reviewed0 = reviewed.getJSONObject(0);
                Float score = reviewed0.getFloat("scored", 0F);
                scores.add(score);
            }
        }
        if (!scores.isEmpty()) {
            // 计算平均分
            float sum = 0F;
            for (Float score : scores) {
                sum += score;
            }
            float avg = sum / scores.size();

            // 计算中位数
            List<Float> sortedScores = new ArrayList<>(scores);
            Collections.sort(sortedScores);
            float median;
            int size = sortedScores.size();
            if (size % 2 == 0) {
                median = (sortedScores.get(size / 2 - 1) + sortedScores.get(size / 2)) / 2;
            } else {
                median = sortedScores.get(size / 2);
            }

            // 计算方差
            float varianceSum = 0F;
            for (Float score : scores) {
                varianceSum += Math.pow(score - avg, 2);
            }
            float variance = varianceSum / scores.size();

            // 计算每个分数段的人数
            int count80_100 = 0;
            int count60_79 = 0;
            int count40_59 = 0;
            int count20_39 = 0;
            int count0_19 = 0;

            for (Float score : scores) {
                if (score >= 80 && score <= 100) {
                    count80_100++;
                } else if (score >= 60 && score < 80) {
                    count60_79++;
                } else if (score >= 40 && score < 60) {
                    count40_59++;
                } else if (score >= 20 && score < 40) {
                    count20_39++;
                } else if (score >= 0 && score < 20) {
                    count0_19++;
                }
            }
            // 填充模板
            scoreSituation = String.format(
                    scoreSituation,
                    count80_100,
                    count60_79,
                    count40_59,
                    count20_39,
                    count0_19,
                    avg,
                    median,
                    variance
            );
        }
        GptAskLogEntity gptAskLogEntity = new GptAskLogEntity();


        String report = completionService.createEssayAnalyticalReportCompletion(records, param, scoreSituation, gradeName);

        // 生成批改结果
        Map<String, Object> res = pdfService.createEssayAnalyticalReport(report);
        // 保存到远程文件夹
        save2Remote(String.format("%s 批改结果(%s).pdf", recordName, "作文报告"), res, param.getRemoteFileProps());
        // 远程打印
        doPrint(res, param.getPrinterProps());
        return res;
    }


    @Override
    public List<DocCorrectRecordDTO> listByIdOrTaskId(String id, String taskId) {
        User loginUser = AuthUtil.getLoginUser();

        LambdaQueryWrapper<DocCorrectRecord> queryWrapper = Wrappers.<DocCorrectRecord>lambdaQuery()
                .eq(StrUtil.isNotBlank(id), DocCorrectRecord::getId, id)
                .eq(StrUtil.isBlank(id), DocCorrectRecord::getTaskId, taskId)
                .orderByAsc(DocCorrectRecord::getDocname);
        if (Objects.nonNull(loginUser) && !Objects.equals(loginUser.getRole(), UserRoleConsts.ADMIN)) {
            String userId = loginUser.getId();
            queryWrapper.exists(String.format("select 1 from user_doc ud where ud.deleted = 0 and ud.user_id = '%s' and ud.doc_id = doc_correct_record.id", userId));
        }
        return docCorrectRecordMapper.selectList(queryWrapper).stream()
                .map(r -> BeanUtil.copyProperties(r, DocCorrectRecordDTO.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<DocCorrectRecord> selectByTaskId(String taskId, List<String> recordIds) {
        LambdaQueryWrapper<DocCorrectRecord> queryWrapper = Wrappers.<DocCorrectRecord>lambdaQuery()
                .eq(DocCorrectRecord::getTaskId, taskId);
        if (recordIds != null && !recordIds.isEmpty()) {
            queryWrapper.in(DocCorrectRecord::getId, recordIds);
        }
        return docCorrectRecordMapper.selectList(queryWrapper);
    }

    @Override
    public List<DocCorrectRecord> selectByTaskId(List<String> taskIds) {
        LambdaQueryWrapper<DocCorrectRecord> queryWrapper = Wrappers.<DocCorrectRecord>lambdaQuery()
                .in(DocCorrectRecord::getTaskId, taskIds);
        return docCorrectRecordMapper.selectList(queryWrapper);
    }

    @Override
    public Integer correctQs(String taskId, Boolean isUpdateHasChanged, Integer rightQsIdx, Boolean isRight) {
        Integer cnt = 0;
        LambdaQueryWrapper<DocCorrectRecord> queryWrapper = Wrappers.<DocCorrectRecord>lambdaQuery()
                .eq(Objects.nonNull(taskId), DocCorrectRecord::getTaskId, taskId)
                .orderByAsc(DocCorrectRecord::getDocname);
        List<DocCorrectRecord> dbList =  docCorrectRecordMapper.selectList(queryWrapper);
        for(DocCorrectRecord r : dbList) {
            JSONArray reviewedObj = JSONUtil.parseArray(r.getReviewed());
            if (reviewedObj == null || reviewedObj.isEmpty()) {
                continue;
            }
            int idx = -1;
            for(int areaIdx = 0; areaIdx < reviewedObj.size(); areaIdx++) {
                JSONObject areaObj = reviewedObj.getJSONObject(areaIdx);
                JSONArray qs = areaObj.getJSONArray("reviewed");
                if (qs == null || qs.isEmpty()) {
                    continue;
                }
                if (idx > rightQsIdx) {
                    break;
                }
                for(int qsIdx = 0; qsIdx < qs.size(); qsIdx++) {
                    idx ++;
                    if (idx == rightQsIdx) {
                        JSONObject qsDetail = qs.getJSONObject(qsIdx);
                        int hasChange = 0;
                        if (qsDetail.containsKey("hasChange")) {
                            hasChange = Integer.parseInt(qsDetail.getStr("hasChange"));
                        }
                        log.info("correctQs, taskId: {}, isUpdateHasChanged: {}, rightQsIdx: {}, isRight: {}, hasChange: {}, areaIdx: {}, qsIdx: {}",
                                taskId, isUpdateHasChanged, rightQsIdx, isRight, hasChange, areaIdx, qsIdx);
                        if (hasChange == 1 && !isUpdateHasChanged) {
                            continue;
                        }
                        if (qsDetail.getStr("isCorrect").equals("Y") && isRight) {
                            continue;
                        }
                        if (qsDetail.getStr("isCorrect").equals("N") && !isRight) {
                            continue;
                        }
                        qsDetail.set("hasChange", 1);
                        qsDetail.set("isCorrect", isRight ? "Y" : "N");
                        r.setReviewed(JSONUtil.toJsonStr(reviewedObj));
                        docCorrectRecordMapper.updateById(r);
                        cnt++;
                    }
                    if (idx > rightQsIdx) {
                        break;
                    }
                }
            }
        };
        return cnt;
    }

    /**
     * 保存到远程文件夹
     */
    private void save2Remote(String filename, Map<String, Object> docRes, SaveRemoteFileDTO props) {
        if (Objects.nonNull(props) && props.isSave()) {
            String fileUrl = (String) docRes.get("fileUrl");
            props.setSaveFileName(filename);
            props.setFilename(FileUtil.INSTANCE.ctxUrl2Path(fileUrl));
            remoteFileService.save2Remote(props);
        }
    }

    /**
     * 远程打印
     */
    private void doPrint(Map<String, Object> docRes, PrinterPropsDTO printerProps) {
        if (Objects.nonNull(printerProps) && printerProps.isPrint()) {
            String fileUrl = (String) docRes.get("fileUrl");
            String filename = FileUtil.INSTANCE.ctxUrl2Path(fileUrl);
            printerProps.setFilename(filename);
            String printId = printerService.print(printerProps);
            docRes.put("printId", printId);
        }
    }
}
