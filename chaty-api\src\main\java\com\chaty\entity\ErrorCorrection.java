package com.chaty.entity;

import java.util.Date;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import com.chaty.Handler.HutoolJsonTypeHandler;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

@Data
@TableName("error_correction")
public class ErrorCorrection {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String recordId;

    private String taskId;

    private String fileId;

    private String configId;

    private String docName;

    private Integer areaIdx;

    private Integer qsIdx;

    private Float afterErrorCorrectionScore;

    private Float beforeErrorCorrectionScore;

    private String afterErrorCorrectionAnswer;

    private String rightAnswer;

    private String studentAnswer;

    private String beforeErrorCorrectionAnswer;

    private String detail;

    private String areaType;

    private String areaImgUrl;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}

