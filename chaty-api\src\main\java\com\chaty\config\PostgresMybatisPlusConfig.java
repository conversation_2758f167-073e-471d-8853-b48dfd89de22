package com.chaty.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.chaty.tenant.CustomTenantHandler;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

// 1. 指定 PostgreSQL Mapper 的包路径，引用对应的 SqlSessionFactory
@Configuration
@MapperScan(basePackages = "com.chaty.postgres.mapper",
        sqlSessionFactoryRef = "postgresSqlSessionFactory")
public class PostgresMybatisPlusConfig {



    @Bean("postgresMybatisInterceptor")
    public MybatisPlusInterceptor pgInterceptor(CustomTenantHandler handler) {
        MybatisPlusInterceptor it = new MybatisPlusInterceptor();
        it.addInnerInterceptor(new PaginationInnerInterceptor(DbType.POSTGRE_SQL));
        it.addInnerInterceptor(new TenantLineInnerInterceptor(handler));
        return it;
    }

    @Bean("postgresSqlSessionFactory")
    public SqlSessionFactory postgresSqlSessionFactory(
            @Qualifier("postgresDataSource") DataSource ds,
            @Qualifier("postgresMybatisInterceptor") MybatisPlusInterceptor it
    ) throws Exception {
        MybatisSqlSessionFactoryBean fb = new MybatisSqlSessionFactoryBean();
        fb.setDataSource(ds);
        fb.setPlugins(it);
        return fb.getObject();
    }

    @Bean("postgresSqlSessionTemplate")
    public SqlSessionTemplate postgresSqlSessionTemplate(
            @Qualifier("postgresSqlSessionFactory") SqlSessionFactory sf
    ) {
        return new SqlSessionTemplate(sf);
    }
}

