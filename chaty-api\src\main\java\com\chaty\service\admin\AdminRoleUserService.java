package com.chaty.service.admin;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.dto.AdminRoleDTO;
import com.chaty.dto.AdminRoleUserDTO;
import com.chaty.entity.admin.AdminRoleUser;

public interface AdminRoleUserService extends IService<AdminRoleUser> {

    void add(AdminRoleUserDTO params);

    List<?> list(AdminRoleUserDTO params);

    void delete(String id);

    List<AdminRoleDTO> getRoleList(String userId);
    
}
