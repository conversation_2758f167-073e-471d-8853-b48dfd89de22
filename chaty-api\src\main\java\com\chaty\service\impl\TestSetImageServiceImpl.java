package com.chaty.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.entity.TestSetImageEntity;
import com.chaty.mapper.TestSetImageMapper;
import com.chaty.service.TestSetImageService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Service
public class TestSetImageServiceImpl
        extends ServiceImpl<TestSetImageMapper, TestSetImageEntity>
        implements TestSetImageService {

    @Resource
    private TestSetImageMapper mapper;

    @Override
    public IPage<TestSetImageEntity> page(Integer pageNumber, Integer pageSize,
                                          Long testSetId, String questionType, String fromRecordId) {
        Page<TestSetImageEntity> page = new Page<>(
                pageNumber == null ? 1 : pageNumber,
                pageSize == null ? 10 : pageSize
        );

        LambdaQueryWrapper<TestSetImageEntity> qw = new LambdaQueryWrapper<>();
        if (testSetId != null) qw.eq(TestSetImageEntity::getTestSetId, testSetId);
        if (StringUtils.hasText(questionType)) qw.eq(TestSetImageEntity::getQuestionType, questionType);
        if (StringUtils.hasText(fromRecordId)) qw.eq(TestSetImageEntity::getFromRecordId, fromRecordId);
        qw.orderByDesc(TestSetImageEntity::getCreateTime);

        return this.page(page, qw);
    }

    @Override
    public Long add(TestSetImageEntity param) {
        // 默认 useQuestionDetail = true（防止前端没传）
        if (param.getUseQuestionDetail() == null) {
            param.setUseQuestionDetail(Boolean.TRUE);
        }
        this.save(param);
        return param.getId();
    }

    @Override
    public Long updateOne(TestSetImageEntity param) {
        this.updateById(param);
        return param.getId();
    }

    @Override
    public void deleteById(Long id) {
        if (id != null) this.removeById(id);
    }
}
