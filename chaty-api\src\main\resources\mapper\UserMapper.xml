<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chaty.mapper.UserMapper">

    <!-- list -->
    <select id="list">
        select * from user 
        <where>
            deleted = 0
        </where>
    </select>



    <!-- selectById --> 

    <select id="selectById">
        select * from user where id = #{id}
    </select>

    <!-- deleteById --> 

    <update id="deleteById">
        update user set deleted = 1 where id = #{id}
    </update>    
</mapper>