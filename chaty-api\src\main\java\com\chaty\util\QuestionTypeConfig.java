package com.chaty.util;

import org.springframework.stereotype.Component;

/**
 * 题型配置类
 * 固定支持涂卡题的图像处理
 */
@Component
public class QuestionTypeConfig {
    
    /**
     * 检查指定题型是否需要图像处理
     * 只对涂卡题进行图像处理
     * @param questionType 题型名称
     * @return 是否需要图像处理
     */
    public boolean needsImageProcessing(String questionType) {
        if (questionType == null || questionType.trim().isEmpty()) {
            return false;
        }
        // 只对包含"涂卡题"的题型进行图像处理
        return questionType.contains("涂卡题");
    }
} 