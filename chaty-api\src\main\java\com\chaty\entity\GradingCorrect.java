package com.chaty.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableLogic;

import lombok.Data;

@Data
public class GradingCorrect {

    private String id;
    /**
     * 模板任务ID
     */
    private String templateId;
    /**
     * 模板任务
     */
    private String templateName;
    /**
     * 总题目数量
     */
    private Integer totalNum;
    /**
     * 纠错数量
     */
    private Integer fixedNum;
    /**
     * 纠错题目统计数据
     */
    private String qsStats;

    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    @TableLogic
    private Boolean deleted;

    /**
     * 批改失败的数量
     */
    private Integer failNum;

    private Integer modifyNum;

    private String stats;

    private String remark;

    private String tenantId;
}
