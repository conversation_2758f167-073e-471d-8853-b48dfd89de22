<#assign docInfo = records[0].config.getDocInfo()>

\documentclass[12pt,landscape]{article}
\usepackage[paperwidth=${docInfo.width}mm, paperheight=${docInfo.height}mm]{geometry}

\usepackage[absolute, overlay]{textpos}
\usepackage{CJKutf8}
\usepackage{fancyhdr}
\usepackage{pifont}
\usepackage{color}
\usepackage{graphicx}
\usepackage{pdfpages}
\usepackage{pbox}
\usepackage{amssymb}
\usepackage{tikz}
\pagestyle{fancy}
\fancyhf{}
\renewcommand{\headrulewidth}{0pt}
\usepackage{lmodern}
\usepackage{fix-cm}

% 定义自定义的勾（调整为更粗的线条）
\newcommand{\customCheck}[2]{
  \begin{tikzpicture}[scale=#1]
  % 这里4pt是粗细
    \draw[line width=2pt, color=#2] (0,0.25) -- (0.125,0.125) -- (0.5,0.5); % 线条宽度调整为1pt
  \end{tikzpicture}
}

% 定义自定义的叉
\newcommand{\customCross}[2]{
  \begin{tikzpicture}[scale=#1]
    \draw[line width=2pt, color=#2] (0,0) -- (0.5,0.5);
    \draw[line width=2pt, color=#2] (0.5,0) -- (0,0.5);
  \end{tikzpicture}
}

\setlength{\TPHorizModule}{1mm}
\setlength{\TPVertModule}{1mm}
\DeclareUnicodeCharacter{0964}{}

\begin{document}
\begin{CJK*}{UTF8}{gbsn} 
\begin{textblockorigin}{0mm}{0mm}

<#function latexEscape str>
    <#return str?replace("#", "\\#")?replace("$", "\\$")?replace("%", "\\%")?replace("&", "\\&")?replace("_", "\\_")?replace("{", "\\{")?replace("}", "\\}")?replace("~", "\\~")?replace("^", "\\^")?replace("\\", "\\textbackslash")?replace("[", "\\texttt{[}")?replace("]", "\\texttt{]}")>
</#function>

<#assign pageNo = 0>

<#list records as record>
<#if pageNo == pageNum>
<#assign pageNo = 0>
</#if>
<#assign pageNo = (pageNo + 1) >
<#assign config = record.config>
<#assign task = record.task>
<#assign areas = config.getAreasObj()>
<#assign configObj = config.getConfigObj()>
<#assign fontSize = configObj.fontSize>
<#assign flagSize = configObj.flagSize>
<#assign flagColor = configObj.flagColor!'green'>
<#assign errorFlagColor = configObj.errorFlagColor!'red'>
<#assign errorFlagSize = (configObj.errorFlagSize)!configObj.flagSize>
<#assign additionalName = (configObj.additionalName)!'附加'>
<#assign isScore = configObj.score && configObj.scoreArea??>
<#assign scoreArea = configObj.scoreArea>
<#assign docurl = record.docurl>
<#assign reviewedObj = record.getReviewedObj()>
<#assign scorePageNo = 1>
<#if isReversed?? && isReversed>
  <#assign scorePageNo = pageNum>
</#if>

\begin{textblock}{1000}(0,0)
\begin{tikzpicture}[remember picture, overlay, shift={(current page.north west)}, yscale=-1]

% 分数
<#if isScore && (pageNo == scorePageNo)>
<#assign totalScore = 0>
<#assign score = 0>
<#assign additionalScore = 0>
<#assign additionalScored = 0>
<#assign correctRate = 100>
<#assign pages = 0>
<#assign scoreTypes = {}>
<#if mergeScore && (pageNum > 1)>
    <#assign pages = pageNum - 1>
</#if>
<#list 0..pages as pageIdx>
    <#assign rIdx = record_index + pageIdx>
    <#if isReversed?? && isReversed>
        <#assign rIdx = record_index - pageIdx>
    </#if>
    <#assign r = records[rIdx]>
    <#assign scoreObj = r.getScore()>
    <#assign totalScore += scoreObj.totalScore>
    <#assign score += scoreObj.scored>
    <#assign additionalScore += scoreObj.additionalScore>
    <#assign additionalScored += scoreObj.additionalScored>
    <#if pageIdx == 0>
        <#assign scoreTypes = scoreObj.scoreTypeMap>
    <#else>
        <#assign scoreTypes = record.mergeScoreType(scoreTypes, scoreObj.scoreTypeMap)>
    </#if>
</#list>
<#if totalScore != 0>
    <#assign correctRate = score / totalScore * 100>
</#if>
<#assign areax = scoreArea.x!0>
<#assign areay = scoreArea.y!0>
<#assign scoreColor = configObj.scoreColor!'red'>
<#if scoreColor == 'byScore'>
    <#assign scoreColor = (correctRate gte 60)?string('green','red')>
</#if>
<#assign scoreTxt = record.scoreTxt(totalScore, score, additionalName, additionalScore, additionalScored, scoreTypes, configObj.scoreFormat!1)>
    \node[anchor=north west] at (${areax / 300 * 25.4}mm, ${areay / 300 * 25.4}mm) {
        \textcolor{${scoreColor}}{\fontsize{20pt}{100pt} \selectfont {${scoreTxt}} }
    };
</#if>


% 区域
<#list areas as area>
<#if !(area.enabled?? && area.enabled == false)>
<#assign questions = area.questions>
<#assign areaType = area.areaType!1>
<#assign qsArea = reviewedObj[area_index]>

% 批改成功
<#list questions as qs>
<#assign qsRes = qsArea.reviewed[qs_index]>
<#assign isTrue = qsRes.isCorrect?? && qsRes.isCorrect == 'Y'>
<#assign flagArea = qs.flagArea>
<#assign review = qsRes.review!''>
<#if (qs.reviewType!1) == 2>
<#assign review = qs.defaultReview>
</#if>

% 批改意见  
<#if (!isTrue || areaType == 3) && review?? && qs.reviewArea??>
<#assign reviewArea = qs.reviewArea>

<#if areaType == 3>
\node[anchor=north west] at (${reviewArea.x / 300 * 25.4}mm, ${reviewArea.y / 300 * 25.4}mm) {
    \color{red}
    \fontsize{${fontSize}}{15pt}\selectfont 
    评分: ${qsRes.scored!''} \\
    <#if qsRes.review.错误分析??>
    错误分析: \\
    拼写错误: \\
    ${latexEscape(qsRes.review.错误分析.拼写错误)!''} \\
    语法错误:
    ${latexEscape(qsRes.review.错误分析.语法错误)!''} \\
    用词不当:
    ${latexEscape(qsRes.review.错误分析.用词不当)!''} \\
    </#if>
    <#if qsRes.review.亮点分析??>
    亮点分析: \\
    高级词汇: \\
    ${latexEscape(qsRes.review.亮点分析.高级词汇)!''} \\
    亮点表达: \\
    ${latexEscape(qsRes.review.亮点分析.亮点表达)!''} \\
    </#if>
    <#if qsRes.review.写作建议??>
    写作建议: \\
    ${latexEscape(qsRes.review.写作建议)!''} \\
    </#if>
    <#if qsRes.studentAnswer??>
    学生作文全文: \\
    ${latexEscape(qsRes.studentAnswer)!''} \\
    </#if>
};

<#else>

\node[anchor=north west] at (${reviewArea.x / 300 * 25.4}mm, ${reviewArea.y / 300 * 25.4}mm) {
    \fbox{
        \pbox[t]{${(reviewArea.width / 300 * 25.4) - 20}mm}{
            \textcolor{red}{\fontsize{${fontSize}}{50pt}\selectfont ${review}}
        }
    }
};

</#if>

</#if>

% flag
\node[anchor=north west] at (${flagArea.x / 300 * 25.4}mm, ${flagArea.y / 300 * 25.4}mm) {
% \begin{minipage}{${flagArea.width / 300 * 25.4}mm}

% 分数
<#assign qsScoredTxt = ''>
<#if showQsScore!false>
    <#assign qsScored = 0>
    <#if (qs.isScorePoint!1) == 2>
        <#assign qsScored = qsRes.scored!"0">
    <#elseif isTrue>
        <#assign qsScored = qs.score>
    </#if>
    <#assign qsScoredTxt = ["\\fontsize{", configObj.scoreFontSize!10, "}{50pt}$", qsScored, "/", qs.score, "$"]?join("")>
</#if>

<#if isTrue>
    <#if ((configObj.correctFlag)!'a') == 'a'>
\textcolor{${flagColor}}{\fontsize{${flagSize}}{50pt}\selectfont $\checkmark$ ${qsScoredTxt}}
    <#elseif configObj.correctFlag == 'b'>
\textcolor{${flagColor}}{\fontsize{${flagSize}}{50pt}\selectfont \ding{51} ${qsScoredTxt}}
    <#elseif configObj.correctFlag == 'c'>
\textcolor{${flagColor}}{\fontsize{${flagSize}}{50pt}\selectfont \ding{52} ${qsScoredTxt}}
    <#elseif configObj.correctFlag == 'd'>
\textcolor{${flagColor}}{\fontsize{${flagSize}}{50pt}\selectfont \customCheck{${flagSize/20}}{${flagColor}} ${qsScoredTxt}}
    </#if>
<#else>
    <#if ((configObj.errorFlag)!'x') == 'x'>
\textcolor{${errorFlagColor}}{\fontsize{${errorFlagSize}}{50pt}\selectfont \ding{55} ${qsScoredTxt}}
    <#elseif configObj.errorFlag == 'c'>
\textcolor{${errorFlagColor}}{\fontsize{${errorFlagSize}}{50pt}\selectfont \ding{56} ${qsScoredTxt}}
    <#elseif configObj.errorFlag == 'd'>
\textcolor{${errorFlagColor}}{\fontsize{${errorFlagSize}}{50pt}\selectfont \ding{53} ${qsScoredTxt}}
    <#elseif configObj.errorFlag == 'a'>
\textcolor{${errorFlagColor}}{\fontsize{${errorFlagSize}}{50pt}\selectfont \customCross{${errorFlagSize/20*0.6}}{${errorFlagColor}} ${qsScoredTxt}}
    <#elseif configObj.errorFlag == 'b'>
\textcolor{${errorFlagColor}}{\fontsize{${errorFlagSize}}{50pt}\selectfont \ding{54} ${qsScoredTxt}}
    </#if>
</#if>

};

</#list>

</#if>
</#list>

\end{tikzpicture}
\end{textblock}

% 是否预览
<#if isPreview>
% 开启预览
\includepdf[pages=-, frame=true, scale=1, pagecommand={}]{${docurl?split("/")[2]}}
<#else>
% 不开启预览
\null
\clearpage
</#if>

</#list>

\end{textblockorigin}
\end{CJK*} 
\end{document}