package com.chaty.dto;

import java.util.List;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.Data;

/**
 * 试卷批改区域结果
 */
@Data
public class CorrectAreaDTO {

    /**
     * 批改状态 1-批改成功 2-批改失败
     */
    private Integer status;
    /**
     * 区域索引
     */
    private Integer areaIndex;
    /**
     * 试卷id
     */
    private String recordId;
    /**
     * 模型批改响应内容
     */
    private String $response;
    /**
     * 批改结果
     */
    private List<JSONObject> reviewed;

    /**
     * 原始JSON
     */
    private JSONObject rawObj;

    /**
     * 区域批改是否成功
     */
    public Boolean isSuccess() {
        return status == 1;
    }

    /**
     * 合并返回JSON结果
     */
    public JSONObject toJSONObj() {
        // 合并当前对象和原始JSON
        JSONObject obj = JSONUtil.parseObj(this);
        obj.remove("rawObj");
        rawObj.putAll(obj);
        return rawObj;
    }

    /**
     * 创建对象
     */
    public static CorrectAreaDTO create(JSONObject obj) {
        CorrectAreaDTO dto = JSONUtil.toBean(obj, CorrectAreaDTO.class);
        dto.setRawObj(obj);
        return dto;
    }

}
