package com.chaty.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.dto.DocCorrectRecordDTO;
import com.chaty.entity.DocCorrectRecord;

public interface DocCorrectRecordService extends IService<DocCorrectRecord> {

    IPage<DocCorrectRecordDTO> page(DocCorrectRecordDTO param);

    void add(DocCorrectRecordDTO param);

    void update(DocCorrectRecordDTO param);

    DocCorrectRecordDTO checkHasChange(DocCorrectRecordDTO param);

    void delete(String id);

    void setRecordWait(List<String> ids, String taskId);

    List<DocCorrectRecord> listWait(Integer limit);

    void reset2Wait();

    Map<String, Object> createReviewedDoc(DocCorrectRecordDTO param);

    Map<String, Object> createEssayReport(DocCorrectRecordDTO param);

    Map<String, Object> createEssayAnalyticalReport(DocCorrectRecordDTO param);

    List<DocCorrectRecord> selectByTaskId(String taskId, List<String> ids);
    List<DocCorrectRecord> selectByTaskId(List<String> taskIds);

    List<DocCorrectRecordDTO> listByIdOrTaskId(String id, String taskId);

    Integer correctQs(String taskId, Boolean isUpdateHasChanged, Integer qsIdx, Boolean isRight);
    
}
