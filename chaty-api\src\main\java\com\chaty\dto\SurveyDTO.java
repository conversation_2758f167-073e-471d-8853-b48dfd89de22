package com.chaty.dto;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

@Data
public class SurveyDTO extends BaseDTO {

    private String id;
    private String name;
    private String phone;
    private String email;
    private String school;
    private String subject;
    private String grade;
    private String scenarios;
    private String features;
    private String comments;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    
}
