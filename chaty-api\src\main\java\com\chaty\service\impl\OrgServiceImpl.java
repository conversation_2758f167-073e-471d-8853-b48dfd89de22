package com.chaty.service.impl;

import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;

import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chaty.completion.CompletionService;
import com.chaty.dto.OrgQuestionDTO;
import com.chaty.entity.Org;
import com.chaty.entity.OrgCorrectResult;
import com.chaty.entity.OrgQuestions;
import com.chaty.exception.BaseException;
import com.chaty.mapper.OrgCorrectResultMapper;
import com.chaty.mapper.OrgMapper;
import com.chaty.mapper.OrgQuestionsMapper;
import com.chaty.security.AuthUtil;
import com.chaty.security.TokenAuth;
import com.chaty.security.TokenAuthService;
import com.chaty.service.OrgService;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class OrgServiceImpl implements OrgService, TokenAuthService {

    @Resource
    private OrgMapper orgMapper;
    @Resource
    private OrgQuestionsMapper orgQuestionsMapper;
    @Resource
    private CompletionService completionService;
    @Resource
    private OrgCorrectResultMapper orgCorrectResultMapper;

    private Cache<String, Org> orgCache = CacheUtil.newLRUCache(100, 5 * 60 * 1000);

    @Override
    public TokenAuth<?> authenticate(String token) throws AuthenticationException {
        Org org = orgCache.get(token, () -> {
            Org selected = orgMapper.selectOne(Wrappers.lambdaQuery(Org.class)
                    .eq(Org::getApiKey, token));
            return Optional.ofNullable(selected).orElse(new Org());
        });
        if (Objects.isNull(org.getId())) {
            log.error("BadCredentialsException: {}", token);
            throw new BadCredentialsException("Unauthorized");
        }

        TokenAuth<Org> auth = new TokenAuth<>(token);
        auth.setAuth(org);
        return auth;
    }

    @Override
    public OrgQuestions saveQuestion(OrgQuestionDTO params) {
        Org org = AuthUtil.getOrgAuth().getAuth();
        if (Objects.nonNull(params.getId())) {
            OrgQuestions existed = orgQuestionsMapper.selectById(params.getId());
            if (Objects.isNull(existed)) {
                throw new BaseException("题目不存在");
            }
            if (!Objects.equals(existed.getOrgId(), org.getId())) {
                throw new BaseException("未授权的操作");
            }
            orgQuestionsMapper.updateById(params);
        } else {
            params.setOrgId(org.getId());
            orgQuestionsMapper.insert(params);
        }
        return params;
    }

    @Override
    public OrgCorrectResult correct(OrgQuestionDTO params) {
        Org org = AuthUtil.getOrgAuth().getAuth();
        OrgQuestions qs = orgQuestionsMapper.selectById(params.getId());
        if (Objects.isNull(qs)) {
            throw new BaseException("题目不存在");
        }
        if (!Objects.equals(qs.getOrgId(), org.getId())) {
            throw new BaseException("未授权的操作");
        }

        BeanUtil.copyProperties(qs, params);
        OrgCorrectResult res = completionService.correctOrgQs(params);
        res.setQsId(params.getId());
        res.setQsNo(params.getQsNo());
        res.setCorrectImage(params.getCorrectImage());
        res.setCorrectAnswer(params.getCorrectAnswer());
        res.setOrgId(org.getId());
        res.setPoints(1);
        orgCorrectResultMapper.insert(res);

        res.setCorrectImage(null);
        return res;
    }

}
