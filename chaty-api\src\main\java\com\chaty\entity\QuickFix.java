package com.chaty.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableLogic;

import lombok.Data;

@Data
public class QuickFix {

    private String id;
    /**
     * 模板任务ID
     */
    private String templateId;
    /**
     * 模板任务
     */
    private String templateName;
    /**
     * 纠错任务ID
     */
    private String fixedId;
    /**
     * 纠错任务
     */
    private String fixedName;
    /**
     * 总题目数量
     */
    private Integer totalNum;
    /**
     * 纠错数量
     */
    private Integer fixedNum;

    private Integer failNum;
    /**
     * 纠错题目统计数据
     */
    private String qsStats;

    private Integer identifySumCount;

    private Integer identifyRightCount;

    private Integer identifyWrongCount;

    private Double identifyRatio;

    private Integer studentNumberSumCount;

    private Integer studentNumberRightCount;

    private Integer studentNumberWrongCount;

    private Double studentNumberRatio;

    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    @TableLogic
    private Boolean deleted;

    private Integer correctNum;

    private String stats;

    private String remark;

    private String tenantId;
}
