package com.chaty.dto;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import cn.hutool.core.util.StrUtil;
import com.chaty.entity.DocCorrectTask;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.Data;

import static com.chaty.enums.CorrectConfigConsts.*;

@Data
public class DocCorrectTaskDTO extends DocCorrectTask {

    private PageDTO<DocCorrectTask> page;

    private String configName;

    private String aimodel;

    private String ocrType;

    private Boolean responseFormat;

    private Boolean jsonobject;

    private Boolean jsonschema;

    JSONObject correctConfigObj;

    private String filename;

    private List<List<JSONObject>> docList;

    private Boolean scoreMerge;

    private Boolean isPreview;

    private List<String> taskIds;

    private Integer count;

    private List<String> recordIds;

    private boolean countRecord;

    private Integer recordCount;

    private Integer finishedCount;

    private Integer fontSize;

    private BigDecimal segedScore;

    private Boolean showQsScore = false;

    private String[] tagIds;

    private String taskId;

    private Boolean orderByName = false;

    private Boolean isCreateConfig;

    private Boolean isReversed = false;

    private Boolean isRotate = false;
    
    private String similarPaperId;

    private SaveRemoteFileDTO remoteFileProps;

    private PrinterPropsDTO printerProps;

    private String name;

    private Boolean enableLevelOutput;

    // 按照percentage排序,从小到大
    private JSONObject ranges;

    private String segments;

    private List<RotationItemDTO> needRotationList;

    private List<SwapPagesItemDTO> swapPagesList;

    private String reIdentifyNameOrStudentNumberType;

    private Boolean onlyShowWrongQsScore = true;

    /**
     * 统计分数时是否将小数保存为整数
     */
    private Boolean isSaveInteger = true;

    /**
     * 统计分数时 识别分数类型是否显示平均分
     */
    private Boolean scorePointTypeShowAverageScore = false;

    public String getCorrectConfigStr() {
        return JSONUtil.createObj()
                .set("aimodel", getAimodel())
                .set("ocrType", getOcrType())
                .set("responseFormat", getResponseFormat())
                .set("jsonobject", getJsonobject())
                .set("jsonschema", getJsonschema())
                .toString();
    }

    public JSONObject getCorrectConfigObj() {
        return Optional.ofNullable(correctConfigObj)
                .orElseGet(() -> JSONUtil.parseObj(getCorrectConfig()));
    }

    public String getAimodel() {
        return Optional.ofNullable(aimodel)
                .orElseGet(() -> getCorrectConfigObj().getStr("aimodel"));
    }

    public String getOcrType() {
        return Optional.ofNullable(ocrType)
                .orElseGet(() -> getCorrectConfigObj().getStr("ocrType"));
    }

    public Boolean getResponseFormat() {
        return Optional.ofNullable(responseFormat)
                .orElseGet(() -> getCorrectConfigObj().getBool("responseFormat", false));
    }

    public Boolean getJsonobject() {
        Boolean defaultJSONobject;
        if (StrUtil.isNotBlank(this.aimodel)) {
            defaultJSONobject = this.aimodel.contains("doubao") ? defaultJsonObjectValueForDouBao : defaultJsonObjectValueForOtherModal;
        } else {
            defaultJSONobject = com.chaty.enums.CorrectConfigConsts.defaultJsonObjectValue;
        }
        return Optional.ofNullable(jsonobject)
                .orElseGet(() -> getCorrectConfigObj().getBool("jsonobject", defaultJSONobject));
    }

    public Boolean getJsonschema() {
        boolean defaultJSONSchema;
        if (StrUtil.isNotBlank(this.aimodel)) {
            defaultJSONSchema = this.aimodel.contains("doubao") ? defaultJsonSchemaValueForDouBao : defaultJsonSchemaValueForOtherModal;
        } else {
            defaultJSONSchema = com.chaty.enums.CorrectConfigConsts.defaultJsonSchemaValue;
        }

        return Optional.ofNullable(jsonschema)
                .orElseGet(() -> getCorrectConfigObj().getBool("jsonschema", defaultJSONSchema));
    }

}
