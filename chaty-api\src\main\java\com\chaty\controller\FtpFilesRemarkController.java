package com.chaty.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.chaty.common.BaseResponse;
import com.chaty.dto.FtpFilesRemarkDTO;
import com.chaty.entity.FtpFiles;
import com.chaty.mapper.FtpFilesMapper;
import com.chaty.service.FtpFilesService;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.nio.file.Paths;
import java.util.List;

import static com.chaty.util.MyStringUtils.getNullPropertyNames;

@RestController
@RequestMapping("/api/ftpFilesRemark")
public class FtpFilesRemarkController {

    @Resource
    private FtpFilesService ftpFilesService;
    @Resource
    private FtpFilesMapper ftpFilesMapper;

    @PostMapping("/add")
    public BaseResponse<?> addRemark(@RequestBody FtpFilesRemarkDTO dto) {
        if (StrUtil.isBlank(dto.getFilename()) || StrUtil.isBlank(dto.getPath())) {
            return BaseResponse.error("路径/文件名称不能为空");
        }
        // 确保不会出现重复数据 如果重复则合并，以dto优先
//        LambdaQueryWrapper<FtpFiles> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(FtpFiles::getFilename, dto.getFilename());
//        queryWrapper.eq(FtpFiles::getPath, dto.getPath());
//        queryWrapper.orderByDesc(FtpFiles::getCreateTime);
//        List<FtpFiles> ftpFilesList = ftpFilesMapper.selectList(queryWrapper);
//        FtpFiles target;
//        if (ftpFilesList.size() > 1) {
//            target = ftpFilesList.get(0);
//            for (int i = 1; i < ftpFilesList.size(); i++) {
//                ftpFilesMapper.deleteById(ftpFilesList.get(i).getId());
//            }
//        } else if (ftpFilesList.size() == 1) {
//            target = ftpFilesList.get(0);
//        } else {
//            target = new FtpFiles();
//            BeanUtils.copyProperties(dto, target);
//            ftpFilesMapper.insert(target);
//            return BaseResponse.ok(dto);
//        }
//
//        String[] ignoreProps = getNullPropertyNames(dto);
//        BeanUtils.copyProperties(dto, target, ignoreProps);

        FtpFilesRemarkDTO result = ftpFilesService.addRemark(dto);
        return BaseResponse.ok(result);
    }

    @PostMapping("/update")
    public BaseResponse<?> updateRemark(@RequestBody FtpFilesRemarkDTO dto) {
        FtpFilesRemarkDTO result = ftpFilesService.updateRemark(dto);
        return BaseResponse.ok(result);
    }

    @PostMapping("/updateRemarkByPath")
    public BaseResponse<?> updateRemarkByPath(@RequestBody FtpFilesRemarkDTO dto) {
        // 先查询，从路径获取filename
        String path = dto.getPath();
        if (path == null || path.isEmpty()) {
            return BaseResponse.error("Path cannot be null or empty");
        }
        String fileName = Paths.get(path).getFileName().toString();
        path = path.replace(fileName, "");
        if (path.endsWith("/") || path.endsWith("\\")) {
            path = path.substring(0, path.length() - 1);
        }
        // 根据path和filename匹配，如果有则update remark
        QueryWrapper<FtpFiles> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("path", path)
                .eq("filename", fileName);
        List<FtpFiles> ftpFiles = ftpFilesMapper.selectList(queryWrapper);
        if (ftpFiles.isEmpty()) {
            FtpFiles needInsert = new FtpFiles();
            needInsert.setPath(path);
            needInsert.setFilename(fileName);
            needInsert.setRemark(dto.getRemark());
            needInsert.setType("remark");
            ftpFilesMapper.insert(needInsert);
            return BaseResponse.ok(needInsert);
        } else if (ftpFiles.size() != 1) {
            return BaseResponse.error("Multiple files found with the specified path and filename, please refine your search");
        }
        FtpFiles needUpdate = new FtpFiles();
        needUpdate.setId(ftpFiles.get(0).getId());
        needUpdate.setRemark(dto.getRemark());

        int row = ftpFilesMapper.updateById(needUpdate);
        if (row == 0) {
            return BaseResponse.error("Failed to update remark");
        }
        return BaseResponse.ok(needUpdate);
    }
} 