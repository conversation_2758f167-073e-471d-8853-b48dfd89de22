<#assign docInfo = configs[0].getDocInfo()>

\documentclass[a4paper]{article}
\usepackage[paperwidth=${docInfo.width}mm, paperheight=${docInfo.height}mm]{geometry}

\usepackage[absolute, overlay]{textpos}
\usepackage{CJKutf8}
\usepackage{fancyhdr}
\usepackage{pifont}
\usepackage{color}
\usepackage{graphicx}
\usepackage{pdfpages}
\usepackage{pbox}
\pagestyle{fancy}

\fancyhf{}
\renewcommand{\headrulewidth}{0pt}

\setlength{\TPHorizModule}{1mm}
\setlength{\TPVertModule}{1mm}
\DeclareUnicodeCharacter{0964}{}



\begin{document}

<#function latexEscape str>
    <#return str?replace("#", "\\#")?replace("$", "\\$")?replace("%", "\\%")?replace("&", "\\&")?replace("_", "+")?replace("{", "\\{")?replace("}", "\\}")?replace("~", "\\~")?replace("^", "\\^")?replace("\\", "\\textbackslash")?replace("[", "\\texttt{[}")?replace("]", "\\texttt{]}")>
</#function>

<#list configs as config>

<#assign areas = areaList[config_index]>
<#assign configObj = config.getConfigObj()>
<#assign isScore = config_index == 0 && configObj.score && configObj.scoreArea??>
<#assign scoreArea = configObj.scoreArea>
<#assign additionalName = (configObj.additionalName)!'附加'>
<#assign flagSize = configObj.flagSize>
<#assign total = stats.total>

% 分数
<#if isScore>
<#assign totalScore = stats.totalScore>
<#assign avgScored = stats.avgScored>
<#assign medianScored = stats.medianScored>
<#assign totalAddScore = stats.totalAddScore>
<#assign avgAddScored = stats.avgAddScored>
<#if totalScore != 0>
    <#assign correctRate = avgScored / totalScore * 100>
<#else>
    <#assign correctRate = 0>
</#if>


<#assign areax = scoreArea.x!0>
<#assign areay = scoreArea.y!0>
<#assign scoreColor = configObj.scoreColor!'red'>
<#if scoreColor == 'byScore'>
    <#assign scoreColor = (correctRate gte 60)?string('green','red')>
</#if>

\begin{textblock}{1000}(${70 / 300 * 25.4}, ${70 / 300 * 25.4})
\fontsize{${fontSize!flagSize}}{10pt} \selectfont
\begin{CJK*}{UTF8}{gbsn}

    \textcolor{${scoreColor}}{\textbf{${latexEscape(taskName)}}}

    <#if totalAddScore == 0>
        \textcolor{${scoreColor}}{\textbf{平均分: ${avgScored}/${totalScore} 中位数: ${medianScored}/${totalScore} }}
    <#else>
        \textcolor{${scoreColor}}{\textbf{试卷 ${avgScored}/${totalScore}  ${additionalName} ${avgAddScored}/${totalAddScore}}}
    </#if>
    
    <#list segedList as seged>
        \textcolor{${scoreColor}}{\textbf{${seged.score}分：${seged.count}人}}

    </#list>

\end{CJK*}
\end{textblock}
</#if>

<#list areas as area>
<#if !(area.enabled?? && area.enabled == false)>
<#assign questions = area.questions>

<#list questions as qs>
<#assign flagArea = qs.flagArea>
<#assign correctCount = qs.correctCount>
<#assign isScorePoint = qs.isScorePoint>
<#assign score = qs.score>
<#if isScorePoint == 2>
    <#if scorePointTypeShowAverageScore>
        <#assign correctRate = correctCount / total>
    <#else>
        <#assign correctRate = correctCount / total * 100>
    </#if>
<#else>
<#assign correctRate = correctCount / total * 100>
</#if>
<#assign smallSize = (fontSize!flagSize)?number - 5 />
\begin{textblock}{1000}(${flagArea.x / 300 * 25.4}, ${flagArea.y / 300 * 25.4})
\fontsize{10pt}{100pt} \selectfont
\begin{CJK*}{UTF8}{gbsn}
        <#if isSaveInteger>
          <#assign numFmt = '0'>
        <#else>
          <#assign numFmt = '0.0'>
        </#if>
        <#if isScorePoint == 2>
            <#if scorePointTypeShowAverageScore>
                \textcolor{${(correctRate gte 60)?string('green','red')}}{\fontsize{${fontSize!flagSize}}{30pt}\selectfont \textbf{${correctRate?string(numFmt)}/${score?string(numFmt)}分}}
            <#else>
                \textcolor{${(correctRate gte 60)?string('green','red')}}{\fontsize{${smallSize}}{30pt}\selectfont \textbf{${correctRate?string(numFmt)}\%}}
            </#if>
        <#else>
          \textcolor{${(correctRate gte 60)?string('green','red')}}{\fontsize{${smallSize}}{30pt}\selectfont \textbf{${correctRate?string(numFmt)}\%}}
        </#if>
\end{CJK*}
\end{textblock}


</#list>

</#if>
</#list>

% 开启预览
\includepdf[pages=-, frame=true, scale=1, pagecommand={}]{${config.docurl?split("/")[2]}}

</#list>

\end{document}

