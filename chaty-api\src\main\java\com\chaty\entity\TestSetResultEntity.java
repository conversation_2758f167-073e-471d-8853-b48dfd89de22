package com.chaty.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.chaty.enums.TestSetResultStatus;
import lombok.Data;
import java.util.Date;

@Data
@TableName("test_set_result")
public class TestSetResultEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long testSetId;

    private Integer correctCount;
    private Integer totalCount;
    private Integer imageCount;
    private Integer imageSuccessCount;

    private String mdUrl;

    /** 新增：状态（PROCESSING=批改中，DONE=批改完成） */
    private TestSetResultStatus status;

    // 结构化的测试集信息快照
    private String testSetName;
    private String testSetDescription;
    private String testSetQuestionTypes;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
