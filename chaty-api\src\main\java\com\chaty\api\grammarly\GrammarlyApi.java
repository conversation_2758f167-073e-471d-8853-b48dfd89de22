package com.chaty.api.grammarly;

import com.chaty.api.lianke.LiankeBaseResponse;
import feign.Headers;
import feign.Param;
import feign.RequestLine;

import java.io.File;

public interface GrammarlyApi {

    /**
     * 提交写作评分请求，返回上传文件的 URL
     *
     * @param apiKey 你的API密钥
     * @param filename 要上传的文件名
     * @return LiankeBaseResponse
     */
    @RequestLine("POST /ecosystem/api/v2/scores")
    @Headers({
            "Content-Type: application/json",
            "Authorization: Bearer {apiKey}",
            "user-agent: API client"
    })
    LiankeBaseResponse<ScoreRequestResponse> submitScoreRequest(@Param("apiKey") String apiKey, @Param("filename") String filename);

    /**
     * 上传文档到指定的URL
     *
     * @param file 上传的文件
     * @param fileUploadUrl 文档上传地址
     */
    @RequestLine("PUT {fileUploadUrl}")
    void uploadDocument(@Param("fileUploadUrl") String fileUploadUrl, File file);

    /**
     * 获取写作评分结果
     *
     * @param apiKey 你的API密钥
     * @param scoreRequestId 请求ID
     * @return 评分结果
     */
    @RequestLine("GET /ecosystem/api/v2/scores/{scoreRequestId}")
    @Headers({
            "Content-Type: application/json",
            "Authorization: Bearer {apiKey}",
            "user-agent: API client"
    })
    LiankeBaseResponse<ScoreResultResponse> getScoreResult(@Param("apiKey") String apiKey, @Param("scoreRequestId") String scoreRequestId);
}

