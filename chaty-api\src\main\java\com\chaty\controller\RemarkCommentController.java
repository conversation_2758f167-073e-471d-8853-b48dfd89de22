package com.chaty.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.common.BaseResponse;
import com.chaty.entity.RemarkComment;
import com.chaty.service.cache.RemarkCommentService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/api/remark")
public class RemarkCommentController {

    @Resource
    private RemarkCommentService service;

    /** 分页查询（支持关键字模糊匹配 content） */
    @GetMapping("/page")
    public BaseResponse<IPage<RemarkComment>> page(
            @RequestParam(required = false) Integer pageNumber,
            @RequestParam(required = false) Integer pageSize,
            @RequestParam(required = false) String keyword
    ) {
        return BaseResponse.ok(service.page(pageNumber, pageSize, keyword));
    }

    /** 新增 */
    @PostMapping("/add")
    public BaseResponse<Long> add(@RequestBody RemarkComment param) {
        return BaseResponse.ok(service.add(param));
    }

    /** 更新 */
    @PostMapping("/update")
    public BaseResponse<Long> update(@RequestBody RemarkComment param) {
        return BaseResponse.ok(service.updateOne(param));
    }

    /** 删除 */
    @DeleteMapping("/{id}")
    public BaseResponse<?> delete(@PathVariable Long id) {
        service.deleteById(id);
        return BaseResponse.ok("删除成功");
    }

    /** 加权随机抽取一条评语 */
    @GetMapping("/random")
    public BaseResponse<RemarkComment> random() {
        return BaseResponse.ok(service.randomOne());
    }

    /** 手动重建缓存（可选） */
    @PostMapping("/rebuildCache")
    public BaseResponse<?> rebuildCache() {
        service.rebuildAllCaches();
        return BaseResponse.ok("缓存已重建");
    }
}
