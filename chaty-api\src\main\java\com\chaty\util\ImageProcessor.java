package com.chaty.util;

import lombok.extern.slf4j.Slf4j;
import java.awt.image.BufferedImage;

/**
 * 图像处理器
 * 实现图像增强和像素连接功能
 */
@Slf4j
public class ImageProcessor {
    
    /**
     * 默认阈值
     */
    private static final int DEFAULT_THRESHOLD = 220;
    
    /**
     * 默认是否启用像素连接
     */
    private static final boolean DEFAULT_CONNECT = true;
    
    /**
     * 处理图像，应用阈值处理和像素连接
     * @param image 输入图像
     * @return 处理后的图像
     */
    public static BufferedImage processImage(BufferedImage image) {
        return processImage(image, DEFAULT_THRESHOLD, DEFAULT_CONNECT);
    }
    
    /**
     * 处理图像，应用阈值处理和像素连接
     * @param image 输入图像
     * @param threshold 阈值
     * @param connect 是否启用像素连接
     * @return 处理后的图像
     */
    public static BufferedImage processImage(BufferedImage image, int threshold, boolean connect) {
        if (image == null) {
            return null;
        }
        
        try {
            // 转换为灰度图
            BufferedImage grayscaleImage = convertToGrayscale(image);
            
            // 应用阈值处理
            BufferedImage thresholdImage = applyThreshold(grayscaleImage, threshold);
            
            // 应用像素连接（如果需要）
            BufferedImage finalImage;
            if (connect) {
                finalImage = applyPixelConnection(thresholdImage);
            } else {
                finalImage = convertGrayscaleToRGB(thresholdImage);
            }
            
            log.info("图像处理完成，阈值: {}, 像素连接: {}", threshold, connect);
            return finalImage;
            
        } catch (Exception e) {
            log.error("图像处理过程中发生错误", e);
            return image; // 返回原图
        }
    }
    
    /**
     * 转换为灰度图
     */
    private static BufferedImage convertToGrayscale(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        
        BufferedImage grayscaleImage = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_GRAY);
        
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb = image.getRGB(x, y);
                int gray = (int) (0.299 * ((rgb >> 16) & 0xFF) + 
                                  0.587 * ((rgb >> 8) & 0xFF) + 
                                  0.114 * (rgb & 0xFF));
                int newRgb = (gray << 16) | (gray << 8) | gray;
                grayscaleImage.setRGB(x, y, newRgb);
            }
        }
        
        return grayscaleImage;
    }
    
    /**
     * 应用阈值处理
     */
    private static BufferedImage applyThreshold(BufferedImage image, int threshold) {
        int width = image.getWidth();
        int height = image.getHeight();
        
        BufferedImage result = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_GRAY);
        
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb = image.getRGB(x, y);
                int gray = rgb & 0xFF; // 获取灰度值
                
                // 应用阈值：小于阈值的设为0（黑色），大于等于阈值的设为255（白色）
                int newGray = (gray < threshold) ? 0 : 255;
                
                // 设置灰度值
                int newRgb = (newGray << 16) | (newGray << 8) | newGray;
                result.setRGB(x, y, newRgb);
            }
        }
        
        return result;
    }
    
    /**
     * 应用像素连接
     */
    private static BufferedImage applyPixelConnection(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        
        // 转换为数组以便直接修改
        int[][] pixelArray = new int[height][width];
        
        // 初始化像素数组
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb = image.getRGB(x, y);
                pixelArray[y][x] = rgb & 0xFF; // 获取灰度值
            }
        }
        
        // 横向扫描，相差两个像素进行连接
        for (int y = 0; y < height; y++) {
            for (int x = 2; x < width - 2; x++) {
                if (pixelArray[y][x - 2] == 0 && pixelArray[y][x - 1] == 255 && 
                    pixelArray[y][x] == 255 && pixelArray[y][x + 1] == 255 && 
                    pixelArray[y][x + 2] == 0) {
                    // 将中间的白色像素连接为黑色
                    pixelArray[y][x - 1] = 0;
                    pixelArray[y][x] = 0;
                    pixelArray[y][x + 1] = 0;
                }
            }
        }
        
        // 纵向扫描，相差两个像素进行连接
        for (int x = 0; x < width; x++) {
            for (int y = 2; y < height - 2; y++) {
                if (pixelArray[y - 2][x] == 0 && pixelArray[y - 1][x] == 255 && 
                    pixelArray[y][x] == 255 && pixelArray[y + 1][x] == 255 && 
                    pixelArray[y + 2][x] == 0) {
                    // 将中间的白色像素连接为黑色
                    pixelArray[y - 1][x] = 0;
                    pixelArray[y][x] = 0;
                    pixelArray[y + 1][x] = 0;
                }
            }
        }
        
        // 将修改后的数组转换回BufferedImage
        BufferedImage result = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int gray = pixelArray[y][x];
                int rgb = (gray << 16) | (gray << 8) | gray;
                result.setRGB(x, y, rgb);
            }
        }
        
        return result;
    }
    
    /**
     * 将灰度图转换为RGB格式
     */
    private static BufferedImage convertGrayscaleToRGB(BufferedImage grayscaleImage) {
        int width = grayscaleImage.getWidth();
        int height = grayscaleImage.getHeight();
        
        BufferedImage rgbImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb = grayscaleImage.getRGB(x, y);
                rgbImage.setRGB(x, y, rgb);
            }
        }
        
        return rgbImage;
    }
} 