package com.chaty.dto;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

@Data
public class GradingCorrectDTO extends BaseDTO {

    private String id;
    /**
     * 模板任务ID
     */
    private String templateId;
    /**
     * 模板任务
     */
    private String templateName;
    /**
     * 总题目数量
     */
    private Integer totalNum;
    /**
     * 纠错数量
     */
    private Integer fixedNum;
    /**
     * 纠错题目统计数据
     */
    private String qsStats;

    /**
     * 批改失败的数量
     */
    private Integer failNum;

    private Integer modifyNum;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    private String stats;

    private String remark;

    private String tenantId;
}