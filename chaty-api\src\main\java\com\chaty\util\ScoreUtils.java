package com.chaty.util;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.chaty.dto.DocCorrectConfigDTO;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class ScoreUtils {


    public static JSONObject mergeScoreTypes(List<JSONObject> scoreTypes) {
        JSONObject mergedResult = new JSONObject();
        JSONObject mergedScoreTypeObj = new JSONObject();
        Double totalScore = 0D;
        // 遍历每一个传入的 scoreTypes
        for (JSONObject scoreType : scoreTypes) {
            JSONObject scoreTypeObj = scoreType.getJSONObject("scoreTypeObj");

            // 遍历每个评分类型及其分数
            for (String key : scoreTypeObj.keySet()) {
                Double score = scoreTypeObj.getDouble(key);
                if (!key.equals("总分")) {
                    totalScore += score;
                }
                if (mergedScoreTypeObj.containsKey(key)) {
                    // 如果该评分类型已经存在，累加分数
                    mergedScoreTypeObj.set(key, mergedScoreTypeObj.getDouble(key) + score);
                } else {
                    // 如果评分类型不存在，直接添加
                    mergedScoreTypeObj.set(key, score);
                }
            }
        }

        // 将合并的结果设置到 mergedResult 中
        JSONArray mergedScoreTypes = new JSONArray();
        JSONArray mergedScoreTypesMaxScore = new JSONArray();

        for (String key : mergedScoreTypeObj.keySet()) {
            if (!key.equals("总分")) {
                mergedScoreTypes.put(key);
                mergedScoreTypesMaxScore.put(mergedScoreTypeObj.getDouble(key));
            }
        }
        // add total,保证最后一个是总分
        mergedScoreTypes.put("总分");
        mergedScoreTypesMaxScore.put(totalScore);
        mergedScoreTypeObj.set("总分", totalScore);

        mergedResult.set("scoreTypes", mergedScoreTypes);
        mergedResult.set("scoreTypesMaxScore", mergedScoreTypesMaxScore);
        mergedResult.set("scoreTypeObj", mergedScoreTypeObj);

        return mergedResult;
    }


    /**
     * 需要排序
     *
     * @param docCorrectConfigDTOS
     * @return
     */
    public static JSONObject mergeScoreTypesFromDTO(List<DocCorrectConfigDTO> docCorrectConfigDTOS) {
        return getScoreType(docCorrectConfigDTOS);
    }

    public static JSONObject getScoreType(List<DocCorrectConfigDTO> docCorrectConfigDTOS) {
        JSONObject res = new JSONObject();
        JSONArray scoreTypes = new JSONArray();
        JSONArray scoreTypesMaxScore = new JSONArray();
        JSONObject scoreTypeObj = new JSONObject();
        scoreTypeObj.set("总分",0D);
        for (DocCorrectConfigDTO docCorrectConfigDTO : docCorrectConfigDTOS) {
            JSONArray areas = docCorrectConfigDTO.getAreasObj();
            areas.forEach(questionsObj -> {
                JSONObject obj = (JSONObject) questionsObj;
                JSONArray questions = obj.getJSONArray("questions");
                questions.forEach(questionObj -> {
                    JSONObject question = (JSONObject) questionObj;
                    String scoreType = "总分";
                    if (question.containsKey("scoreType")) {
                        scoreType = question.getStr("scoreType");
                    }
                    Double score = 0D;
                    if (question.containsKey("score")) {
                        score = question.getDouble("score");
                    }
                    if (!scoreType.equals("总分")) {
                        if (scoreTypeObj.containsKey(scoreType)) {
                            scoreTypeObj.set(scoreType, scoreTypeObj.getDouble(scoreType) + score);
                        } else {
                            scoreTypeObj.set(scoreType, score);
                            scoreTypes.put(scoreType);
                        }
                    }
                    scoreTypeObj.set("总分", scoreTypeObj.getDouble("总分") + score);
                });
            });
        }
        if (!scoreTypes.contains("总分")) {
            scoreTypes.put("总分");
        }
        for (int i = 0; i < scoreTypes.size(); i++) {
            scoreTypesMaxScore.set(scoreTypeObj.get(scoreTypes.get(i)));
        }

        res.set("scoreTypes", scoreTypes);
        res.set("scoreTypesMaxScore", scoreTypesMaxScore);
        res.set("scoreTypeObj", scoreTypeObj);
        return res;
    }
}
