package com.chaty.entity.admin;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.chaty.entity.ClassStatisticDataItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ClassStatisticDataReq {

    private List<ClassStatisticDataItem> fileDetails;

    private Double youxiuScore;

    private Double hegeScore;

    /**
     * 学科名称
     */
    private String subject;

    /**
     * 年级
     */
    private String gradeName;

    /**
     * 科目名称
     */
    private String subjectName;

    /**
     * 数量界限
     */
    private Integer studentNumberLimit;

    /**
     * 等级
     */
    private JSONObject ranges;

    private Boolean orderByStudentNumber;

    private Integer realClassNum;
}

