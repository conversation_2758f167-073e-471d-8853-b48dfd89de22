package com.chaty.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.util.StrUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.dto.CorrectAreaDTO;
import com.chaty.dto.CorrectAreaQsDTO;
import com.chaty.dto.DataCounter;
import com.chaty.dto.DocCorrectConfigDTO;
import com.chaty.dto.QuickFixDTO;
import com.chaty.entity.DocCorrectConfig;
import com.chaty.entity.DocCorrectFile;
import com.chaty.entity.DocCorrectRecord;
import com.chaty.entity.DocCorrectTask;
import com.chaty.entity.QuickFix;
import com.chaty.enums.CorrectEnums;
import com.chaty.exception.BaseException;
import com.chaty.mapper.DocCorrectConfigMapper;
import com.chaty.mapper.DocCorrectFileMapper;
import com.chaty.mapper.DocCorrectRecordMapper;
import com.chaty.mapper.DocCorrectTaskMapper;
import com.chaty.mapper.QuickFixMapper;
import com.chaty.service.PDFService;
import com.chaty.service.PDFService.TexCmd;
import com.chaty.service.QuickFixServce;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class QuickFixServiceImpl extends ServiceImpl<QuickFixMapper, QuickFix> implements QuickFixServce {

    @Resource
    private DocCorrectFileMapper docCorrectFileMapper;
    @Resource
    private QuickFixMapper quickFixMapper;
    @Resource
    private DocCorrectTaskMapper docCorrectTaskMapper;
    @Resource
    private DocCorrectRecordMapper docCorrectRecordMapper;
    @Resource
    private DocCorrectConfigMapper docCorrectConfigMapper;
    @Resource
    private PDFService pdfService;
    @Resource
    private GptAskLogServiceImpl gptAskLogService;

    /**
     * 纠错
     */
    @Override
    public QuickFix doFix(QuickFixDTO params) {
        // 校验数据
        String templateId = params.getTemplateId();
        String fixedId = params.getFixedId();
        DocCorrectFile tempFile = docCorrectFileMapper.selectById(templateId);
        DocCorrectFile fixedFile = docCorrectFileMapper.selectById(fixedId);
        // 查询批改任务
        if (tempFile == null || fixedFile == null) {
            throw new BaseException("未查询到批改任务");
        }
        // 查询任务列表
        List<DocCorrectTask> tempTasks = docCorrectTaskMapper
                .selectList(Wrappers.lambdaQuery(DocCorrectTask.class)
                        .eq(DocCorrectTask::getFileId, templateId)
                        .orderByAsc(DocCorrectTask::getName));
        List<DocCorrectTask> fixedTasks = docCorrectTaskMapper
                .selectList(Wrappers.lambdaQuery(DocCorrectTask.class)
                        .eq(DocCorrectTask::getFileId, fixedId)
                        .orderByAsc(DocCorrectTask::getName));
        // 判断任务数量是否一致
        if (tempTasks.size() != fixedTasks.size()) {
            throw new BaseException("任务数量不一致");
        }
        // 查询任务状态
        if (tempTasks.stream().anyMatch(task -> !task.getStatus().equals(CorrectEnums.CorrectTakStatus.FINISH))) {
            throw new BaseException("模板任务未批改完成");
        }
        if (fixedTasks.stream().anyMatch(task -> !task.getStatus().equals(CorrectEnums.CorrectTakStatus.FINISH))) {
            throw new BaseException("纠错任务未批改完成");
        }
        // 查询批改试卷列表
        List<List<DocCorrectRecord>> tempTaskRecords = tempTasks.stream().map(task -> {
            return docCorrectRecordMapper.selectList(Wrappers.lambdaQuery(DocCorrectRecord.class)
                    .eq(DocCorrectRecord::getTaskId, task.getId())
                    .orderByAsc(DocCorrectRecord::getDocname));
        }).collect(Collectors.toList());
        List<List<DocCorrectRecord>> fixedTaskRecords = fixedTasks.stream().map(task -> {
            return docCorrectRecordMapper.selectList(Wrappers.lambdaQuery(DocCorrectRecord.class)
                    .eq(DocCorrectRecord::getTaskId, task.getId())
                    .orderByAsc(DocCorrectRecord::getDocname));
        }).collect(Collectors.toList());
        // 判断数量是否一致
        // if (tempRecords.size() != fixedRecords.size()) {
        //     throw new BaseException("试卷数量不一致");
        // }
        // 开始纠错
        List<QuickFixDTO> results = new ArrayList<>();
        DataCounter qsFixCounter = new DataCounter();
        try {
            for (int docIdx = 0; docIdx < tempTaskRecords.get(0).size(); docIdx++) {
                for (int taskIdx = 0; taskIdx < tempTaskRecords.size(); taskIdx++) {
                    DocCorrectRecord template = tempTaskRecords.get(taskIdx).get(docIdx);
                    DocCorrectRecord fixed = fixedTaskRecords.get(taskIdx).get(docIdx);
                    QuickFixDTO res = fixRecord(template, fixed, qsFixCounter);
                    // 保存纠错结果
                    results.add(res);
                }
                qsFixCounter.resetIndex(); // 重置索引
            }
        } catch (BaseException e) {
            throw e;
        } catch (IndexOutOfBoundsException e) {
            throw new BaseException("试卷数量不一致,请检查试卷数量和试题数量是否配置正确!", e);
        } catch (Exception e) {
            throw new BaseException("纠错失败!", e);
        }

        // 合并纠错结果
        Integer studentNumberSumCount = 0, studentNumberRightCount = 0, studentNumberWrongCount = 0;
        Integer identifySumCount = 0, identifyRightCount = 0, identifyWrongCount = 0;

        List<DocCorrectRecord> updateRecords = new ArrayList<>();
        Integer totalNum = 0, fixedNum = 0, failNum = 0, correctNum = 0;
        for (QuickFixDTO res : results) {
            totalNum += res.getTotalNum();
            fixedNum += res.getFixedNum();
            failNum += res.getFailNum();
            correctNum += res.getCorrectNum();
            if (res.getFixedNum() > 0) {
                DocCorrectRecord record = new DocCorrectRecord();
                record.setId(res.getFixedId());
                record.setHasChange(1);
                record.setReviewed(res.getFixedReviewed());
                updateRecords.add(record);
            }
            studentNumberSumCount += res.getStudentNumberSumCount();
            studentNumberRightCount += res.getStudentNumberRightCount();
            studentNumberWrongCount += res.getStudentNumberWrongCount();
            identifySumCount += res.getIdentifySumCount();
            identifyRightCount += res.getIdentifyRightCount();
            identifyWrongCount += res.getIdentifyWrongCount();
        }
        // 保存纠错结果
        QuickFix quickFix = new QuickFix();
        quickFix.setTemplateId(templateId);
        quickFix.setTemplateName(tempFile.getName());
        quickFix.setFixedId(fixedId);
        quickFix.setFixedName(fixedFile.getName());
        quickFix.setTotalNum(totalNum);
        quickFix.setFixedNum(fixedNum);
        quickFix.setFailNum(failNum);
        quickFix.setCorrectNum(correctNum);
        quickFix.setQsStats(qsFixCounter.toStr());
        if (StrUtil.isNotBlank(params.getRemark())) {
            quickFix.setRemark(params.getRemark());
        }

        quickFix.setIdentifySumCount(identifySumCount);
        quickFix.setIdentifyRightCount(identifyRightCount);
        quickFix.setIdentifyWrongCount(identifyWrongCount);
        if (identifySumCount == 0) {
            quickFix.setIdentifyRatio(0.00);
        } else {
            // 四舍五入，保留两位
            double ratioIdentify = ((double) identifyRightCount / identifySumCount) * 100;
            BigDecimal bdIdentify = new BigDecimal(ratioIdentify);
            bdIdentify = bdIdentify.setScale(2, RoundingMode.HALF_UP);
            quickFix.setIdentifyRatio(bdIdentify.doubleValue());
        }
        quickFix.setStudentNumberSumCount(studentNumberSumCount);
        quickFix.setStudentNumberRightCount(studentNumberRightCount);
        quickFix.setStudentNumberWrongCount(studentNumberWrongCount);
        // 四舍五入，保留两位
        if (studentNumberSumCount == 0) {
            quickFix.setStudentNumberRatio(0.00);
        } else {
            double ratio = ((double) studentNumberRightCount / studentNumberSumCount) * 100;
            BigDecimal bd = new BigDecimal(ratio);
            bd = bd.setScale(2, RoundingMode.HALF_UP);
            quickFix.setStudentNumberRatio(bd.doubleValue());
        }
        // 更新纠错记录
        for (DocCorrectRecord record : updateRecords) {
            docCorrectRecordMapper.updateById(record);
        }

        // file更新为已完成
        DocCorrectFile docCorrectFile = new DocCorrectFile();
        docCorrectFile.setId(params.getFixedId());
        docCorrectFile.setIsCorrectFinish(1);
        docCorrectFileMapper.updateById(docCorrectFile);

        // 统计平均时间
        try {
            String stats = JSONUtil.toJsonStr(gptAskLogService.getStatsByFileId(params.getFixedId()));
            quickFix.setStats(stats);
        }catch (Exception e) {
            log.error("统计平均时间失败", e);
        }

        quickFixMapper.insert(quickFix);

        return quickFix;
    }

    /**
     * 试卷纠错
     * fixed是待纠错
     * template是 已纠错
     */
    public QuickFixDTO fixRecord(DocCorrectRecord template, DocCorrectRecord fixed, DataCounter qsFixCounter) {
//        log.info("开始纠错试卷：{} -> {}", template.getDocname(), fixed.getDocname());
        QuickFixDTO fixedRes = new QuickFixDTO();
        fixedRes.setFixedId(fixed.getId());
        Integer totalNum = 0, fixedNum = 0, failNum = 0, correctNum = 0;
        // 纠错学号
        JSONArray areas = JSONUtil.parseArray(template.getReviewed());
        JSONArray fixedAreas = JSONUtil.parseArray(fixed.getReviewed());
        // 数量校验
        if (areas.size() != fixedAreas.size()) {
            throw new BaseException("试卷区域数量不一致, 试卷：" + fixed.getDocname());
        }
        // 遍历区域
        for (int areaIdx = 0; areaIdx < areas.size(); areaIdx++) {
            CorrectAreaDTO tempArea = CorrectAreaDTO.create(areas.getJSONObject(areaIdx));
            CorrectAreaDTO fixedArea = CorrectAreaDTO.create(fixedAreas.getJSONObject(areaIdx));
            // if (!tempArea.isSuccess()) {
            // throw new BaseException(String.format("纠错失败, 纠错试卷 %s 的区域 %s 未批改成功",
            // fixed.getDocname(), areaIdx + 1));
            // }
            // if (!fixedArea.isSuccess()) {
            // throw new BaseException(String.format("纠错失败, 模板试卷 %s 的区域 %s 未批改成功",
            // fixed.getDocname(), areaIdx + 1));
            // }
            // 题目数量校验
            if (tempArea.getReviewed().size() != fixedArea.getReviewed().size()) {
                throw new BaseException(String.format("试卷 %s 的区域 %s 题目数量不一致", fixed.getDocname(), areaIdx + 1));
            }
            // 统计题目数量
            totalNum += tempArea.getReviewed().size();
            // 遍历题目
            Boolean isSuccess = fixedArea.isSuccess();
            for (int qsIdx = 0; qsIdx < tempArea.getReviewed().size(); qsIdx++) {
                CorrectAreaQsDTO tempQs = CorrectAreaQsDTO.create(tempArea.getReviewed().get(qsIdx));
                CorrectAreaQsDTO fixedQs = CorrectAreaQsDTO.create(fixedArea.getReviewed().get(qsIdx));
                // 比较批改结果
                if (Objects.isNull(tempQs.getIsScorePoint()) || !Objects.equals(tempQs.getIsScorePoint(), 2)) {
                    // 普通题目
                    if (!isSuccess) {
//                        fixedArea.setStatus(1);
                        // 批改失败，直接纠正
                        if (tempQs.getCorrect() != fixedQs.getCorrect()) {
                            fixedQs.changeNormalCorrectVal();
                        }
                        // 设置题目结果
                        fixedArea.getReviewed().set(qsIdx, fixedQs.toJSONObj());
                        // 纠错数量+1
                        fixedNum++;
                        // 错误数量+1
                        failNum++;
                        // 统计纠错数量
                        qsFixCounter.countFailOne();

                    } else if (tempQs.getCorrect() != fixedQs.getCorrect()) {
                        // 修改批改结果
                        fixedQs.changeNormalCorrectVal();
                        // 设置题目结果
                        fixedArea.getReviewed().set(qsIdx, fixedQs.toJSONObj());
                        // 纠错数量+1
                        fixedNum++;
                        // 统计纠错数量
                        qsFixCounter.countOne();
                    } else {
                        // 统计正确数量
                        qsFixCounter.countZero();
                    }
                } else {
                    // 手写数字识别要对比分数
                    if (!isSuccess) {
                        if (tempQs.getScoredVal().compareTo(fixedQs.getScoredVal()) != 0) {
                            fixedQs.changeScorePointCorrectVal(tempQs.getScoredVal());
                        }
                        // 设置题目结果
                        fixedArea.getReviewed().set(qsIdx, fixedQs.toJSONObj());
                        // 纠错数量+1
                        fixedNum++;
                        // 失败数量+1
                        failNum++;
                        // 统计纠错数量
                        qsFixCounter.countFailOne();
                    } else if (tempQs.getScoredVal().compareTo(fixedQs.getScoredVal()) != 0) {
                        log.info("试卷 {} 区域 {} 题目 {} 批改结果不一致", fixed.getDocname(), areaIdx + 1, qsIdx + 1);
                        // 修改批改结果
                        fixedQs.changeScorePointCorrectVal(tempQs.getScoredVal());
                        // 设置题目结果
                        fixedArea.getReviewed().set(qsIdx, fixedQs.toJSONObj());
                        // 纠错数量+1
                        fixedNum++;
                        // 统计纠错数量
                        qsFixCounter.countOne();
                    } else {
                        // 统计正确数量
                        qsFixCounter.countZero();
                    }
                }
                // 统计纠错题目数量
                if (fixedQs.hasChangeVal()) {
                    correctNum++;
                }
            }
            // 设置区域结果
            areas.set(areaIdx, fixedArea.toJSONObj());
        }

        fixedRes.setFixedReviewed(areas.toString());
        fixedRes.setTotalNum(totalNum);
        fixedRes.setFixedNum(fixedNum);
        fixedRes.setFailNum(failNum);
        fixedRes.setCorrectNum(correctNum);

        // 模板有值才对比
        if (StrUtil.isNotBlank(template.getIdentify())){
            fixedRes.setIdentifySumCount(1);
            if (template.getIdentify().equals(fixed.getIdentify())) {
                fixedRes.setIdentifyRightCount(1);
                fixedRes.setIdentifyWrongCount(0);
            } else {
                fixedRes.setIdentifyRightCount(0);
                fixedRes.setIdentifyWrongCount(1);
            }
        } else {
            fixedRes.setIdentifySumCount(0);
            fixedRes.setIdentifyRightCount(0);
            fixedRes.setIdentifyWrongCount(0);
        }

        if (StrUtil.isNotBlank(template.getStudentNumber())){
            fixedRes.setStudentNumberSumCount(1);
            if (template.getStudentNumber().equals(fixed.getStudentNumber())) {
                fixedRes.setStudentNumberRightCount(1);
                fixedRes.setStudentNumberWrongCount(0);
            } else {
                fixedRes.setStudentNumberRightCount(0);
                fixedRes.setStudentNumberWrongCount(1);
            }
        } else {
            fixedRes.setStudentNumberSumCount(0);
            fixedRes.setStudentNumberRightCount(0);
            fixedRes.setStudentNumberWrongCount(0);
        }

        return fixedRes;
    }

    @Override
    public IPage<QuickFixDTO> getPage(QuickFixDTO params) {
        LambdaQueryWrapper<QuickFix> wrapper = Wrappers.lambdaQuery(QuickFix.class)
                .orderByDesc(QuickFix::getCreateTime);
        return page(params.getPage().page(QuickFix.class), wrapper)
                .convert(entity -> BeanUtil.copyProperties(entity, QuickFixDTO.class));
    }

    @Override
    public Map<String, Object> getFile(String id) {
        QuickFix quickFix = quickFixMapper.selectById(id);
        Optional.ofNullable(quickFix).orElseThrow(() -> new BaseException("未查询到数据"));
        List<DocCorrectTask> tempTasks = docCorrectTaskMapper.selectList(Wrappers.lambdaQuery(DocCorrectTask.class)
                .eq(DocCorrectTask::getFileId, quickFix.getFixedId())
                .orderByAsc(DocCorrectTask::getName));
        List<DocCorrectConfigDTO> configs = tempTasks.stream().map(task -> {
            DocCorrectConfig config = docCorrectConfigMapper.selectById(task.getConfigId());
            Optional.ofNullable(config).orElseThrow(() -> new BaseException("未查询到试卷配置"));
            return BeanUtil.toBean(config, DocCorrectConfigDTO.class);
        }).collect(Collectors.toList());
        Optional.ofNullable(quickFix.getQsStats()).orElseThrow(() -> new BaseException("未查询到题目统计数据"));
        JSONObject qsStats = JSONUtil.parseObj(quickFix.getQsStats());
        Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>())
            .put("quickfix", quickFix)
            .put("configs", configs)
            .put("qsStats", qsStats)
            .build();
        return pdfService.createDoc(TexCmd.PDFLATEX, "quickfix", params);
    }

}
