package com.chaty.api.alist;

import feign.Headers;
import feign.Param;
import feign.RequestLine;

import java.io.File;
import java.util.Map;

public interface AlistApi {
    
    @RequestLine("POST /api/fs/list")
    @Headers("Content-Type: application/json")
    AlistBaseResponse<FsListResponse> fsList(FsListRequest request);

    @RequestLine("PUT /api/fs/form")
    @Headers({
            "Content-Type: multipart/form-data",
//            "Content-Length: {contentLength}",
            "File-Path: {filePath}",
            "As-Task: {asTask}"
    })
    AlistBaseResponse<?> fsForm(@Param("filePath") String filePath,
                                @Param("asTask") boolean asTask, @Param("file") File file);

    @RequestLine("POST /api/fs/mkdir")
    @Headers("Content-Type: application/json")
    AlistBaseResponse<?> createFolder(Map<String, String> req);
}
