package com.chaty.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.chaty.dto.DocCorrectResultDTO;
import com.chaty.entity.DocCorrectResult;

@Mapper
public interface DocCorrectResultMapper extends BaseMapper<DocCorrectResult> {

    @Select("select dcr.*, dcrr.identify from doc_correct_result dcr left join doc_correct_record dcrr on dcr.record_id = dcrr.id left join doc_correct_task dct on dcr.task_id = dct.id ${ew.customSqlSegment}")
    IPage<DocCorrectResultDTO> selectDTOPage(IPage<?> page, @Param(Constants.WRAPPER) Wrapper<?> wrapper);

    @Update("UPDATE doc_correct_result dcr,(\n" + //
            "SELECT area_idx,qs_idx,sum(is_correct) AS is_correct,count(1) AS total FROM doc_correct_result WHERE deleted=0 AND task_id=#{taskId} GROUP BY area_idx,qs_idx) r2\n"
            + //
            "SET dcr.correct_rate=ROUND((100.0*r2.is_correct/r2.total),2) WHERE dcr.task_id=#{taskId} AND dcr.area_idx=r2.area_idx AND dcr.qs_idx=r2.qs_idx")
    void setCorrectRateByTaskId(@Param("taskId") String taskId);

}