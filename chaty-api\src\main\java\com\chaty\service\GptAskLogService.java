package com.chaty.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.dto.GptAskLogDTO;
import com.chaty.dto.RecordTimeStatsDTO;
import com.chaty.entity.GptAskLogEntity;

import java.util.List;

public interface GptAskLogService extends IService<GptAskLogEntity> {
    IPage<GptAskLogEntity> page(GptAskLogDTO param);

    Long add(GptAskLogEntity param);

    Long update(GptAskLogEntity param);

    void delete(Long id);

    GptAskLogEntity getById(Long id);

    /**
     * 根据 recordName 统计所有同名记录的平均耗时、总耗时和总数量
     *
     * @param recordName 记录名称
     * @return 汇总结果 DTO
     */
    RecordTimeStatsDTO getStatsByRecordName(String recordName);

    /**
     * 根据用户模糊搜索串，返回去重后的 recordName 列表
     *
     * @param searchStr 模糊匹配串
     * @return 去重后的 recordName 列表
     */
    List<String> getDistinctRecordNames(String searchStr);


    /**
     * 按 fileId 统计请求耗时：平均值、总和、总条数以及空值数
     *
     * @param fileId 日志中的 file_id
     * @return 包含统计结果的 DTO
     */
    RecordTimeStatsDTO getStatsByFileId(String fileId);

    /**
     * 统计 gpt_ask_log 表全体记录数量
     *
     * @return 全体记录数量
     */
    Long getRequestCount();
}
