\documentclass[a4paper]{ctexart}
\usepackage[absolute, overlay]{textpos}
\usepackage{fancyhdr}
\usepackage{pifont}
\usepackage{xcolor}
\usepackage{graphicx}
\usepackage{wrapfig}
\usepackage{tikz}

\usepackage{mdframed}

\pagestyle{fancy}
\fancyhf{}
\renewcommand{\headrulewidth}{0pt}
\setlength{\TPHorizModule}{1mm}
\setlength{\TPVertModule}{1mm}

\begin{document}

<#list questions as question>

\subsection*{${question.id}}

\begin{tikzpicture}[overlay, remember picture]
\draw (-2.46cm,1.2cm) rectangle (-1.46cm,0.6cm);
\end{tikzpicture}
${question.question}
\begin{mdframed}[linewidth=1pt]
\begin{minipage}[t][${question.height}cm]{\textwidth}
  \begin{tikzpicture}[overlay, remember picture]
    \draw (-2.1cm,0.25cm) rectangle (-1.1cm,-0.3cm); % Adjust the Y-coordinates to match the height of the minipage
  \end{tikzpicture}  
\end{minipage}
\end{mdframed}

\begin{tikzpicture}[overlay, remember picture]
\draw (-2.46cm,1.2cm) rectangle (-1.46cm,0.6cm); % Adjust the Y-coordinates to match the height of the minipage
\end{tikzpicture} 

</#list>

\end{document}