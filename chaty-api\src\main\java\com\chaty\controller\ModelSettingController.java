package com.chaty.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.common.BaseResponse;
import com.chaty.dto.ModelSettingDTO;
import com.chaty.dto.PageDTO;
import com.chaty.entity.ModelSetting;
import com.chaty.entity.PromptReq;
import com.chaty.service.cache.DefaultModelRedisService;
import com.chaty.service.cache.ModelSettingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/api/model-request")
public class ModelSettingController {

    @Autowired
    private ModelSettingService modelSettingService;

    @Autowired
    private DefaultModelRedisService defaultModelRedisService;


    @PostMapping("/add")
    public BaseResponse<?> addModelRequest(@RequestBody ModelSettingDTO modelRequestDTO) {
        try {
            ModelSetting modelSetting = modelSettingService.addModelRequest(modelRequestDTO);
            return BaseResponse.ok(modelSetting);
        } catch (Exception e) {
            log.error("添加模型报错：{}", e.getMessage());
            return BaseResponse.error("添加模型失败" );
        }
    }

    @GetMapping("/delete")
    public BaseResponse<?> deleteModelRequest(@RequestParam Integer id) {
        modelSettingService.deleteModelRequest(id);
        return BaseResponse.ok("删除成功");
    }

    @PostMapping("/update")
    public BaseResponse<?> updateModelRequest(@RequestBody ModelSettingDTO modelRequestDTO) {
        try {
            return BaseResponse.ok(modelSettingService.updateModelRequest(modelRequestDTO));
        } catch (Exception e) {
            return BaseResponse.error("更新模型失败");
        }
    }

    @PostMapping("/addPrompt")
    public BaseResponse<?> addPromptModelRequest(@RequestBody PromptReq promptReq) {
        ModelSetting dbModelSetting = modelSettingService.getById(promptReq.getModelRequestId());

        if (Objects.isNull(dbModelSetting)) {
            return BaseResponse.error("找不到该模型");
        }
        ModelSettingDTO dbModelRequestDTO = new ModelSettingDTO();
        BeanUtils.copyProperties(dbModelSetting, dbModelRequestDTO);

        JSONObject promptObj = dbModelRequestDTO.getPromptObj();
        if (promptObj.containsKey(promptReq.getKey())) {
            return BaseResponse.error("该模型已存在该key");
        }
        promptObj.put(promptReq.getKey(), promptReq.getValue());
        dbModelRequestDTO.setPrompt(JSONUtil.toJsonStr(promptObj));
        modelSettingService.updateModelRequest(dbModelRequestDTO);
        return BaseResponse.ok("添加成功");
    }

    /**
     * 更新模型请求中的提示信息
     */
    @PostMapping("/updatePrompt")
    public BaseResponse<?> updatePrompt(@RequestBody PromptReq promptReq) {
        ModelSetting dbModelSetting = modelSettingService.getById(promptReq.getModelRequestId());

        if (Objects.isNull(dbModelSetting)) {
            return BaseResponse.error("找不到该模型");
        }

        ModelSettingDTO dbModelRequestDTO = new ModelSettingDTO();
        BeanUtils.copyProperties(dbModelSetting, dbModelRequestDTO);

        JSONObject promptObj = dbModelRequestDTO.getPromptObj();
        if (!promptObj.containsKey(promptReq.getKey())) {
            return BaseResponse.error("该模型不存在该key");
        }

        promptObj.put(promptReq.getKey(), promptReq.getValue());
        dbModelRequestDTO.setPrompt(JSONUtil.toJsonStr(promptObj));

        // 更新实体对象
        BeanUtils.copyProperties(dbModelRequestDTO, dbModelSetting);
        modelSettingService.updateModelRequest(dbModelRequestDTO);

        return BaseResponse.ok("更新成功");
    }

    /**
     * 删除模型请求中的提示信息
     */
    @PostMapping("/deletePrompt")
    public BaseResponse<?> deletePrompt(@RequestBody PromptReq promptReq) {
        ModelSetting dbModelSetting = modelSettingService.getById(promptReq.getModelRequestId());

        if (Objects.isNull(dbModelSetting)) {
            return BaseResponse.error("找不到该模型");
        }

        ModelSettingDTO dbModelRequestDTO = new ModelSettingDTO();
        BeanUtils.copyProperties(dbModelSetting, dbModelRequestDTO);

        JSONObject promptObj = dbModelRequestDTO.getPromptObj();
        if (!promptObj.containsKey(promptReq.getKey())) {
            return BaseResponse.error("该模型不存在该key");
        }

        promptObj.remove(promptReq.getKey());
        dbModelRequestDTO.setPrompt(JSONUtil.toJsonStr(promptObj));

        // 更新实体对象
        BeanUtils.copyProperties(dbModelRequestDTO, dbModelSetting);
        modelSettingService.updateModelRequest(dbModelRequestDTO);

        return BaseResponse.ok("删除成功");
    }


    @PostMapping("/selectPage")
    public BaseResponse<IPage<ModelSettingDTO>> selectPage(@RequestBody ModelSettingDTO modelRequestDTO) {
        IPage<ModelSettingDTO> result = modelSettingService.selectPage(modelRequestDTO);
        return BaseResponse.ok(result);
    }

    @GetMapping("/detail")
    public BaseResponse<?> getDetail(@RequestParam Integer id) {
        // 构造查询参数
        ModelSettingDTO dto = new ModelSettingDTO();
        dto.setId(id);
        PageDTO<?> page = new PageDTO<>();
        page.setPageSize(1);
        page.setPageNumber(1);
        dto.setPage(page);

        IPage<ModelSettingDTO> result = modelSettingService.selectPage(dto);
        if (result.getRecords().isEmpty()) {
            return BaseResponse.error("未找到对应模型");
        }
        return BaseResponse.ok(result.getRecords().get(0));
    }


    @GetMapping("/getMaxEmptyWeight")
    public BaseResponse<Integer> getMaxEmptyWeight() {
        return BaseResponse.ok(modelSettingService.getMaxEmptyWeight());
    }

    // -----------------------
    // 默认模型配置相关接口
    // -----------------------

    /**
     * 获取当前默认模型配置信息
     */
    @GetMapping("/default/get")
    public BaseResponse<ModelSetting> getDefaultModel() {
        ModelSetting req = defaultModelRedisService.getModelRequest();
        return BaseResponse.ok(req);
    }

    /**
     * 更新默认模型配置：根据传入的 ModelRequest ID
     */
    @PostMapping("/default/update/{id}")
    public BaseResponse<?> updateDefaultModel(@PathVariable Integer id) {
        ModelSetting req = modelSettingService.getById(id);
        if (req == null) {
            return BaseResponse.error("未找到对应的模型请求");
        }
        defaultModelRedisService.update(req);
        return BaseResponse.ok("默认模型已更新");
    }

    /**
     * 重置默认模型所有配置为初始值
     */
    @PostMapping("/default/reset")
    public BaseResponse<?> resetDefaultModel() {
        // 重置所有字段
        Map<String, String> defaults = defaultModelRedisService.getAllDefaultConfig();
        defaults.forEach((suffix, val) ->
                defaultModelRedisService.resetValue("defaultModel:" + suffix)
        );
        return BaseResponse.ok("默认模型已重置到初始值");
    }
}
