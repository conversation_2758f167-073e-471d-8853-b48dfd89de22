package com.chaty.service.cache;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.entity.RemarkComment;

public interface RemarkCommentService extends IService<RemarkComment> {

    IPage<RemarkComment> page(Integer pageNumber, Integer pageSize, String keyword);

    /** 新增：写库并同步缓存 */
    Long add(RemarkComment param);

    /** 更新：写库并同步缓存 */
    Long updateOne(RemarkComment param);

    /** 删除：删库并同步缓存 */
    void deleteById(Long id);

    /** 启动或手动：全量重建缓存 */
    void rebuildAllCaches();

    /** 加权随机抽取一条评语（命中概率作为权重） */
    RemarkComment randomOne();
}
