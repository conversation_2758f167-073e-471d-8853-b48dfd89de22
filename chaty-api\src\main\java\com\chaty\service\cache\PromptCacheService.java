package com.chaty.service.cache;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.dto.PromptDTO;
import com.chaty.entity.Prompt;

import java.util.List;

public interface PromptCacheService {

    /** 启动或需要时：清空并全量重建索引 */
    void rebuildAll();

    /** 新增/更新后：重建该条缓存与索引（内部会根据旧值做差异清理） */
    void index(Prompt prompt);

    /** 删除时：移除缓存与索引 */
    void remove(Long id);

    /** 仅返回符合条件的 ID（按更新时间倒序） */
    List<Long> searchIds(PromptDTO param);

    /** Redis 快速分页（按 updateTime 倒序） */
    IPage<Prompt> page(PromptDTO param);
}
