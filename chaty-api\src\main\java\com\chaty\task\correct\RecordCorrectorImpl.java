package com.chaty.task.correct;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.dto.*;
import com.chaty.entity.*;
import com.chaty.enums.CorrectEnums;
import com.chaty.enums.GetOcrContentType;
import com.chaty.enums.GptAskLogType;
import com.chaty.mapper.*;
import com.chaty.service.*;
import com.chaty.service.cache.ModelSettingService;
import com.chaty.service.cache.QuestionTypeService;
import com.chaty.service.cache.RecordModelSettingService;
import com.chaty.tenant.IgnoreTenant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chaty.completion.CompletionService;
import com.chaty.enums.AIModelConsts;
import com.chaty.enums.CorrectEnums.CorrectRecordStatus;
import com.chaty.exception.BaseException;
import com.chaty.task.metrics.TaskTimer;
import com.chaty.task.metrics.TaskTimerMetrics;
import com.chaty.util.FileUtil;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class RecordCorrectorImpl implements RecordCorrector {

    @Resource
    private DocCorrectRecordService docCorrectRecordService;
    @Resource
    private DocCorrectTaskService docCorrectTaskService;
    @Resource
    private DocCorrectTaskMapper docCorrectTaskMapper;
    @Resource
    private DocCorrectRecordMapper docCorrectRecordMapper;
    @Resource
    private DocCorrectConfigMapper docCorrectConfigMapper;
    @Resource
    private Map<String, OCRService> ocrServices;
    @Resource
    private CompletionService completionService;
    @Resource
    private CorrectCacheService correctCacheService;
    @Resource
    private DocCorrectFileMapper docCorrectFileMapper;
    @Resource
    private DocCorrectResultService docCorrectResultService;
    @Resource
    private TaskExecutor correctAreaTaskExecutor;
    @Resource
    private TaskTimerMetrics recordTaskMetrics;
    @Resource
    private TaskTimerMetrics areaTaskMetrics;
    @Resource
    private UserService userService;
    @Resource
    private ModelSettingService modelSettingService;
    @Value("${file.local.path}")
    public String path;
    @Value("${file.local.ctxpath}")
    public String ctxPath;
    @Value("${server.url}")
    public String serverUrl;
    @Resource
    private QuickFixServce quickFixServce;
    @Resource
    private GradingCorrectService gradingCorrectService;
    @Autowired
    private QuickFixMapper quickFixMapper;
    @Autowired
    private GradingCorrectMapper gradingCorrectMapper;
    @Resource
    private GptAskLogService gptAskLogService;
    @Resource
    private RecordModelSettingService recordModelSettingService;
    @Resource
    private QuestionTypeService questionTypeService;
    private final ConcurrentMap<String, List<CompletableFuture<JSONObject>>> correctionTasks = new ConcurrentHashMap<>();


    @Override
    @IgnoreTenant
    public void correct(DocCorrectRecord record) {
        log.info("DocCorrectTask execute, id: {}, taskId: {}, docname: {}", record.getId(), record.getTaskId(), record.getDocname());
        TaskTimer timer = recordTaskMetrics.createTimer(record.getId());
        DocCorrectRecordDTO recordDTO = BeanUtil.copyProperties(record, DocCorrectRecordDTO.class);
        try {
            recordDTO.setDocParh(recordDTO.getDocurl().substring(ctxPath.length() + 1));
            String taskId = record.getTaskId();
            DocCorrectTask task = docCorrectTaskMapper.selectById(taskId);

            if (StrUtil.isBlank(recordDTO.getFileId())) {
                recordDTO.setFileId(task.getFileId());
            }

            Objects.requireNonNull(task, "未查询到任务信息");
            DocCorrectTaskDTO taskDTO = BeanUtil.copyProperties(task, DocCorrectTaskDTO.class);
            DocCorrectConfig config = docCorrectConfigMapper.selectById(task.getConfigId());
            Objects.requireNonNull(config, "未查询到配置信息");
            DocCorrectConfigDTO configDTO = BeanUtil.copyProperties(config, DocCorrectConfigDTO.class);

            recordDTO.setConfig(configDTO);
            recordDTO.setTask(taskDTO);

            // 批改之前，设置批改次数和批改模型
            beforeCorrect(recordDTO);

            // 添加缓存
            correctCacheService.onCorrectRecord(recordDTO);
            timer.step("DocCorrectTask_InitData"); // 记录初始化任务时间

            // PDF图片裁剪
            JSONArray areasObj = FileUtil.INSTANCE.setDocAreasImg(recordDTO.getDocParh(), configDTO.getAreasObj(), recordDTO.getOffsetX(), recordDTO.getOffsetY());
            configDTO.setAreasObj(areasObj);
            timer.step("DocCorrectTask_PdfImgCut"); // 记录裁剪时间

            // 识别试卷姓名
            String identity = ocrForIdentify(recordDTO);
            timer.step("DocCorrectTask_OcrForIdentify"); // 记录识别姓名时间
            // 识别学号
            String studentNumber = ocrForStudentNumber(recordDTO);
            timer.step("DocCorrectTask_ForStudentNumber"); // 记录识别姓名时间
            // 试卷批改
            // JSONArray reviewed = correctAreas(recordDTO);
            JSONArray reviewed = asyncCorrectAreas(recordDTO);
            timer.step("DocCorrectTask_CorrectAreas"); // 记录批改时间
            // 保存批改结果
            DocCorrectRecord update = new DocCorrectRecord();
            update.setId(record.getId());
            update.setConfigId(config.getId());
            update.setStatus(CorrectRecordStatus.FINISH);
            update.setIdentify(identity);
            update.setStudentNumber(studentNumber);
            update.setReviewed(reviewed.toString());
            update.setHasChange(0);
            try {
                onComplete(recordDTO, update);
            } catch (Exception e) {
                log.error("DocCorrectTask onComplete error, id: {}, docname: {}", record.getId(), record.getDocname(), e);
                // 如果onComplete失败，直接抛出异常
                onErrorReTry(recordDTO, update);
            }
            timer.step("DocCorrectTask_SaveReviewed"); // 记录保存数据时间
            recordTaskMetrics.closeTimer(timer);
        } catch (Exception e) {
            log.error("DocCorrectTask execute error, id: {}, docname: {}", record.getId(), record.getDocname(), e);
            onError(recordDTO, e);
        } finally {
            correctCacheService.onRecordCorrected(recordDTO); // 更新缓存
        }
    }

    @Override
    @IgnoreTenant
    public void identifyNameOrStudentNumberType(DocCorrectRecord record, String type) {
        log.info("identifyNameOrStudentNumberType execute, id: {}, taskId: {}, docname: {}", record.getId(), record.getTaskId(), record.getDocname());
        TaskTimer timer = recordTaskMetrics.createTimer(record.getId());
        DocCorrectRecordDTO recordDTO = BeanUtil.copyProperties(record, DocCorrectRecordDTO.class);
        try {
            recordDTO.setDocParh(recordDTO.getDocurl().substring(ctxPath.length() + 1));
            String taskId = record.getTaskId();
            DocCorrectTask task = docCorrectTaskMapper.selectById(taskId);
            Objects.requireNonNull(task, "未查询到任务信息");
            DocCorrectTaskDTO taskDTO = BeanUtil.copyProperties(task, DocCorrectTaskDTO.class);
            DocCorrectConfig config = docCorrectConfigMapper.selectById(task.getConfigId());
            Objects.requireNonNull(config, "未查询到配置信息");
            DocCorrectConfigDTO configDTO = BeanUtil.copyProperties(config, DocCorrectConfigDTO.class);

            recordDTO.setConfig(configDTO);
            recordDTO.setTask(taskDTO);

            // 批改之前，设置批改次数和批改模型
            beforeCorrect(recordDTO);

            // 添加缓存
            correctCacheService.onCorrectRecord(recordDTO);
            timer.step("DocCorrectTask_InitData"); // 记录初始化任务时间

            // 保存批改结果
            DocCorrectRecord update = new DocCorrectRecord();
            update.setId(record.getId());
            update.setStatus(CorrectRecordStatus.FINISH);

            // 识别试卷姓名
            if (CorrectEnums.CorrectEventType.IDENTIFY.equals(type) || CorrectEnums.CorrectEventType.IDENTIFYANDSTUNO.equals(type)) {
                // 识别试卷姓名
                String identity = ocrForIdentify(recordDTO);
                timer.step("DocCorrectTask_OcrForIdentify");
                update.setIdentify(identity);
            }
            // 识别学号
            if (CorrectEnums.CorrectEventType.STUNO.equals(type) || CorrectEnums.CorrectEventType.IDENTIFYANDSTUNO.equals(type)) {
                // 识别学号
                String studentNumber = ocrForStudentNumber(recordDTO);
                timer.step("DocCorrectTask_ForStudentNumber");
                update.setStudentNumber(studentNumber);
            }
            try {
                onComplete(recordDTO, update);
            } catch (Exception e) {
                log.error("DocCorrectTask onComplete error, id: {}, docname: {}", record.getId(), record.getDocname(), e);
                // 如果onComplete失败，直接抛出异常
                onErrorReTry(recordDTO, update);
            }
            timer.step("DocCorrectTask_SaveReviewed"); // 记录保存数据时间
            recordTaskMetrics.closeTimer(timer);
        } catch (Exception e) {
            log.error("DocCorrectTask execute error, id: {}, docname: {}", record.getId(), record.getDocname(), e);
            onError(recordDTO, e);
        } finally {
            correctCacheService.onRecordCorrected(recordDTO); // 更新缓存
        }
    }

    /**
     * 批改之前，设置批改次数和批改模型
     */
    public static void beforeCorrect(DocCorrectRecordDTO record) {
        String aimodel = record.getTask().getAimodel();
        Integer times = null;
        if (Objects.equals(aimodel, AIModelConsts.GPT_4O_20240806_3)) {
            aimodel = AIModelConsts.GPT_4O_20240806;
            times = 3; // 三倍批改
        }
        record.setAimodel(aimodel);
        record.setCorrectTimes(times);
    }

    public void onError(DocCorrectRecordDTO record, Exception originalException) {
        DocCorrectRecord update = new DocCorrectRecord();
        update.setId(record.getId());
        update.setStatus(CorrectRecordStatus.ERROR);
        
        // 直接使用异常的错误信息
        update.setError(originalException.getMessage() == null ? "null" : originalException.getMessage().substring(0, Math.min(200, originalException.getMessage().length())));
        
        onComplete(record, update);
    }

    public void onErrorReTry(DocCorrectRecordDTO record, DocCorrectRecord update) {
        final int MAX_RETRIES = 10;
        int attempt = 0;
        while (true) {
            try {
                onComplete(record, update);
                break;  // 成功后跳出重试循环
            } catch (Exception dlEx) {
                attempt++;
                if (attempt >= MAX_RETRIES) {
                    log.error("onError 在第 {} 次重试时仍然死锁，抛出异常", attempt, dlEx);
                    throw new com.chaty.exception.RetryException(dlEx.getMessage() == null ? "null" : dlEx.getMessage().substring(0, Math.min(200, dlEx.getMessage().length())), dlEx);
                }
                log.warn("onError 抛出死锁，第 {} 次重试……", attempt);
                try {
                    // 简单退避：每次等待 100ms
                    Thread.sleep(100L * attempt);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试过程中线程被中断", ie);
                }
            }
        }
    }


    /**
     * 识别试卷姓名
     */
    public String ocrForIdentify(DocCorrectRecordDTO record) {
        JSONObject configObj = record.getConfig().getConfigObj();
        Integer studentNameRightRotation = configObj.getInt("studentNameRightRotation");
        if (Objects.isNull(studentNameRightRotation)) {
            studentNameRightRotation = 0;
        }

        JSONObject nameArea = configObj.getJSONObject("nameArea");
        if (Objects.isNull(nameArea)) {
            return null;
        }
        String name = "";
        Integer offsetX = Objects.isNull(record.getOffsetX()) ? 0 : record.getOffsetX();
        Integer offsetY = Objects.isNull(record.getOffsetY()) ? 0 : record.getOffsetY();
        String img = FileUtil.INSTANCE.docAreaImg(record.getDocParh(), nameArea.getInt("x") + offsetX, nameArea.getInt("y") + offsetY,
                nameArea.getInt("width"), nameArea.getInt("height"), studentNameRightRotation);
        String ocrText = null;
        String imgUrl = String.format("%s%s/%s", serverUrl, ctxPath, img);
        try {
            ocrText = completionService.getOcrContent("", imgUrl, GetOcrContentType.IDENTIFY_OR_STUDENT_NUMBER);
        } catch (Exception e) {
            ocrText = "";
        }
        // 调用gpt进行提纯
        try {
            String aimodel = record.getTask().getAimodel();
            GptAskLogEntity gptAskLogEntity = new GptAskLogEntity();
            gptAskLogEntity.setRecordId(record.getId());
            gptAskLogEntity.setTaskId(record.getTaskId());
            gptAskLogEntity.setFileId(record.getTask().getFileId());
            gptAskLogEntity.setAimodel(aimodel);
            gptAskLogEntity.setConfigId(record.getConfigId());
            gptAskLogEntity.setRecordName(record.getDocname());
            gptAskLogEntity.setType(GptAskLogType.studentName);
            gptAskLogEntity.setImgUrl(ctxPath);
            name = completionService.extraStudentName(imgUrl, ocrText, aimodel, gptAskLogEntity, record.getModelRequestId());
        } catch (Exception e) {
            log.error("ocrForIdentify error: {}", e.getMessage());
            name = ocrText;
        }
        return name;
    }

    /**
     * 识别试卷学号
     */
    public String ocrForStudentNumber(DocCorrectRecordDTO record) {
        JSONObject configObj = record.getConfig().getConfigObj();
        Integer studentNumberRightRotation = configObj.getInt("studentNumberRightRotation");
        DocCorrectTaskDTO task = record.getTask();

        JSONObject studentNumberArea = configObj.getJSONObject("studentNumberArea");
        if (Objects.isNull(studentNumberArea)) {
            return null;
        }
        String studentNumber = "";
        Integer offsetX = Objects.isNull(record.getOffsetX()) ? 0 : record.getOffsetX();
        Integer offsetY = Objects.isNull(record.getOffsetY()) ? 0 : record.getOffsetY();
        String img = FileUtil.INSTANCE.docAreaImg(record.getDocParh(), studentNumberArea.getInt("x") + offsetX, studentNumberArea.getInt("y") + offsetY,
                studentNumberArea.getInt("width"), studentNumberArea.getInt("height"), studentNumberRightRotation);
        String ocrText = null;
        String imgUrl = String.format("%s%s/%s", serverUrl, ctxPath, img);
        try {
            ocrText = completionService.getOcrContent("", imgUrl, GetOcrContentType.IDENTIFY_OR_STUDENT_NUMBER);
        } catch (Exception e) {
            ocrText = "";
        }
        // 调用gpt进行提纯
        try {
            String aimodel = record.getTask().getAimodel();
            GptAskLogEntity gptAskLogEntity = new GptAskLogEntity();
            gptAskLogEntity.setRecordId(record.getId());
            gptAskLogEntity.setTaskId(record.getTaskId());
            gptAskLogEntity.setFileId(record.getTask().getFileId());
            gptAskLogEntity.setAimodel(aimodel);
            gptAskLogEntity.setConfigId(record.getConfigId());
            gptAskLogEntity.setRecordName(record.getDocname());
            gptAskLogEntity.setType(GptAskLogType.studentNumer);
            gptAskLogEntity.setImgUrl(ctxPath);
            studentNumber = completionService.extraStudentNumber(imgUrl, ocrText, aimodel, gptAskLogEntity, record.getModelRequestId());
        } catch (Exception e) {
            log.error("ocrForStudentNumber error: {}", e.getMessage());
            studentNumber = ocrText;
        }
        return studentNumber;
    }

    /**
     * 批改区域
     */
    public JSONArray correctAreas(DocCorrectRecordDTO record) {
        DocCorrectConfigDTO config = record.getConfig();
        JSONArray areas = config.getAreasObj();

        JSONArray reviewed = new JSONArray();
        for (int areaIdx = 0; areaIdx < areas.size(); areaIdx++) {
            JSONObject areaObj = areas.getJSONObject(areaIdx);

            GptAskLogEntity gptAskLogEntity = new GptAskLogEntity();
            gptAskLogEntity.setRecordId(record.getId());
            gptAskLogEntity.setTaskId(record.getTaskId());
            gptAskLogEntity.setFileId(record.getTask().getFileId());
            gptAskLogEntity.setAimodel(record.getAimodel());
            gptAskLogEntity.setConfigId(record.getConfigId());
            gptAskLogEntity.setAreaIdx(areaIdx);
            gptAskLogEntity.setRecordName(record.getDocname());
            JSONObject areaRes = correctArea(record, areaObj, areaIdx, gptAskLogEntity);
            reviewed.add(areaRes);
        }
        return reviewed;
    }

    /**
     * 异步批改区域，分发批改任务
     */
    public JSONArray asyncCorrectAreas(DocCorrectRecordDTO record) {
        String recordId = record.getId();
        DocCorrectConfigDTO config = record.getConfig();
        JSONArray areas = config.getAreasObj();

        List<CompletableFuture<JSONObject>> futures = new ArrayList<>();
        for (int i = 0; i < areas.size(); i++) {
            JSONObject areaObj = areas.getJSONObject(i);
            int finalIdx = i;
            GptAskLogEntity gptAskLogEntity = new GptAskLogEntity();
            gptAskLogEntity.setRecordId(record.getId());
            gptAskLogEntity.setTaskId(record.getTaskId());
            gptAskLogEntity.setFileId(record.getTask().getFileId());
            gptAskLogEntity.setAimodel(record.getAimodel());
            gptAskLogEntity.setConfigId(record.getConfigId());
            gptAskLogEntity.setAreaIdx(i);
            gptAskLogEntity.setRecordName(record.getDocname());
            CompletableFuture<JSONObject> f = CompletableFuture.supplyAsync(
                    () -> correctArea(record, areaObj, finalIdx, gptAskLogEntity),
                    correctAreaTaskExecutor
            );
            futures.add(f);
        }
        correctionTasks.put(recordId, futures);

        JSONArray result = CompletableFuture
                .allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .collect(Collectors.toCollection(JSONArray::new)))
                .whenComplete((r, ex) -> correctionTasks.remove(recordId))
                .join();

        return result;
    }

    /**
     * 在用户触发"删除"操作时调用，取消正在执行的批改
     */
    public void cancelCorrection(String recordId) {
        List<CompletableFuture<JSONObject>> futures = correctionTasks.remove(recordId);
        if (futures != null) {
            for (CompletableFuture<JSONObject> f : futures) {
                // 尝试中断任务
                f.cancel(true);
            }
        }
    }

    @Override
    public void deleteCorrect(String taskId) {
        DocCorrectTask dbDocCorrectTask = docCorrectTaskMapper.selectById(taskId);
        if (Objects.nonNull(dbDocCorrectTask) && dbDocCorrectTask.getStatus().equals(CorrectEnums.CorrectTakStatus.PROCESSING)) {
            DocCorrectRecordDTO param = new DocCorrectRecordDTO();
            param.setTaskId(taskId);
            PageDTO<DocCorrectRecord> page = new PageDTO<>();
            page.setPageNumber(-1);
            page.setPageSize(99999);
            page.setSearchCount(false);
            param.setPage(page);
            IPage<DocCorrectRecordDTO> recordDTOIPage = docCorrectRecordService.page(param);
            List<DocCorrectRecordDTO> recordDTOList = recordDTOIPage.getRecords();
            for (DocCorrectRecordDTO recordDTO : recordDTOList) {
                this.cancelCorrection(recordDTO.getId());
                docCorrectRecordService.delete(recordDTO.getId());
            }
        }
    }

    /**
     * 批改区域-分发区域
     */
    public JSONObject correctArea(DocCorrectRecordDTO record, JSONObject areaObj, Integer areaIdx, GptAskLogEntity gptAskLogEntity) {
        log.info("DocCorrectTask correctAreas, id: {}, areaIdx: {}", record.getId(), areaIdx);
        TaskTimer timer = areaTaskMetrics.createTimer(String.format("%s_%s", record.getId(), areaIdx));

        JSONObject areaRes = new JSONObject();
        areaRes.set("status", "1"); // status(1：正常，2：异常)
        areaRes.set("areaIdx", areaIdx);
        JSONArray questions = areaObj.getJSONArray("questions");

        gptAskLogEntity.setImgUrl((String) areaObj.getOrDefault("areaImg", ""));
        if (!areaObj.containsKey("areaImg")) {
            areaRes.set("reviewed", "");
            return areaRes;
        }
        try {
            boolean enabled = areaObj.getBool("enabled", true);
            if (!enabled) {
                return areaRes;
            }
            Integer areaType = areaObj.getInt("areaType", 1);
            if (areaType == 1) {
                ModelSetting modelSetting = getModelSettingByQsType((String) areaObj.getOrDefault("commonQuestionType", "通用"), record.getFileId());
                DocCorrectRecordDTO newRecord = BeanUtil.copyProperties(record, DocCorrectRecordDTO.class);
                newRecord.setModelSetting(modelSetting);
                newRecord.setAimodel(modelSetting.getModelValue());
                gptAskLogEntity.setAimodel(modelSetting.getModelValue());
                newRecord.setModelRequestId(modelSetting.getId());
                // 批改
                if (Boolean.TRUE.equals(modelSetting.getDisabled())) {
                    completionService.correctRecordAreaDisabled(areaObj, areaRes);
                } else if (Objects.isNull(newRecord.getCorrectTimes())) {
                    if (Objects.nonNull(newRecord.getModelSetting()) && Boolean.TRUE.equals(newRecord.getModelSetting().getEnableNormalQsTwoRequest())) {
                        completionService.correctRecordAreaNormalQsTwoRequest(newRecord, areaObj, areaRes, gptAskLogEntity);
                    } else {
                        gptAskLogEntity.setType(GptAskLogType.normalArea);
                        completionService.correctRecordArea(newRecord, areaObj, areaRes, gptAskLogEntity);
                    }
                    timer.step("DocCorrectArea_AICorrect");
                } else {
                    // 多次批改结果比较
                    doMultiCorrectArea(newRecord.getCorrectTimes(), newRecord, areaObj, areaRes, gptAskLogEntity);
                    timer.step("DocCorrectArea_AICorrect_3");
                }
                areaTaskMetrics.closeTimer(timer);
            } else if (areaType == 2) {
                // 答题卡区域批改
                gptAskLogEntity.setType(GptAskLogType.cardArea);
                completionService.correctAnswerCard(record, areaObj, areaRes, gptAskLogEntity);
            } else if (areaType == 3) {
                // 写作题处理
                gptAskLogEntity.setType(GptAskLogType.essayArea);
                completionService.correctWriteQsTwiceMergers(record, areaObj, areaRes, gptAskLogEntity);
            } else if (areaType == 4) {
                ModelSetting modelSetting = getModelSettingByQsType("分数识别", record.getFileId());
                DocCorrectRecordDTO newRecord = BeanUtil.copyProperties(record, DocCorrectRecordDTO.class);
                newRecord.setModelSetting(modelSetting);
                newRecord.setAimodel(modelSetting.getModelValue());
                newRecord.setModelRequestId(modelSetting.getId());
                // 老师已有分数识别,一个区域只有一个题目，不需要多次批改，题目用来确定位置和分数
                gptAskLogEntity.setType(GptAskLogType.scorePoint);
                completionService.correctScoreArea(newRecord, areaObj, areaRes, gptAskLogEntity);
            }
        } catch (Exception e) {
            log.error("DocCorrectTask correctAreas error, id: {}, areaIdx: {}", record.getId(), areaIdx, e);
            areaRes.set("status", "2");
            areaRes.set("error", e.getMessage() == null ? "null" : e.getMessage().substring(0, Math.min(200, e.getMessage().length())));
            
            // 获取当前区域类型
            Integer areaType = areaObj.getInt("areaType", 1);
            List<JSONObject> defaultReviewed;
            if (areaType == 4) {
                // 构造批改结果，添加额外字段
                defaultReviewed = questions.stream()
                        .map(qs -> {
                            JSONObject obj = new JSONObject();
                            obj.set("isCorrect", "Y");
                            obj.set("isScorePoint", 2); // 数值类型
                            obj.set("areaType", 4);      // 明确标记区域类型
                            obj.set("isTeacherScore", true);
                            return obj;
                        })
                        .collect(Collectors.toList());
            } else {
                // 其他区域类型保持原有逻辑
                defaultReviewed = questions.stream()
                        .map(qs -> new JSONObject().set("isCorrect", "Y"))
                        .collect(Collectors.toList());
            }
            areaRes.set("reviewed", defaultReviewed);
        }

        correctCacheService.onAreaCorrected(record); // 更新缓存
        return areaRes;
    }

    private ModelSetting getModelSettingByQsType(String questionType, String fileId) {
        Integer modelSettingId = null;
        RecordModelSettingEntity recordModelSettingEntity = recordModelSettingService.getByFileAndQuestionType(fileId,questionType);
        if (Objects.isNull(recordModelSettingEntity)) {
            QuestionType dbQuestionType = questionTypeService.getByName(questionType);
            modelSettingId = dbQuestionType.getDefaultModelRequestId();
        } else {
            modelSettingId = recordModelSettingEntity.getModelSettingId();
        }
        return modelSettingService.getByIdCache(modelSettingId);
    }

    /**
     * 执行多次批改，批改结果比较
     */
    private void doMultiCorrectArea(Integer correctTimes, DocCorrectRecordDTO record, JSONObject areaObj,
                                    JSONObject areaRes, GptAskLogEntity gptAskLogEntity) {
        List<JSONArray> reviewedList = new ArrayList<>();
        int errTimes = 0;
        // 进行多次批改
        for (int time = 0; time < correctTimes; time++) {
            log.info("DocCorrectTask correctAreas, id: {}, areaObj: {}, time: {}", record.getId(), areaObj, time);
            // record.setTemperature(time * 0.5f);
            record.setTopp(time * 0.5f);
            try {
                completionService.correctRecordArea(record, areaObj, areaRes, gptAskLogEntity);
            } catch (Exception e) {
                log.error("DocCorrectTask correctAreas error, id: {}, areaObj: {}, time: {}", record.getId(), areaObj,
                        time, e);
                errTimes++;
                if (errTimes >= correctTimes) {
                    // 全部失败，抛出异常
                    throw new com.chaty.exception.RetryException(e.getMessage() == null ? "null" : e.getMessage().substring(0, Math.min(200, e.getMessage().length())), e);
                }
            }
            reviewedList.add(areaRes.getJSONArray("reviewed")); // 保存批改结果
        }
        // 比较批改结果
        JSONArray questions = areaObj.getJSONArray("questions");
        JSONArray reviewedRes = new JSONArray();
        for (int qsIdx = 0; qsIdx < questions.size(); qsIdx++) {
            int trueNum = 0;
            JSONObject trueReviewed = null;
            JSONObject falseReviewed = null;
            for (JSONArray reviewed : reviewedList) {
                if (Objects.isNull(reviewed)) {
                    trueNum++;
                    continue;
                }
                JSONObject reviewedQs = reviewed.getJSONObject(qsIdx);
                if (Objects.isNull(reviewedQs)) {
                    trueNum++;
                    continue;
                }
                if ("Y".equals(reviewedQs.getStr("isCorrect"))) {
                    trueNum++;
                    trueReviewed = reviewedQs;
                } else {
                    falseReviewed = reviewedQs;
                }
            }
            JSONObject reviewedQs = null; // 这道题的最终批改结果
            if (trueNum >= correctTimes / 2f) {
                reviewedQs = trueReviewed;
            } else {
                reviewedQs = falseReviewed;
            }
            reviewedQs = reviewedQs == null ? new JSONObject() : reviewedQs;
            reviewedQs.set("trueNum", trueNum);
            reviewedRes.add(reviewedQs);
        }
        areaRes.set("reviewed", reviewedRes);
        areaRes.set("reviewList", reviewedList);
    }

    public void onComplete(DocCorrectRecordDTO record, DocCorrectRecord update) {
        docCorrectRecordMapper.updateById(update);
        int completed = docCorrectTaskMapper.tryComplete(record.getTaskId());
        if (completed > 0) {
            // 如果任务批改完成并且关联了文件，尝试更新文件的状态
            if (Objects.nonNull(record.getTask().getFileId())) {
                int fileCompleted = docCorrectFileMapper.tryComplete(record.getTask().getFileId());
                // 姓名学号匹配
                if (fileCompleted > 0) {
                    try {
                        studentNameAndStudentNumberMatch(record.getTask().getFileId());
                    } catch (Exception e) {
                        log.error("studentNameAndStudentNumberMatch error: {}", e.getMessage());
                    }
                    try {
                        startQuickFixAndGrading(record.getTask().getFileId());
                    } catch (Exception e) {
                        log.error("startQuickFixAndGrading error: {}", e.getMessage());
                    }
                    try {
                        gptAskLogService.getStatsByFileId(record.getTask().getFileId());
                    } catch (Exception e) {
                        log.error("gptAskLogService.getStatsByFileId error: {}", e.getMessage());
                    }
                }
            }
            // 保存批改结果 并发问题；难搞...
            // docCorrectResultService.saveByTask(record.getTaskId());
        }
    }

    @IgnoreTenant
    public void startQuickFixAndGrading(String fileId) throws InterruptedException {
        if (StrUtil.isBlank(fileId)) {
            return;
        }
        DocCorrectFile docCorrectFile = docCorrectFileMapper.selectById(fileId);
        if (Objects.isNull(docCorrectFile) || StrUtil.isBlank(docCorrectFile.getTemplateFileId())) {
            return;
        }

        QuickFixDTO quickFixDTO = new QuickFixDTO();
        quickFixDTO.setFixedId(fileId);
        quickFixDTO.setTemplateId(docCorrectFile.getTemplateFileId());
        quickFixDTO.setRemark("一键重批自动纠错");
        log.info("开始快速纠错:{} {}", docCorrectFile.getName(), quickFixDTO);
        QuickFix quickFix = quickFixServce.doFix(quickFixDTO);

        quickFix.setTenantId(docCorrectFile.getTenantId());
        quickFixMapper.updateById(quickFix);

        GradingCorrectDTO gradingCorrectDTO = new GradingCorrectDTO();
        gradingCorrectDTO.setTemplateId(fileId);
        gradingCorrectDTO.setRemark("一键重批自动评估");
        log.info("开始评估批改结果:{} {}", docCorrectFile.getName(), gradingCorrectDTO);
        GradingCorrect gradingCorrect = gradingCorrectService.doGrade(gradingCorrectDTO);

        gradingCorrect.setTenantId(docCorrectFile.getTenantId());
        gradingCorrectMapper.updateById(gradingCorrect);
    }

    public void studentNameAndStudentNumberMatch(String fileId) {
        DocCorrectFile docCorrectFile = docCorrectFileMapper.selectById(fileId);
        if (Objects.isNull(docCorrectFile)) {
            return;
        }
        if (StrUtil.isBlank(docCorrectFile.getClassId())) {
            return;
        }
        List<User> users = userService.selectByClassId(docCorrectFile.getClassId());
        DocCorrectTaskDTO taskParam = new DocCorrectTaskDTO();
        PageDTO<DocCorrectTask> taskPageDTO = new PageDTO<>();
        taskPageDTO.setPageNumber(-1);
        taskPageDTO.setPageSize(9999);
        taskPageDTO.setSearchCount(false);
        taskParam.setPage(taskPageDTO);
        taskParam.setOrderByName(true);
        taskParam.setFileId(fileId);
        IPage<DocCorrectTaskDTO> docCorrectTaskDTOIPage = docCorrectTaskService.page(taskParam);
        List<DocCorrectTaskDTO> data = docCorrectTaskDTOIPage.getRecords();
        if (data.isEmpty()) {
            return;
        }
        DocCorrectTaskDTO docCorrectTaskDTO = data.get(0);
        DocCorrectRecordDTO param = new DocCorrectRecordDTO();
        param.setTaskId(docCorrectTaskDTO.getId());
        PageDTO<DocCorrectRecord> page = new PageDTO<>();
        page.setPageNumber(-1);
        page.setPageSize(99999);
        page.setSearchCount(false);
        param.setPage(page);
        IPage<DocCorrectRecordDTO> recordDTOIPage = docCorrectRecordService.page(param);
        List<DocCorrectRecordDTO> docCorrectRecords = recordDTOIPage.getRecords();

        // 遍历所有匹配学号/姓名
        for (DocCorrectRecordDTO record : docCorrectRecords) {
            String studentNumber = record.getStudentNumber();
            String identify = record.getIdentify();
            if (StrUtil.isBlank(studentNumber) && StrUtil.isBlank(identify)) {
                continue;
            }
            for (User user : users) {
                if (StrUtil.isNotBlank(studentNumber) && user.getStudentId().equals(studentNumber)) {
                    docCorrectRecordMapper.update(Wrappers.lambdaUpdate(DocCorrectRecord.class)
                            .set(DocCorrectRecord::getIdentify, user.getNickname())
                            .eq(DocCorrectRecord::getId, record.getId()));
                    break;
                } else if (StrUtil.isNotBlank(identify) && user.getNickname().equals(identify)) {
                    docCorrectRecordMapper.update(Wrappers.lambdaUpdate(DocCorrectRecord.class)
                            .set(DocCorrectRecord::getStudentNumber, user.getStudentId())
                            .eq(DocCorrectRecord::getId, record.getId()));
                    break;
                }
            }
        }
    }

    public List<JSONObject> getMarkAreas(JSONObject areaObj, JSONObject area) {
        return areaObj.getJSONArray("questions").stream()
                .map(qs -> {
                    JSONObject qsPosArea = ((JSONObject) qs).getJSONObject("qsPosArea");
                    if (Objects.nonNull(qsPosArea)) {
                        qsPosArea.set("x", qsPosArea.getInt("x") - area.getInt("x"));
                        qsPosArea.set("y", qsPosArea.getInt("y") - area.getInt("y"));
                    }
                    return qsPosArea;
                })
                .filter(a -> Objects.nonNull(a))
                .collect(Collectors.toList());
    }

    @Override
    public void correctDocName(DocCorrectTaskDTO param) {
        DocCorrectTask task = docCorrectTaskMapper.selectById(param.getId());
        if (Objects.isNull(task)) {
            throw new BaseException("未查询到任务信息");
        }
        if (Objects.isNull(task.getConfigId())) {
            throw new BaseException("为查询到试卷配置");
        }
        DocCorrectTaskDTO taskDTO = BeanUtil.toBean(task, DocCorrectTaskDTO.class);
        taskDTO.setOcrType("3");
        List<DocCorrectRecord> records = docCorrectRecordService.selectByTaskId(param.getId(), null);
        DocCorrectConfig config = docCorrectConfigMapper.selectById(task.getConfigId());
        DocCorrectConfigDTO configDTO = BeanUtil.toBean(config, DocCorrectConfigDTO.class);
        records.forEach(record -> {
            DocCorrectRecordDTO recordDTO = BeanUtil.toBean(record, DocCorrectRecordDTO.class);
            recordDTO.setTask(taskDTO);
            recordDTO.setConfig(configDTO);
            recordDTO.setDocParh(recordDTO.getDocurl().substring(ctxPath.length() + 1));
            String name = ocrForIdentify(recordDTO);
            String studentNumber = ocrForStudentNumber(recordDTO);
            if (Objects.nonNull(name)) {
                docCorrectRecordMapper.update(Wrappers.lambdaUpdate(DocCorrectRecord.class)
                        .set(DocCorrectRecord::getIdentify, name)
                        .set(DocCorrectRecord::getStudentNumber, studentNumber)
                        .eq(DocCorrectRecord::getId, record.getId()));
            }
        });
    }

}
