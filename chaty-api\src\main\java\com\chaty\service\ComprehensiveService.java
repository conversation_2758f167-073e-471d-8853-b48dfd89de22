package com.chaty.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.dto.ComprehensiveParamDTO;
import com.chaty.dto.ComprehensiveResultDTO;
import com.chaty.dto.DocCorrectFileDTO;

public interface ComprehensiveService {

    IPage<ComprehensiveResultDTO> combinedPage(ComprehensiveParamDTO param);

    IPage<ComprehensiveResultDTO> pageByConfigIds(ComprehensiveParamDTO param);
}
