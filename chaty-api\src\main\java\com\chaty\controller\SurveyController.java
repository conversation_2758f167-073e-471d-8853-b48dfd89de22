package com.chaty.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.common.BaseResponse;
import com.chaty.dto.SurveyDTO;
import com.chaty.entity.Survey;
import com.chaty.service.SurveyService;

import cn.hutool.core.bean.BeanUtil;

@RestController
@RequestMapping("/api/survey")
public class SurveyController {

    @Resource
    private SurveyService surveyService;

    @PostMapping("/confirm")
    public BaseResponse<?> confirm(@RequestBody SurveyDTO params) {
        surveyService.save(BeanUtil.toBean(params, Survey.class));
        return BaseResponse.ok("提交成功");
    }

    @PostMapping("/page")
    public BaseResponse<?> findPage(@RequestBody SurveyDTO params) {
        IPage<Survey> res = surveyService.getPage(params);
        return BaseResponse.ok(res);
    }

    @GetMapping("/delete")
    public BaseResponse<?> delete(@RequestParam String id) {
        surveyService.removeById(id);
        return BaseResponse.ok("删除成功");
    }

}
