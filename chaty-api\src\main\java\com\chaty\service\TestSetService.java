package com.chaty.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.entity.TestSetEntity;

public interface TestSetService extends IService<TestSetEntity> {

    /** 分页查询（仅 DB；支持 name / questionTypes 模糊过滤） */
    IPage<TestSetEntity> page(Integer pageNumber, Integer pageSize,
                              String name, String questionTypes);

    /** 新增（仅写库，不做任何缓存同步） */
    Long add(TestSetEntity param);

    /** 更新（仅写库，不做任何缓存同步） */
    Long updateOne(TestSetEntity param);

    /** 删除（仅删库，不做任何缓存同步） */
    void deleteById(Long id);
}
