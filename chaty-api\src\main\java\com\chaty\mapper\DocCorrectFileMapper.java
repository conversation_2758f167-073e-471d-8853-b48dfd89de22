package com.chaty.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chaty.entity.DocCorrectFile;

@Mapper
public interface DocCorrectFileMapper extends BaseMapper<DocCorrectFile> {

    @Update("UPDATE doc_correct_file " +
            "SET status = 3, " +
            "    finish_correct_time = NOW() " +
            "WHERE deleted = 0 " +
            "  AND id = #{id} " +
            "  AND status <> 3 " +
            "  AND ( " +
            "        SELECT COUNT(1) " +
            "        FROM doc_correct_task " +
            "        WHERE deleted = 0 " +
            "          AND file_id = #{id} " +
            "          AND status = 2" +
            "      ) = 0")
    int tryComplete(@Param("id") String id);


    @Update("UPDATE doc_correct_file SET status = #{file.status} WHERE deleted = 0 AND id = #{file.id}")
    int updateStatusById(@Param("file") DocCorrectFile file);
}
