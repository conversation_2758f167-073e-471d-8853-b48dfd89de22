package com.chaty.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.chaty.enums.TestSetResultStatus;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;

import java.util.Date;

/** 测试集图片批改结果（含模型参数 & 图片信息快照） */
@Data
@TableName(value = "test_set_image_result", autoResultMap = true)
public class TestSetImageResultEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long testSetId;     // 冗余
    private Long imageId;       // 来源图片记录ID

    @TableField(typeHandler = JacksonTypeHandler.class)
    private JsonNode finalResult;     // 最终批改结果 JSON

    private Integer correctCount;
    private Integer totalCount;
    private Boolean success;
    private TestSetResultStatus status;  // PROCESSING / DONE

    private Long firstRoundLogId;
    private Long secondRoundLogId;
    private Long singleRoundLogId;

    // ===== 图片信息快照 =====
    private String imgUrl;
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JsonNode rightAnswer;
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JsonNode questionDetail;
    private String questionType;
    private Boolean useQuestionDetail;
    private String fromRecordId;
    private Integer fromRecordAreaIdx;

    // ===== 模型参数快照 =====
    private Integer modelSettingId;
    private String msName;
    private String msModelValue;
    private Boolean msJsonobject;
    private Boolean msJsonschema;
    private Boolean msEnableNormalQsTwoRequest;
    private String msContent;
    private String msRemark;
    private Integer msWeight;
    private String msPrompt;
    private Boolean msIsSecondRoundUseImage;
    private Boolean msIsSecondRoundJsonComparison;
    private Boolean msEnableImageEnhancement;
    private String msFirstRoundPromptType;
    private String msSecondRoundPromptType;
    private String msSingleRoundPromptType;
    private Boolean msDisabled;
    private String msQuestionType;
    private Date msCreateTime;
    private Date msUpdateTime;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
