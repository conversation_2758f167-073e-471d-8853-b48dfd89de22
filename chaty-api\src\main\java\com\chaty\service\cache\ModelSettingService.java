package com.chaty.service.cache;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.dto.ModelSettingDTO;
import com.chaty.entity.ModelSetting;

public interface ModelSettingService extends IService<ModelSetting> {

    ModelSetting addModelRequest(ModelSettingDTO modelRequestDTO);

    boolean deleteModelRequest(Integer id);

    ModelSetting updateModelRequest(ModelSettingDTO modelRequestDTO);

    IPage<ModelSettingDTO> selectPage(ModelSettingDTO param);

    int getMaxEmptyWeight();

    /** 新增：优先从 Redis 获取，miss 时回源 DB 并回填 Redis */
    ModelSetting getByIdCache(Integer id);
}
