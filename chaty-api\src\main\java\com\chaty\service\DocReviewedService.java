package com.chaty.service;

import java.util.List;
import java.util.Map;

import com.chaty.dto.DocReviewedDTO;
import com.chaty.entity.DocReviewed;

public interface DocReviewedService {

    List<DocReviewed> list(DocReviewed param);

    DocReviewed add(DocReviewed param);

    void updateById(DocReviewed param);

    void deleteById(Integer id);

    DocReviewed selectById(Integer id);

    Map<String, Object> review(DocReviewedDTO param);

    Map<String, Object> batchReviewDoc(DocReviewedDTO param);

}
