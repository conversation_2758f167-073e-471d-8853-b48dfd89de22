package com.chaty.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.chaty.dto.DocTagDTO;
import com.chaty.entity.DocTag;

@Mapper
public interface DocTagMapper extends BaseMapper<DocTag> {

    @Select("select dt.*, dtr.doc_id, dtr.task_id from doc_tag dt left join doc_tag_record dtr on dt.id = dtr.tag_id ${ew.customSqlSegment}")
    List<DocTagDTO> listByDocs(@Param(Constants.WRAPPER) Wrapper wrapper);

}
