package com.chaty.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.dto.PromptDTO;
import com.chaty.entity.Prompt;

public interface PromptService extends IService<Prompt> {

    IPage<Prompt> page(PromptDTO param);

    void add(Prompt param);

    void update(Prompt param);

    void delete(String id);

    /** 手动触发：重建 Redis 缓存与索引 */
    void rebuildCache();
}
