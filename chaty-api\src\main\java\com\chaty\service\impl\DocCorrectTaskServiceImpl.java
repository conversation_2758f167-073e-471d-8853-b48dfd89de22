package com.chaty.service.impl;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.chaty.dto.*;
import com.chaty.entity.*;
import com.chaty.enums.CorrectEnums;
import com.chaty.mapper.DocCorrectFileMapper;
import com.chaty.service.*;
import com.chaty.service.cache.RemarkCommentService;
import freemarker.ext.beans.HashAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chaty.enums.CorrectEnums.CorrectEventType;
import com.chaty.enums.CorrectEnums.CorrectRecordStatus;
import com.chaty.enums.CorrectEnums.CorrectTakStatus;
import com.chaty.enums.UserRoleConsts;
import com.chaty.exception.BaseException;
import com.chaty.mapper.DocCorrectConfigMapper;
import com.chaty.mapper.DocCorrectRecordMapper;
import com.chaty.mapper.DocCorrectTaskMapper;
import com.chaty.security.AuthUtil;
import com.chaty.service.PDFService.TexCmd;
import com.chaty.task.correct.CorrectEvent;
import com.chaty.util.FileUtil;
import com.chaty.mapper.FtpFilesMapper;
import com.chaty.mapper.FtpMessageMapper;
import com.chaty.entity.FtpFiles;
import com.chaty.entity.FtpMessage;
import com.chaty.entity.DocCorrectFile;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import static com.chaty.util.MyStringUtils.replaceBracketContent;
import static com.chaty.util.PDFUtil.getPDFInfo;
import static com.chaty.util.ScoreUtils.mergeScoreTypesFromDTO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class DocCorrectTaskServiceImpl implements DocCorrectTaskService {

    @Resource
    private DocCorrectTaskMapper docCorrectTaskMapper;
    @Resource
    private ApplicationContext ApplicationContext;
    @Resource
    private DocCorrectRecordService docCorrectRecordService;
    @Resource
    private DocCorrectRecordMapper docCorrectRecordMapper;
    @Resource
    private DocCorrectConfigMapper docCorrectConfigMapper;
    @Resource
    private PDFService pdfService;
    @Resource
    private DocCorrectConfigService docCorrectConfigService;
    @Resource
    private RemoteFileService remoteFileService;
    @Resource
    private PrinterService printerService;
    @Resource
    private DocCorrectFileMapper docCorrectFileMapper;
    @Resource
    private FtpFilesMapper ftpFilesMapper;
    @Resource
    private FtpMessageMapper ftpMessageMapper;
    @Value("${file.local.path}")
    private String filePath;
    @Resource
    private RemarkCommentService remarkCommentService;

    @Override
    public void add(DocCorrectTaskDTO param) {
        param.setStatus(CorrectTakStatus.WAIT);
        docCorrectTaskMapper.insert(param);
    }

    @Override
    public void delete(String id) {
        docCorrectTaskMapper.deleteById(id);
    }

    @Override
    public IPage<DocCorrectTaskDTO> page(DocCorrectTaskDTO param) {
        User user = AuthUtil.getLoginUser();
        QueryWrapper<DocCorrectTask> queryWrapper = Wrappers.<DocCorrectTask>query()
                .like(StrUtil.isNotBlank(param.getName()), "dct.name", param.getName())
                .eq(Objects.nonNull(param.getStatus()), "dct.status", param.getStatus())
                .eq(StrUtil.isNotBlank(param.getFileId()), "dct.file_id", param.getFileId())
//                .eq(Objects.nonNull(user), "dct.creator", Optional.ofNullable(user).map(User::getId).orElse(null))
                .eq("dct.deleted", false)
                .eq(Objects.nonNull(param.getSimilarPaperId()), "dct.similar_paper_id", param.getSimilarPaperId())
                .orderByAsc(param.getOrderByName(), "dct.name")
                .orderByAsc(!param.getOrderByName(), "dct.status")
                .eq(Objects.nonNull(param.getIsEssay()), "dct.is_essay", param.getIsEssay())
                .orderByDesc(!param.getOrderByName(), "dct.update_time");
        IPage<DocCorrectTaskDTO> page = docCorrectTaskMapper.page(param.getPage().page(), queryWrapper);
        if (param.isCountRecord()) {
            List<String> taskIds = page.getRecords().stream().map(DocCorrectTask::getId).collect(Collectors.toList());
            if (!taskIds.isEmpty()) {
                QueryWrapper<DocCorrectRecord> wrapper = Wrappers.<DocCorrectRecord>query()
                        .in("task_id", taskIds)
                        .groupBy("task_id");

                // 获取每个 taskId 的计数
                Map<String, Map<String, Object>> statusCount = docCorrectRecordMapper.countByStatus(wrapper).stream()
                        .collect(Collectors.toMap(item -> item.get("taskId").toString(), item -> item));

                // 将计数结果映射到每个记录中
                page.getRecords().forEach(task -> {
                    Map<String, Object> count = statusCount.get(task.getId());
                    if (Objects.nonNull(count)) {
                        Optional.ofNullable(count.get("recordCount"))
                                .ifPresent(c -> task.setRecordCount(Convert.convert(Integer.class, c)));
                        Optional.ofNullable(count.get("finishedCount"))
                                .ifPresent(c -> task.setFinishedCount(Convert.convert(Integer.class, c)));
                    }
                });
            } else {
                // taskIds 为空时，设置所有记录的计数为零
                page.getRecords().forEach(task -> {
                    task.setRecordCount(0);
                    task.setFinishedCount(0);
                });
            }
        }
        return page;
    }

    @Override
    public void update(DocCorrectTaskDTO param) {
        docCorrectTaskMapper.updateById(param);
    }

    @Transactional
    @Override
    public void execute(DocCorrectTaskDTO param) {
        updateTask2Waiting(param);
        // 发送批改事件
        ApplicationContext.publishEvent(CorrectEvent.type(CorrectEventType.CORRECT));
    }

    private void updateTask2Waiting(DocCorrectTaskDTO param) {
        String id = param.getId();
        // 查询任务
        DocCorrectTask task = docCorrectTaskMapper.selectById(id);
        Objects.requireNonNull(task, "未查询到任务信息");
        if (StrUtil.isBlank(task.getConfigId())) {
            throw new BaseException("未查询到批改配置信息");
        }
        // 查询试卷
        List<DocCorrectRecord> records = docCorrectRecordService.selectByTaskId(id, param.getRecordIds());
        if (records.isEmpty()) {
            throw new BaseException("请添加批改试卷");
        }

        DocCorrectTask update = new DocCorrectTask();
        update.setId(id);
        update.setStatus(CorrectEnums.CorrectTakStatus.PROCESSING);
        update.setCorrectConfig(param.getCorrectConfigStr());
        docCorrectTaskMapper.updateById(update);

        // 更新批改试卷状态
        docCorrectRecordService.setRecordWait(param.getRecordIds(), id);

        // 更新关联的file状态
        DocCorrectFile fileUpdate = new DocCorrectFile();
        fileUpdate.setId(task.getFileId());
        fileUpdate.setStatus(CorrectTakStatus.PROCESSING);
        fileUpdate.setLastCorrectTime(new Date());
        docCorrectFileMapper.updateById(fileUpdate);
    }

    @Transactional
    @Override
    public void executeBatch(List<DocCorrectTaskDTO> param) {
        for (DocCorrectTaskDTO task : param) {
            updateTask2Waiting(task);
        }
        ApplicationContext.publishEvent(CorrectEvent.type(CorrectEventType.CORRECT));
    }

    @Transactional
    @Override
    public void reIdentifyNameOrStudentNumberType(DocCorrectTaskDTO param) {
        String id = param.getId();

        // 查询任务
        DocCorrectTask task = docCorrectTaskMapper.selectById(id);
        Objects.requireNonNull(task, "未查询到任务信息");
        if (StrUtil.isBlank(task.getConfigId())) {
            throw new BaseException("未查询到批改配置信息");
        }
        // 查询试卷
        List<DocCorrectRecord> records = docCorrectRecordService.selectByTaskId(id, param.getRecordIds());
        if (records.isEmpty()) {
            throw new BaseException("请添加批改试卷");
        }

        DocCorrectTask update = new DocCorrectTask();
        update.setId(id);
        update.setStatus(CorrectEnums.CorrectTakStatus.PROCESSING);
        update.setCorrectConfig(param.getCorrectConfigStr());
        docCorrectTaskMapper.updateById(update);

        // 更新批改试卷状态
        docCorrectRecordService.setRecordWait(param.getRecordIds(), id);

        // 更新关联的file状态
        DocCorrectFile fileUpdate = new DocCorrectFile();
        fileUpdate.setId(task.getFileId());
        fileUpdate.setStatus(CorrectTakStatus.PROCESSING);
        fileUpdate.setLastCorrectTime(new Date());
        docCorrectFileMapper.updateById(fileUpdate);
        // 发送批改事件
        ApplicationContext.publishEvent(CorrectEvent.type(param.getReIdentifyNameOrStudentNumberType()));
    }

    @Transactional
    @Override
    public void addByFile(DocCorrectTaskDTO param) {
        String filename = param.getFilename();
        List<List<JSONObject>> docList = param.getDocList();
        if (Objects.isNull(docList) || docList.isEmpty()) {
            throw new BaseException("未获取到试卷");
        }
        Boolean isCreateConfig = Optional.ofNullable(param.getIsCreateConfig()).orElse(false);

        for (int i = 0; i < docList.size(); i++) {
            List<JSONObject> docs = docList.get(i);
            if (Objects.isNull(docs) || docs.isEmpty()) {
                continue;
            }
            // 创建批改配置
            String configId = null;
            if (isCreateConfig) {
                JSONObject doc = docs.get(0);
                DocCorrectConfig config = new DocCorrectConfig();
                config.setName(String.format("%s(页%s)_%s_配置", filename, i + 1,
                        LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN)));
                config.setDocurl(doc.getStr("url"));
                config.setImg(FileUtil.INSTANCE.docUrl2ImgUrl(config.getDocurl()));
                config.setDocType("A4");
                config.setConfig("{\"score\":true,\"scoreFormat\":1,\"scoreColor\":\"red\",\"scoreFontSize\":10,\"fontSize\":10,\"flagSize\":20,\"flagColor\":\"red\",\"errorFlagColor\":\"red\",\"errorFlagSize\":20,\"errorFlag\":\"x\",\"correctFlag\":\"a\",\"nameArea\":null,\"scoreArea\":null,\"additionalName\":\"附加\",\"prompt\":null,\"qsOcrPrompt\":null,\"scoreTypes\":[\"总分\"]}");
                config.setAreas("[{\"areaType\":1,\"area\":null,\"enabled\":true,\"opinion\":1,\"questions\":[{\"name\":\"问题1\",\"question\":\"\",\"qsInfo\":\"\",\"answer\":\"\",\"score\":1,\"isAdditional\":1,\"flagArea\":{\"x\":0,\"y\":0,\"width\":50,\"height\":50,\"rotate\":0,\"scaleX\":1,\"scaleY\":1},\"reviewType\":2,\"defaultReview\":\"\",\"scoreType\":\"总分\",\"opinion\":2,\"isScorePoint\":1,\"scorePoints\":\"\"}]}]");
                docCorrectConfigMapper.insert(config);
                configId = config.getId();
            }
            DocCorrectTask task = new DocCorrectTask();
            if (StrUtil.isNotBlank(param.getName())) {
                task.setName(param.getName());
            } else {
                String taskName = String.format("%s(页%s)", filename, i + 1);
                task.setName(taskName);
            }
            task.setConfigId(configId);
            task.setStatus(CorrectTakStatus.WAIT);
            if (param.getSimilarPaperId() != null) {
                task.setSimilarPaperId(param.getSimilarPaperId());
            }
            docCorrectTaskMapper.insert(task);

            for (int j = 0; j < docs.size(); j++) {
                JSONObject doc = docs.get(j);
                DocCorrectRecord record = new DocCorrectRecord();
                record.setTaskId(task.getId());
                record.setConfigId(configId);
                record.setDocurl(doc.getStr("url"));
                record.setDocname(String.format("%s_%03d", task.getName(), j + 1));
                record.setStatus(CorrectRecordStatus.UNCORRECT);
                docCorrectRecordMapper.insert(record);
            }
        }
    }

    @Override
    public Map<String, Object> createReviewedDoc(DocCorrectTaskDTO param) {
        // 前端传过来的的idx要-1
        if (Objects.isNull(param.getNeedRotationList())) {
            param.setNeedRotationList(new ArrayList<>());
        } else {
            List<RotationItemDTO> needRotationList = param.getNeedRotationList();
            for (RotationItemDTO item : needRotationList) {
                item.setOrientation(item.getOrientation() - 1);
                item.setPageNumber(item.getPageNumber() - 1);
            }
            param.setNeedRotationList(needRotationList);
        }
        if (Objects.isNull(param.getSwapPagesList())) {
            param.setSwapPagesList(new ArrayList<>());
        } else {
            List<SwapPagesItemDTO> swapPagesList = param.getSwapPagesList();
            for (SwapPagesItemDTO item : swapPagesList) {
                item.setFromOrientation(item.getFromOrientation() - 1);
                item.setToOrientation(item.getToOrientation() - 1);
                item.setFromPage(item.getFromPage() - 1);
                item.setToPage(item.getToPage() - 1);
            }
            param.setSwapPagesList(swapPagesList);
        }

        List<String> taskIds = CollUtil.newArrayList(param.getTaskIds());
        if (Objects.nonNull(param.getFileId())) {
            // 根据文件查询
            taskIds = docCorrectTaskMapper.selectList(Wrappers.lambdaQuery(DocCorrectTask.class)
                            .eq(DocCorrectTask::getFileId, param.getFileId())
                            .eq(DocCorrectTask::getDeleted, false)
                            .orderByAsc(DocCorrectTask::getName))
                    .stream().map(DocCorrectTask::getId).collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(param.getNeedRotationList())) {
            for (RotationItemDTO item : param.getNeedRotationList()) {
                item.setTaskId(taskIds.get(item.getOrientation()));
            }
        }
        if (param.getIsReversed()) {
            taskIds = CollUtil.reverseNew(taskIds);
        }

        String taskName = "";
        int pageNum = 0;

        List<List<DocCorrectRecordDTO>> recordList = new ArrayList<>();
        JSONArray scoreTypes = new JSONArray();
        List<DocCorrectConfigDTO> correctConfigDTOS = new ArrayList<>();
        List<DocCorrectTaskDTO> docCorrectTaskDTOS = new ArrayList<>();
        for (String taskId : taskIds) {
            DocCorrectTaskDTO task = Optional.ofNullable(docCorrectTaskMapper.selectById(taskId))
                    .map(t -> BeanUtil.copyProperties(t, DocCorrectTaskDTO.class))
                    .orElseThrow(() -> new RuntimeException("未查询到批改任务信息"));
            docCorrectTaskDTOS.add(task);
            DocCorrectConfigDTO config = Optional.ofNullable(docCorrectConfigMapper.selectById(task.getConfigId()))
                    .map(c -> BeanUtil.copyProperties(c, DocCorrectConfigDTO.class))
                    .orElseThrow(() -> new RuntimeException("未查询到试卷配置信息"));
            correctConfigDTOS.add(config);
        }
        String docType = correctConfigDTOS.get(0).getDocType();
        if (StrUtil.isBlank(docType)) {
            docType = "8k";
        }

        if (CollUtil.isNotEmpty(docCorrectTaskDTOS)) {
            updateExportCompletedStatus(docCorrectTaskDTOS.get(0));
        }

        if (param.getIsReversed()) {
            correctConfigDTOS = CollUtil.reverseNew(correctConfigDTOS);
            scoreTypes = mergeScoreTypesFromDTO(correctConfigDTOS).getJSONArray("scoreTypes");
            correctConfigDTOS = CollUtil.reverseNew(correctConfigDTOS);
        } else {
            scoreTypes = mergeScoreTypesFromDTO(correctConfigDTOS).getJSONArray("scoreTypes");
        }

        List<DocCorrectRecordDTO> backUpRecords = new ArrayList<>();


        for (String taskId : taskIds) {
            DocCorrectTaskDTO task = docCorrectTaskDTOS.get(pageNum);
            if (pageNum == 0) {
                taskName = task.getName();
            }
            DocCorrectConfigDTO config = correctConfigDTOS.get(pageNum);

            List<DocCorrectRecordDTO> records = docCorrectRecordService.listByIdOrTaskId(null, taskId);

            records.forEach(r -> {
                r.setTask(task);
                r.setConfig(config);
                r.setRanges(param.getRanges());
                r.setEnableLevelOutput(param.getEnableLevelOutput());
                r.setIsRotate(param.getIsRotate());
                // 自动生成所有需要旋转的,根据record的needRation 添加到. 不含原卷需要旋转
                if (r.getNeedRation() && !param.getIsPreview()) {
                    r.setIsRotate(!r.getIsRotate());
                }
            });

            // 处理旋转
            if (CollUtil.isNotEmpty(param.getNeedRotationList())) {
                for (RotationItemDTO item : param.getNeedRotationList()) {
                    if (taskId.equals(item.getTaskId())) {
                        DocCorrectRecordDTO record = records.get(item.getPageNumber());
                        record.setIsRotate(!record.getIsRotate());
                        records.set(item.getPageNumber(), record);
                    }
                }
            }
            recordList.add(records);
            backUpRecords.addAll(records);
            pageNum++;
        }

        // 计算&设置总分情况，在每个学生的第一页
        int pageIdx = 0;
        int firstPageIdx = param.getIsReversed() ? recordList.size() - 1 : 0;
        for (DocCorrectRecordDTO recordDTO : recordList.get(firstPageIdx)) {
            recordDTO.setIsFirstPage(true);
            BigDecimal totalScore = BigDecimal.ZERO;
            BigDecimal score = BigDecimal.ZERO;
            BigDecimal additionalScore = BigDecimal.ZERO;
            BigDecimal additionalScored = BigDecimal.ZERO;
            String additionalName = recordDTO.getConfig().getConfigObj().getStr("additionalName", "附加");
            Integer scoreFormat = recordDTO.getConfig().getConfigObj().getInt("scoreFormat", 1);
            Map<String, Object> scoreTypesMap = new HashMap<>();
            for (int i = 0; i < recordList.size(); i++) {
                if (pageIdx >= recordList.get(i).size()) {
                    continue;
                }
                DocCorrectRecordDTO record = recordList.get(i).get(pageIdx);
                JSONObject scoreObj = record.getScore();
                totalScore = totalScore.add(scoreObj.getBigDecimal("totalScore", BigDecimal.ZERO));
                score = score.add(scoreObj.getBigDecimal("scored", BigDecimal.ZERO));
                additionalScore = additionalScore.add(scoreObj.getBigDecimal("additionalScore", BigDecimal.ZERO));
                additionalScored = additionalScored.add(scoreObj.getBigDecimal("additionalScored", BigDecimal.ZERO));
                if (i == 0) {
                    scoreTypesMap = scoreObj.getJSONObject("scoreTypeMap");
                } else {
                    scoreTypesMap = recordDTO.mergeScoreType(scoreTypesMap, scoreObj.getJSONObject("scoreTypeMap"));
                }
            }
            String firstPageScore = recordDTO.scoreTxt(totalScore, score, scoreTypesMap, scoreFormat, scoreTypes.toList(String.class));
            recordDTO.setFirstPageScore(firstPageScore);

            int correctRate = 100;
            if (totalScore.compareTo(BigDecimal.ZERO) > 0) {
                correctRate = score.divide(totalScore, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).intValue();
            }
            String scoreColor = recordDTO.getConfig().getConfigObj().getStr("scoreColor", "red");
            if ("byScore".equals(scoreColor)) {
                scoreColor = correctRate > 60 ? "green" : "red";
            }
            recordDTO.setScoreColor(scoreColor);
            pageIdx++;
        }

        // 处理交换的情况，不含原卷需要交换回去
        if (!param.getIsPreview()) {
            for (List<DocCorrectRecordDTO> recordDTOS : recordList) {
                for (DocCorrectRecordDTO item : recordDTOS) {
                    if (StrUtil.isNotBlank(item.getSwapTargetRecordId()) && !item.getSwapTargetRecordId().equals(item.getId())) {
                        recordDTOS.set(recordDTOS.indexOf(item), backUpRecords.stream().filter(r -> r.getId().equals(item.getSwapTargetRecordId())).findFirst().orElse(item));
                    }
                }
            }
        }


        List<DocCorrectRecordDTO> records = new ArrayList<>();
        int recordSize = recordList.get(0).size();
        // 合并不同分数类型的分数
        DocCorrectRecordDTO docCorrectRecordDTO = new DocCorrectRecordDTO();
        docCorrectRecordDTO.setRanges(param.getRanges());
        docCorrectRecordDTO.setEnableLevelOutput(param.getEnableLevelOutput());
        for (int i = 0; i < recordSize; i++) {
            JSONObject score = new JSONObject();
            JSONObject totalScoreArea = null;
            for (List<DocCorrectRecordDTO> list : recordList) {
                if (i >= list.size()) {
                    continue;
                }
                JSONObject scoreItem = list.get(i).getScore();
                score = docCorrectRecordDTO.mergeScore(score, scoreItem);
                if (Objects.isNull(totalScoreArea)) {
                    totalScoreArea = Optional.ofNullable(list.get(i).getConfig().getConfigObj().getJSONObject("totalScoreArea")).orElse(null);
                }
            }
            String levelRangeStr = docCorrectRecordDTO.getLevelRange(score, totalScoreArea, scoreTypes);
            if (Objects.isNull(totalScoreArea)) {
                totalScoreArea = new JSONObject();
                totalScoreArea.set("x", 100);
                totalScoreArea.set("y", 100);
            }
            // 全部合并完成,将每个学生的所有页数试卷的分值合并
            for (List<DocCorrectRecordDTO> list : recordList) {
                if (i >= list.size()) {
                    continue;
                }
                list.get(i).setPapersScore(score);
                if (Objects.nonNull(param.getEnableLevelOutput()) && param.getEnableLevelOutput()) {
                    list.get(i).setLevelRangeTxt(levelRangeStr);
                } else {
                    list.get(i).setLevelRangeTxt("");
                }
                list.get(i).setTotalScoreAreaX(totalScoreArea.getStr("x"));
                list.get(i).setTotalScoreAreaY(totalScoreArea.getStr("y"));
                list.get(i).setTotalScoreArea(totalScoreArea);
            }
        }
        Integer scorePageNo = 1;
        // 试卷排序打印 含原卷4321；不含原卷4页特殊情况：2143 之前是1234 翻转为4321
        if (!param.getIsPreview()) {
            List<Integer> orderIndices = new ArrayList<>();
            int pageCount = recordList.size();
            int pairCount = pageCount / 2;
            // 处理成对部分：例如 recordSize==4 时，i=1生成(1,0)，i=2生成(3,2)
            for (int i = 1; i <= pairCount; i++) {
                orderIndices.add(2 * i - 1); // 对应第 2,4,6,... 页（0-indexed 为 1,3,5,...）
                orderIndices.add(2 * i - 2); // 对应第 1,3,5,... 页（0-indexed 为 0,2,4,...）
            }
            // 如果页数为奇数，则追加最后一页（例如 5 页时，追加第5页，下标为4）
            if (pageCount % 2 == 1) {
                orderIndices.add(pageCount - 1);
            }
            // 翻转之后是3412
            Collections.reverse(orderIndices);
            // 根据计算的顺序依次从每份记录中取出相应的试卷记录
            scorePageNo = pageCount > 1 ? 2 : 1;
            for (int studentIndex = 0; studentIndex < recordSize; studentIndex++) {
                for (Integer index : orderIndices) {
                    records.add(recordList.get(index).get(studentIndex));
                }
            }
        } else {
            scorePageNo = 1;
            for (int i = 0; i < recordSize; i++) {
                for (List<DocCorrectRecordDTO> list : recordList) {
                    if (i >= list.size()) {
                        continue;
                    }
                    records.add(list.get(i));
                }
            }
        }
        String fullPath = records.get(0).getDocurl();
        String pdfFilePath = filePath + File.separator + fullPath.substring(fullPath.lastIndexOf("/") + 1);
        JSONObject docInfo = getPDFInfo(pdfFilePath);
        // 原卷根据 docType来，不含原卷根据 上传的pdf来
        if (param.getIsPreview()) {
            // 8k,a4,a3
            int width = 260, height = 370;
            if ("a4".equals(docType)) {
                width = 210;
                height = 297;
            } else if ("a3".equals(docType)) {
                width = 297;
                height = 420;
            } else {
                width = 260;
                height = 370;
            }

            float originWidth = (float) docInfo.getOrDefault("width", 260f), originHeight = (float) docInfo.getOrDefault("height", 370f);
            if (originWidth > originHeight) {
                docInfo.set("width", height);
                docInfo.set("height", width);
            } else {
                docInfo.set("width", width);
                docInfo.set("height", height);
            }
        }

        // 分发评语
        for (DocCorrectRecordDTO record : records) {
            if (record.getConfig() != null && record.getConfig().getConfigObj() != null && record.getConfig().getConfigObj().getJSONObject("remarkArea") != null) {
                if (Boolean.TRUE.equals(record.getConfig().getConfigObj().getBool("isRandomRemark"))) {
                    record.setRemarkContent(remarkCommentService.randomOne().getContent());
                } else if(StrUtil.isNotBlank(record.getConfig().getConfigObj().getStr("remarkContent"))) {
                    record.setRemarkContent(record.getConfig().getConfigObj().getStr("remarkContent"));
                } else {
                    record.setRemarkContent(remarkCommentService.randomOne().getContent());
                }
            }
        }


        // 生成批改结果
        Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>())
                .put("records", records)
                .put("pageNum", taskIds.size())
                .put("isPreview", param.getIsPreview())
                .put("mergeScore", param.getScoreMerge())
                .put("showQsScore", param.getShowQsScore())
                .put("isReversed", param.getIsReversed())
                .put("isRotate", param.getIsRotate())
                .put("allScoreTypes", scoreTypes.toList(String.class))
                .put("enableLevelOutput", Optional.ofNullable(param.getEnableLevelOutput()).orElse(false))
                .put("scorePageNo", scorePageNo)
                .put("needRotationList", param.getNeedRotationList())
                .put("docInfo", docInfo)
                .put("onlyShowWrongQsScore", param.getOnlyShowWrongQsScore())
                .build();
        Map<String, Object> res = pdfService.createDoc(TexCmd.PDFLATEX, "doccorrect", params);
        // 保存到远程文件夹
        String fileName = String.format("%s 批改结果(%s).pdf", taskName, param.getIsPreview() ? "含原卷" : "不含原卷");
        if (Objects.nonNull(param.getRemoteFileProps()) && StrUtil.isNotBlank(param.getRemoteFileProps().getSaveFileName())) {
            fileName = param.getRemoteFileProps().getSaveFileName();
        }
        save2Remote(fileName, res, param.getRemoteFileProps());
        setExportFolderPath(param, docCorrectTaskDTOS.get(0));
        // 远程打印
        doPrint(res, param.getPrinterProps());
        return res;
    }

    private void setExportFolderPath(DocCorrectTaskDTO param, DocCorrectTaskDTO task) {
        if (Objects.nonNull(param.getRemoteFileProps()) && StrUtil.isNotBlank(task.getFileId()) && StrUtil.isNotBlank(param.getRemoteFileProps().getPath())) {
            DocCorrectFile docCorrectFile = new DocCorrectFile();
            docCorrectFile.setId(task.getFileId());
            docCorrectFile.setExportFolderPath(param.getRemoteFileProps().getPath());
            docCorrectFileMapper.updateById(docCorrectFile);
        }
    }


    @Override
    public List<DocCorrectTaskDTO> statsByStatus() {
        return docCorrectTaskMapper.countByStatus(AuthUtil.getLoginUser().getId());
    }

    @Override
    public DocCorrectTaskDTO getById(String id) {
        return BeanUtil.copyProperties(docCorrectTaskMapper.selectById(id), DocCorrectTaskDTO.class);
    }

    @Override
    public Map<String, Object> createStatsDoc(DocCorrectTaskDTO param) {
        List<String> taskIds = CollUtil.newArrayList(param.getTaskIds());
        if (Objects.nonNull(param.getFileId())) {
            // 根据文件查询
            taskIds = docCorrectTaskMapper.selectList(Wrappers.lambdaQuery(DocCorrectTask.class)
                            .eq(DocCorrectTask::getFileId, param.getFileId())
                            .eq(DocCorrectTask::getDeleted, false)
                            .orderByAsc(DocCorrectTask::getName))
                    .stream().map(DocCorrectTask::getId).collect(Collectors.toList());
        }

        int totalSize = 0;
        List<DocCorrectConfigDTO> configs = new ArrayList<>();
        List<JSONArray> areaList = new ArrayList<>();
        BigDecimal totalScore = BigDecimal.ZERO;
        BigDecimal scored = BigDecimal.ZERO;
        BigDecimal addTotalScore = BigDecimal.ZERO;
        BigDecimal addScored = BigDecimal.ZERO;
        boolean isFirstTask = true;
        String taskName = "";
        List<BigDecimal> scores = new ArrayList<>();
        List<DocCorrectTaskDTO> tasks = new ArrayList<>();
        Boolean scorePointTypeShowAverageScore = Optional.ofNullable(param.getScorePointTypeShowAverageScore()).orElse(false);
        for (String taskId : taskIds) {
            DocCorrectTaskDTO task = Optional.ofNullable(docCorrectTaskMapper.selectById(taskId))
                    .map(t -> BeanUtil.copyProperties(t, DocCorrectTaskDTO.class))
                    .orElseThrow(() -> new RuntimeException("未查询到批改任务信息"));
            DocCorrectConfigDTO config = Optional.ofNullable(docCorrectConfigMapper.selectById(task.getConfigId()))
                    .map(c -> BeanUtil.copyProperties(c, DocCorrectConfigDTO.class))
                    .orElseThrow(() -> new RuntimeException("未查询到试卷配置信息"));
            List<DocCorrectRecordDTO> records = docCorrectRecordService.listByIdOrTaskId(null, taskId);
            tasks.add(task);
            if (isFirstTask) {
                isFirstTask = false;
                totalSize = records.size();
                taskName = task.getName();
            }

            JSONArray areas = config.getAreasObj();
            JSONObject configObj = config.getConfigObj();
            boolean isScore = configObj.getBool("score", false);
            boolean isFirst = true;
            for (int recordIdx = 0; recordIdx < records.size(); recordIdx++) {
                DocCorrectRecordDTO record = records.get(recordIdx);
                JSONArray reviewed = record.getReviewedObj();
                BigDecimal recordScore = BigDecimal.ZERO;
                for (int areaIdx = 0; areaIdx < areas.size(); areaIdx++) {
                    JSONObject area = areas.getJSONObject(areaIdx);
                    JSONArray qss = area.getJSONArray("questions");
                    for (int qsIdx = 0; qsIdx < qss.size(); qsIdx++) {
                        JSONObject qs = qss.getJSONObject(qsIdx);
                        int additional = qs.getInt("isAdditional", 1);
                        BigDecimal score = qs.getBigDecimal("score");
                        String isCorrect = JSONUtil.getByPath(reviewed,
                                String.format("[%s].reviewed[%s].isCorrect", areaIdx, qsIdx), "Y");
                        boolean isTrue = Objects.equals(isCorrect, "Y");
                        if (qs.getInt("isScorePoint", 1) == 2) {
                            // 得分点不计入正确率
                            if (scorePointTypeShowAverageScore) {
                                BigDecimal correctCount = qs.getBigDecimal("correctCount", BigDecimal.ZERO);
                                BigDecimal qsScore = JSONUtil.getByPath(reviewed, String.format(
                                        "[%s].reviewed[%s].scored", areaIdx, qsIdx), BigDecimal.ZERO);
                                qs.set("correctCount", correctCount.add(qsScore));
                            } else {
                                // 全对才算正确
                                int correctCount = qs.getInt("correctCount", 0);
                                BigDecimal qsScore = JSONUtil.getByPath(reviewed, String.format(
                                        "[%s].reviewed[%s].scored", areaIdx, qsIdx), BigDecimal.ZERO);
                                boolean isFullScore = qsScore.compareTo(qs.getBigDecimal("score", BigDecimal.ZERO)) == 0;
                                qs.set("correctCount", isFullScore ? correctCount + 1 : correctCount);
                            }

                        } else {
                            int correctCount = qs.getInt("correctCount", 0);
                            qs.set("correctCount", isTrue ? correctCount + 1 : correctCount);
                        }
                        if (isScore) {
                            if (additional == 1) {
                                if (isFirst) {
                                    totalScore = totalScore.add(score);
                                }
                                if (qs.getInt("isScorePoint", 1) == 2) {
                                    // 得分点加分
                                    BigDecimal qsScore = JSONUtil.getByPath(reviewed, String.format(
                                            "[%s].reviewed[%s].scored", areaIdx, qsIdx), BigDecimal.ZERO);
                                    scored = scored.add(qsScore);
                                    recordScore = recordScore.add(qsScore);
                                } else {
                                    scored = scored.add(isTrue ? score : BigDecimal.ZERO);
                                    recordScore = recordScore.add(isTrue ? score : BigDecimal.ZERO);
                                }
                            } else {
                                if (isFirst) {
                                    addTotalScore = addTotalScore.add(score);
                                }
                                addScored = addScored.add(isTrue ? score : BigDecimal.ZERO);
                            }
                        }
                    }
                }

                if (scores.size() <= recordIdx) {
                    scores.add(recordScore);
                } else {
                    scores.set(recordIdx, scores.get(recordIdx).add(recordScore));
                }
                isFirst = false;
            }

            areaList.add(areas);
            configs.add(config);
        }

        // 分数段统计
        JSONArray segedList = new JSONArray();
        if (StrUtil.isNotBlank(param.getSegments()) && !"[]".equals(param.getSegments())) {
            segedList = segedScoreCount(scores, param.getSegments(), totalScore);
        } else {
            segedList = segedScoreCount(scores, BigDecimal.valueOf(10), totalScore);
        }
        Boolean isSaveInteger = Optional.ofNullable(param.getIsSaveInteger()).orElse(true);

        JSONObject statsObj = new JSONObject();
        statsObj.set("total", totalSize);
        statsObj.set("totalScore", NumberUtil.div(totalScore, 1).setScale(isSaveInteger ? 1 : 0, RoundingMode.HALF_UP));
        statsObj.set("avgScored", NumberUtil.div(scored, totalSize).setScale(isSaveInteger ? 1 : 0, RoundingMode.HALF_UP));
        statsObj.set("avgAddScored", NumberUtil.div(addScored, totalSize).setScale(isSaveInteger ? 1 : 0, RoundingMode.HALF_UP));
        statsObj.set("totalAddScore", addTotalScore);

        // 计算中位数分数
        List<BigDecimal> sortedScores = new ArrayList<>(scores);
        sortedScores.sort(Comparator.naturalOrder());
        BigDecimal median;
        int middle = sortedScores.size() / 2;
        if (sortedScores.size() % 2 == 0) {
            median = sortedScores.get(middle - 1).add(sortedScores.get(middle))
                    .divide(BigDecimal.valueOf(2), isSaveInteger ? 1 : 0, RoundingMode.HALF_UP);
        } else {
            median = sortedScores.get(middle);
        }
        statsObj.set("medianScored", median);
        // 统计分数的时候最好是加上一共多少人（把页 1 那个位置，改成多少人吧（识别替换一下））
        taskName = replaceBracketContent(taskName, String.format("%s人", totalSize));
        // 生成批改结果
        Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>())
                .put("configs", configs)
                .put("areaList", areaList)
                .put("stats", statsObj)
                .put("fontSize", param.getFontSize())
                .put("segedList", segedList)
                .put("segedScore", param.getSegedScore())
                .put("taskName", taskName)
                .put("isSaveInteger", param.getIsSaveInteger())
                .put("scorePointTypeShowAverageScore", scorePointTypeShowAverageScore)
                .build();
        Map<String, Object> res = pdfService.createDoc(TexCmd.PDFLATEX, "correctstats", params);
        // 保存到远程文件夹
        String fileName = String.format("%s_统计结果.pdf", taskName);
        if (Objects.nonNull(param.getRemoteFileProps()) && StrUtil.isNotBlank(param.getRemoteFileProps().getSaveFileName())) {
            fileName = param.getRemoteFileProps().getSaveFileName();
        }
        save2Remote(fileName, res, param.getRemoteFileProps());
        setExportFolderPath(param, tasks.get(0));
        // 远程打印
        doPrint(res, param.getPrinterProps());
        return res;
    }

    /**
     * 按照分数段统计，最低分段下限不低于0
     *
     * @param scores     分数列表
     * @param segedScore 分数段大小
     * @param totalScore 总分
     * @return JSON数组，包含每个分数段的范围和数量
     */
    private JSONArray segedScoreCount(List<BigDecimal> scores, BigDecimal segedScore, BigDecimal totalScore) {
        Map<BigDecimal, Integer> segedMap = new HashMap<>();
        for (BigDecimal score : scores) {
            BigDecimal key;
            if (score.compareTo(totalScore) == 0) {
                // 总分单独计算
                key = totalScore;
            } else {
                // 计算分数段上边界
                key = score
                        .add(totalScore.subtract(score.add(BigDecimal.ONE)).remainder(segedScore))
                        .setScale(2, RoundingMode.HALF_UP);
            }
            segedMap.put(key, segedMap.getOrDefault(key, 0) + 1);
        }

        JSONArray segeList = new JSONArray();
        segedMap.entrySet().stream()
                .sorted((e1, e2) -> e2.getKey().compareTo(e1.getKey()))
                .forEach(e -> {
                    JSONObject obj = new JSONObject();
                    BigDecimal upper = e.getKey();
                    if (upper.compareTo(totalScore) == 0) {
                        obj.set("score", upper);
                    } else {
                        // 计算分数段下限，并确保不小于0
                        BigDecimal lower = upper.add(BigDecimal.ONE).subtract(segedScore);
                        if (lower.compareTo(BigDecimal.ZERO) < 0) {
                            lower = BigDecimal.ZERO;
                        }
                        obj.set("score", String.format("%d-%d", lower.intValue(), upper.intValue()));
                    }
                    obj.set("count", e.getValue());
                    segeList.add(obj);
                });
        return segeList;
    }


    /**
     * 按自定义区间统计分数，并且单独统计等于满分的"总分"。
     * <p>
     * 返回格式与老版本一致：
     * [{"score":100,"count":x},     // 第一个元素：总分组
     * {"score":"90-99","count":y}, // 后面是各段
     * ……
     * ]
     *
     * @param scores       分数列表
     * @param segmentsJson 前端传来的区间 JSON 字符串，每项示例：{"min":0,"max":10,"name":"低分段"}
     * @param totalScore   满分
     */
    public static JSONArray segedScoreCount(List<BigDecimal> scores,
                                            String segmentsJson,
                                            BigDecimal totalScore) {
        // 1. 先单独挑出所有等于满分的分数
        long totalCount = scores.stream()
                .filter(s -> s.compareTo(totalScore) == 0)
                .count();

        // 2. 把等于满分的分数从列表里移除，剩下的用来做分段统计
        List<BigDecimal> otherScores = scores.stream()
                .filter(s -> s.compareTo(totalScore) != 0)
                .collect(Collectors.toList());

        // 3. 解析并按 max 降序排列各自定义区间
        JSONArray segmentArr = JSONUtil.parseArray(segmentsJson);
        segmentArr.sort((o1, o2) -> {
            BigDecimal max1 = ((JSONObject) o1).getBigDecimal("max");
            BigDecimal max2 = ((JSONObject) o2).getBigDecimal("max");
            return max2.compareTo(max1);
        });

        // 4. 构造返回数组
        JSONArray result = new JSONArray();

        // 4.1 "总分"分组放第一位
        JSONObject totalObj = new JSONObject();
        totalObj.set("score", totalScore);
        totalObj.set("count", totalCount);
        result.add(totalObj);

        // 4.2 各自定义分段（只统计 < 满分 的分数）
        for (int i = 0; i < segmentArr.size(); i++) {
            JSONObject seg = segmentArr.getJSONObject(i);
            BigDecimal min = seg.getBigDecimal("min");
            BigDecimal max = seg.getBigDecimal("max");

            long cnt = otherScores.stream()
                    .filter(s -> s.compareTo(min) >= 0 && s.compareTo(max) <= 0)
                    .count();

            JSONObject out = new JSONObject();
            // 这里不用再判断 max==totalScore，因为已经把 totalScore 单独拿出来了
            out.set("score", String.format("%d-%d", min.intValue(), max.intValue()));
            out.set("count", cnt);
            result.add(out);
        }

        return result;
    }

    @Override
    public void syncDocName(DocCorrectTaskDTO param) {
        List<DocCorrectRecord> target = docCorrectRecordService.selectByTaskId(param.getId(), null);
        List<DocCorrectRecord> source = docCorrectRecordService.selectByTaskId(param.getTaskId(), null);
        List<DocCorrectRecord> updated = new ArrayList<>();

        for (int i = 0; i < Math.min(source.size(), target.size()); i++) {
            DocCorrectRecord targetRecord = target.get(i);
            DocCorrectRecord sourceRecord = source.get(i);
            String identity = sourceRecord.getIdentify();
            if (StringUtils.hasText(identity)) {
                DocCorrectRecord update = new DocCorrectRecord();
                update.setId(targetRecord.getId());
                update.setIdentify(sourceRecord.getIdentify());
                updated.add(update);
            }
        }

        docCorrectRecordService.updateBatchById(updated);
    }

    @Override
    public Map<String, Object> correctCount() {
        User loginUser = AuthUtil.getLoginUser();

        // 如果用户未登录，返回所有数据的统计（管理员视角）
        boolean isAdmin = loginUser == null || Objects.equals(loginUser.getRole(), UserRoleConsts.ADMIN);

        Long recordCount = docCorrectRecordMapper.selectCount(Wrappers.lambdaQuery(DocCorrectRecord.class)
                .eq(DocCorrectRecord::getDeleted, 0)
                .exists(!isAdmin,
                        "select 1 from user_doc ud where ud.deleted = 0 and ud.user_id = '" + (loginUser != null ? loginUser.getId() : "")
                                + "' and ud.doc_id = doc_correct_record.id"));
        Timestamp startTimestamp = Timestamp.valueOf(LocalDate.now().atStartOfDay());  // 今天的开始时间 (00:00:00)
        Timestamp endTimestamp = Timestamp.valueOf(LocalDate.now().atTime(LocalTime.MAX));
        Long todayRecordsCount = docCorrectRecordMapper.selectCount(Wrappers.lambdaQuery(DocCorrectRecord.class)
                .eq(DocCorrectRecord::getDeleted, 0)  // 确保未删除的记录
                .ge(DocCorrectRecord::getCreateTime, startTimestamp)
                .le(DocCorrectRecord::getCreateTime, endTimestamp)
                .exists(!isAdmin,
                        "SELECT 1 FROM user_doc ud WHERE ud.deleted = 0 " +
                                "AND ud.user_id = '" + (loginUser != null ? loginUser.getId() : "") + "' " +
                                "AND ud.doc_id = doc_correct_record.id"));  // 判断用户是否与记录关联
        Long taskCount = docCorrectTaskMapper.selectCount(Wrappers.lambdaQuery(DocCorrectTask.class)
                .eq(DocCorrectTask::getDeleted, 0)
                .exists(!isAdmin,
                        "select 1 from user_doc ud where ud.deleted = 0 and ud.user_id = '" + (loginUser != null ? loginUser.getId() : "")
                                + "' and ud.task_id = doc_correct_task.id"));
        Long todayTasksCount = docCorrectTaskMapper.selectCount(Wrappers.lambdaQuery(DocCorrectTask.class)
                .eq(DocCorrectTask::getDeleted, 0)
                .ge(DocCorrectTask::getCreateTime, startTimestamp)
                .le(DocCorrectTask::getCreateTime, endTimestamp)
                .exists(!isAdmin,
                        "select 1 from user_doc ud where ud.deleted = 0 and ud.user_id = '" + (loginUser != null ? loginUser.getId() : "")
                                + "' and ud.task_id = doc_correct_task.id"));

        Long printerDeviceCount = (long) printerService.getDevices().size();
        return MapBuilder.create(new HashMap<String, Object>())
                .put("taskCount", taskCount)
                .put("recordCount", recordCount)
                .put("printerDeviceCount", printerDeviceCount)
                .put("todayRecordsCount", todayRecordsCount)
                .put("todayTasksCount", todayTasksCount)
                .build();
    }

    @Override
    public Map<String, Object> taskStats(String taskId) {
        Long recordCount = docCorrectRecordMapper.selectCount(Wrappers.lambdaQuery(DocCorrectRecord.class)
                .eq(DocCorrectRecord::getDeleted, 0)
                .eq(DocCorrectRecord::getTaskId, taskId));

        List<Map<String, Object>> tasksStats = docCorrectTaskMapper.taskStats(taskId);
        Map<String, Object> taskStats;
        if (CollUtil.isNotEmpty(tasksStats)) {
            taskStats = tasksStats.get(0);
        } else {
            taskStats = MapUtil.builder(new HashMap<String, Object>()).put("totalScore", 0).build();
        }
        BigDecimal totalScore = NumberUtil.toBigDecimal(taskStats.getOrDefault("totalScore", 0).toString());
        taskStats.put("recordCount", recordCount);
        taskStats.put("avgScore", NumberUtil.div(totalScore, recordCount).setScale(2, RoundingMode.HALF_UP));

        return taskStats;
    }

    @Override
    public Map<Integer, Map<Integer, DocCorrectResultDTO>> qsStats(String taskId, String configId) {
        List<DocCorrectResultDTO> results = docCorrectTaskMapper.selectQsStats(taskId, configId);
        Long recordCount = docCorrectRecordMapper.selectCount(Wrappers.lambdaQuery(DocCorrectRecord.class)
                .eq(DocCorrectRecord::getDeleted, 0)
                .eq(DocCorrectRecord::getTaskId, taskId));

        return results.stream().collect(Collectors.groupingBy(DocCorrectResult::getAreaIdx,
                Collectors.toMap(DocCorrectResult::getQsIdx, result -> {
                    result.setAvgScore(
                            NumberUtil.div(result.getTotalScore(), recordCount).setScale(2, RoundingMode.HALF_UP));
                    result.setCorrectRate(NumberUtil.mul(NumberUtil.div(result.getCorrectNum(), recordCount), 100)
                            .setScale(2, RoundingMode.HALF_UP));
                    return result;
                })));
    }

    @Override
    public List<Map<String, Object>> tasksStats(List<String> taskIds) {
        if (CollUtil.isEmpty(taskIds)) {
            return Collections.emptyList();
        }

        String taskIdsStr = taskIds.stream().collect(Collectors.joining("','"));
        Map<Object, Map<String, Object>> recordCountMap = docCorrectRecordMapper.countByTaskIds(taskIdsStr)
                .stream().collect(Collectors.toMap(map -> map.get("taskId"), map -> map));
        Map<Object, Map<String, Object>> statsCountMap = docCorrectTaskMapper.taskStats(taskIdsStr)
                .stream().collect(Collectors.toMap(map -> map.get("taskId"), map -> map));
        return taskIds.stream().map(taskId -> {
            Map<String, Object> stats = new HashMap<String, Object>();
            Map<String, Object> recordCounts = recordCountMap.get(taskId);
            Integer recordCount = 0;
            if (recordCounts != null) {
                recordCount = Convert.toInt(recordCounts.getOrDefault("recordCount", 0));
            }
            BigDecimal totalScore = BigDecimal.ZERO;
            BigDecimal totalScoree = BigDecimal.ZERO;
            Map<String, Object> statsCounts = statsCountMap.get(taskId);
            if (statsCounts != null) {
                totalScore = NumberUtil.toBigDecimal(statsCounts.getOrDefault("totalScore", 0).toString());
                totalScoree = NumberUtil.toBigDecimal(statsCounts.getOrDefault("totalScoree", 0).toString());
            }

            stats.put("recordCount", recordCount);
            stats.put("totalScore", totalScore);
            stats.put("avgScore", NumberUtil.div(totalScore, recordCount).setScale(2, RoundingMode.HALF_UP));
            stats.put("docScore", NumberUtil.div(totalScoree, recordCount).setScale(2, RoundingMode.HALF_UP));
            stats.put("taskId", taskId);
            return stats;
        }).collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> fileZip(DocCorrectTaskDTO param) {
        List<CompletableFuture<Map<String, Object>>> fileFutures = new ArrayList<>();
        // 批改不含原卷
        param.setIsPreview(false);
        fileFutures.add(CompletableFuture.supplyAsync(() -> createReviewedDoc(param)));
        // 批改含原卷
        DocCorrectTaskDTO param1 = BeanUtil.copyProperties(param, DocCorrectTaskDTO.class);
        param1.setIsPreview(true);
        param1.setIsReversed(false); // 批改含原卷不需要交换顺序
        fileFutures.add(CompletableFuture.supplyAsync(() -> createReviewedDoc(param1)));
        // 统计结果
        fileFutures.add(CompletableFuture.supplyAsync(() -> createStatsDoc(param)));

        CompletableFuture<Void> allFutures = CompletableFuture.allOf(fileFutures.toArray(new CompletableFuture[0]));
        CompletableFuture<List<Map<String, Object>>> allResults = allFutures.thenApply(v -> fileFutures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList()));
        List<Map<String, Object>> results = allResults.join();

        // 压缩文件
        String zipUrl = FileUtil.INSTANCE.zipUrls(param.getName(), results.stream()
                .map(map -> map.get("fileUrl").toString()).collect(Collectors.toList()));

        return Collections.singletonMap("fileUrl", zipUrl);
    }

    /**
     * 保存到远程文件夹
     */
    private void save2Remote(String filename, Map<String, Object> docRes, SaveRemoteFileDTO props) {
        if (Objects.nonNull(props) && props.isSave()) {
            String fileUrl = (String) docRes.get("fileUrl");
            props.setSaveFileName(filename);
            props.setFilename(FileUtil.INSTANCE.ctxUrl2Path(fileUrl));
            remoteFileService.save2Remote(props);
        }
    }

    /**
     * 远程打印
     */
    private void doPrint(Map<String, Object> docRes, PrinterPropsDTO printerProps) {
        if (Objects.nonNull(printerProps) && printerProps.isPrint()) {
            String fileUrl = (String) docRes.get("fileUrl");
            String filename = FileUtil.INSTANCE.ctxUrl2Path(fileUrl);
            printerProps.setFilename(filename);
            String printId = printerService.print(printerProps);
            docRes.put("printId", printId);
        }
    }

    private void updateExportCompletedStatus(DocCorrectTaskDTO docCorrectTaskDTO) {
        try {
            if (StrUtil.isBlank(docCorrectTaskDTO.getFileId())) {
                return;
            }

            // 更新 doc_correct_file 表的 export_completed
            try {
                DocCorrectFile docCorrectFile = new DocCorrectFile();
                docCorrectFile.setId(docCorrectTaskDTO.getFileId());
                docCorrectFile.setExportCompleted(true);
                docCorrectFileMapper.updateById(docCorrectFile);
            } catch (Exception e) {
                log.error("Failed to update doc_correct_file export_completed status for fileId: {}, error: {}",
                        docCorrectTaskDTO.getFileId(), e.getMessage());
            }

            // 查询 ftp_files 表获取 file_id 信息
            try {
                QueryWrapper<FtpFiles> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("file_id", docCorrectTaskDTO.getFileId());
                List<FtpFiles> dbFtpFiles = ftpFilesMapper.selectList(queryWrapper);
                // 一般dbFtpFiles只有1个或0，所以可以for循环
                for (FtpFiles ftpFile : dbFtpFiles) {
                    if (ftpFile != null && StrUtil.isNotBlank(ftpFile.getPath()) && StrUtil.isNotBlank(ftpFile.getFilename())) {
                        String filePath = ftpFile.getPath() + File.separator + ftpFile.getFilename();
                        // 更新 ftp_message 表的 export_completed
                        FtpMessage ftpMessage = new FtpMessage();
                        ftpMessage.setExportCompleted(true);
                        ftpMessage.setExportCompletedTime(LocalDateTime.now());
                        LambdaUpdateWrapper<FtpMessage> updateWrapper = Wrappers.lambdaUpdate();
                        updateWrapper.eq(FtpMessage::getFilePath, filePath);

                        ftpMessageMapper.update(ftpMessage, updateWrapper);
                    }
                }

            } catch (Exception e) {
                log.error("Failed to update ftp_message export_completed status for fileId: {}, error: {}",
                        docCorrectTaskDTO.getFileId(), e.getMessage());
            }
        } catch (Exception e) {
            log.error("Error in updateExportCompletedStatus for task: {}, error: {}",
                    docCorrectTaskDTO.getId(), e.getMessage());
        }
    }

}
