package com.chaty.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.dto.UserDTO;
import com.chaty.entity.User;

public interface UserService {

    void insert(UserDTO user);
    
    void updateById(User user);

    User findById(String id);

    List<User> list(User params);

    void delete(String id);

    User findByUsername(String username);

    List<UserDTO> userCount();

    List<User> selectByClassId(String classId);

    void updateByNicknameAndStudentId(User user);

    IPage<UserDTO> findPage(UserDTO params);
}
