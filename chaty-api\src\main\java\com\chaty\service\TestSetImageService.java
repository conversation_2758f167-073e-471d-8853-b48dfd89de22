package com.chaty.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.entity.TestSetImageEntity;

public interface TestSetImageService extends IService<TestSetImageEntity> {

    /** 分页查询（支持按测试集、题型、来源试卷ID过滤） */
    IPage<TestSetImageEntity> page(Integer pageNumber, Integer pageSize,
                                   Long testSetId, String questionType, String fromRecordId);

    /** 新增（仅DB） */
    Long add(TestSetImageEntity param);

    /** 更新（仅DB） */
    Long updateOne(TestSetImageEntity param);

    /** 删除（仅DB） */
    void deleteById(Long id);
}
