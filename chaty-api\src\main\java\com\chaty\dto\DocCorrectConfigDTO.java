package com.chaty.dto;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import com.chaty.entity.DocCorrectConfig;
import com.chaty.util.FileUtil;
import com.chaty.util.PDFUtil;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import cn.hutool.core.util.StrUtil;

@Data
public class DocCorrectConfigDTO extends DocCorrectConfig {

    private PageDTO<DocCorrectConfig> page;

    private JSONObject configObj;

    private JSONArray areasObj;

    private JSONObject pageSettingsObj;

    public JSONObject getConfigObj() {
        return Optional.ofNullable(configObj)
                .orElseGet(() -> JSONUtil.parseObj(getConfig()));
    }

    public JSONArray getAreasObj() {
        return Optional.ofNullable(areasObj)
                .orElseGet(() -> JSONUtil.parseArray(getAreas()));
    }

    public JSONObject getPageSettingsObj() {
        return Optional.ofNullable(pageSettingsObj)
                .orElseGet(() -> {
                    String pageSettings = getPageSettings();
                    return StrUtil.isNotBlank(pageSettings) ? JSONUtil.parseObj(pageSettings) : new JSONObject();
                });
    }

    public JSONObject getDocInfo() {
        return PDFUtil.getPDFInfo(FileUtil.INSTANCE.relUrl2Path(getDocurl()));
    }

    public ScoreTypesDTO getScoreType() {
        ScoreTypesDTO res = new ScoreTypesDTO();
        Map<String, Double> scoreTypeObj = new HashMap<>();
        JSONArray areas = getAreasObj();
        areas.forEach(questionsObj -> {
            JSONObject obj = (JSONObject) questionsObj;
            JSONArray questions = obj.getJSONArray("questions");
            questions.forEach(questionObj -> {
                JSONObject question = (JSONObject) questionObj;
                String scoreType = (String) question.getOrDefault("scoreType", "总分");
                Double score = question.getDouble("score", 0.0);
                if (!scoreType.equalsIgnoreCase("总分")) {
                    scoreTypeObj.merge(scoreType, score, Double::sum);
                }
                scoreTypeObj.merge("总分", score, Double::sum);
            });
        });
        res.initByScoreTypeObj(scoreTypeObj);
        return res;
    }

}
