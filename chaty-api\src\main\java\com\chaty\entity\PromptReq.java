package com.chaty.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PromptReq {

    @NotNull(message = "modelRequestId不能为空")
    private Integer modelRequestId;

    @NotNull(message = "key不能为空")
    private String key;

    @NotNull(message = "value不能为空")
    private String value;
}
