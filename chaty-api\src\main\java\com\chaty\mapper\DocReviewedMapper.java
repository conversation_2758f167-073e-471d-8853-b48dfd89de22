package com.chaty.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.chaty.entity.DocReviewed;

@Mapper
public interface DocReviewedMapper {

    List<DocReviewed> list(DocReviewed param);

    void insertOne(DocReviewed entity);

    void updateById(DocReviewed entity);

    @Update("update doc_reviewed set deleted = 1 where id = #{id}")
    void deleteById(@Param("id") Integer id);

    @Select("select * from doc_reviewed where id = #{id}")
    DocReviewed selectById(@Param("id") Integer id);
    
}
