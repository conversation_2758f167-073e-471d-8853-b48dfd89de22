package com.chaty.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.dto.DocCorrectResultDTO;
import com.chaty.dto.DocCorrectTaskDTO;

public interface DocCorrectTaskService {

    IPage<DocCorrectTaskDTO> page(DocCorrectTaskDTO param);

    void add(DocCorrectTaskDTO param);

    void update(DocCorrectTaskDTO param);

    void delete(String id);

    void execute(DocCorrectTaskDTO param);

    void executeBatch(List<DocCorrectTaskDTO> param);

    void reIdentifyNameOrStudentNumberType(DocCorrectTaskDTO param);

    void addByFile(DocCorrectTaskDTO param);

    Map<String, Object> createReviewedDoc(DocCorrectTaskDTO param);

    List<DocCorrectTaskDTO> statsByStatus();

    DocCorrectTaskDTO getById(String id);

    Map<String, Object> createStatsDoc(DocCorrectTaskDTO param);

    void syncDocName(DocCorrectTaskDTO param);

    Map<String, Object> correctCount();

    Map<String, Object> taskStats(String taskId);

    Map<Integer, Map<Integer, DocCorrectResultDTO>> qsStats(String taskId, String configId);

    List<Map<String, Object>> tasksStats(List<String> taskIds);

    Map<String, Object> fileZip(DocCorrectTaskDTO param);
}
