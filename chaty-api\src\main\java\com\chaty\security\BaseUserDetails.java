package com.chaty.security;

import java.util.Collection;
import java.util.Collections;
import java.util.Objects;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import com.chaty.dto.SchoolDTO;
import com.chaty.entity.User;

import lombok.Getter;
import lombok.Setter;

public class BaseUserDetails implements UserDetails {

    @Getter
    private User user;

    @Setter
    @Getter
    private SchoolDTO school;

    public BaseUserDetails(User user) {
        this.user = user;
    }

    public BaseUserDetails(BaseUserDetails other) {
        this.user = other.getUser();
        this.school = other.getSchool();
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return Collections.emptyList();
    }

    @Override
    public String getPassword() {
        return user.getPassword();
    }

    @Override
    public String getUsername() {
        return user.getUsername();
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return Objects.equals(1, user.getStatus());
    }

}