package com.chaty.service.cache;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chaty.entity.QuestionType;

import java.util.List;

public interface QuestionTypeService {

    /** 新增（含名称唯一性校验），写库并同步到 Redis */
    QuestionType add(QuestionType body);

    /** 更新（含名称唯一性校验），写库并同步到 Redis */
    QuestionType update(QuestionType body);

    /** 按主键删除，写库并同步到 Redis */
    boolean delete(Integer id);

    /** 批量删除，写库并同步到 Redis */
    int deleteBatch(List<Integer> ids);

    /** 详情：优先读 Redis，未命中再回源 DB 并回填 Redis */
    QuestionType detail(Integer id);

    /** 列表：优先读 Redis 的全量列表（小表适合），支持 name 模糊与分页 */
    Page<QuestionType> list(String name, long pageNo, long pageSize);

    /** 返回全量题型（优先 Redis） */
    List<QuestionType> all();

    /** 手动刷新全量缓存（可用于运维） */
    void refreshAllCache();

    // ====== 新增：按 name 精确查（优先 Redis） ======
    /**
     * 通过题型名称精确查找（忽略前后空格，大小写不敏感的 Redis 索引；DB 侧为精确等值匹配）。
     * @param name 题型名（必填）
     * @return QuestionType 或 null
     */
    QuestionType getByName(String name);
}
