package com.chaty.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.dto.SchoolDTO;
import com.chaty.entity.School;

import java.util.List;

public interface SchoolService {

    List<School> selectAll();

    Integer add(SchoolDTO school);

    Integer updateById(SchoolDTO school);

    Integer deleteById(String id);

    Integer deleteByBatch(List<String> ids);

    IPage<?> findPage(SchoolDTO params);
}
