\documentclass[15pt]{beamer}
\usepackage{tikz}
\usepackage{ctex}
\usepackage{adjustbox}
\usetheme{Madrid}
\usecolortheme{default}
{% comment %} \setCJKmainfont{AR PL UKai CN} {% endcomment %}
\title[] %optional
{Madrid theme + beaver}
\subtitle{Demonstrating larger fonts}
\author[Generated by SolveGPT] % (optional)
{}

\institute[] % (optional)
{
}

\date[] % (optional)
{}

% Use a simple TikZ graphic to show where the logo is positioned
\logo{}
\begin{frame}
\frametitle{题目}

\begin{block}

${question}

\end{block}

\end{frame}

\begin{frame}[allowframebreaks]
\frametitle{正确答案}

<#if answer?has_content>

\begin{block}

${answer}

\end{block}

</#if>

\end{frame}

\begin{frame}[allowframebreaks]
\frametitle{知识点}

<#if knowledge?has_content>

\begin{block}

${knowledge}

\end{block}

</#if>

\end{frame}

\begin{frame}[allowframebreaks]
\frametitle{输入答案}

<#if ocrText?has_content>

\begin{block}

${ocrText}

\end{block}

</#if>

\end{frame}

\begin{frame}[allowframebreaks]
\frametitle{批改结果}

<#if trueText?has_content>

\begin{block}{是否正确}

${trueText}

\end{block}

</#if>

<#if isTrue == 0>
\begin{block}{错误}
${errText}

\end{block}
</#if>

<#if comment?has_content>
\begin{block}{评价}

${comment}

\end{block}

</#if>

\end{frame}

\end{document}
