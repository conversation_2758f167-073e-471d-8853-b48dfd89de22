package com.chaty.service.admin.impl;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.dto.AdminRoleDTO;
import com.chaty.entity.admin.AdminRole;
import com.chaty.mapper.admin.AdminRoleMapper;
import com.chaty.service.admin.AdminRoleService;
import com.github.pagehelper.util.StringUtil;

import cn.hutool.core.bean.BeanUtil;

@Service
public class AdminRoleServiceImpl extends ServiceImpl<AdminRoleMapper, AdminRole> implements AdminRoleService {

    @Override
    public void add(AdminRoleDTO params) {
        AdminRole add = BeanUtil.copyProperties(params, AdminRole.class);
        this.save(add);
    }

    @Override
    public void update(AdminRoleDTO params) {
        AdminRole update = BeanUtil.copyProperties(params, AdminRole.class);
        this.updateById(update);
    }

    @Override
    public void delete(String id) {
        LambdaUpdateWrapper<AdminRole> wrapper = Wrappers.lambdaUpdate(AdminRole.class)
                .set(AdminRole::getDeleted, true)
                .eq(AdminRole::getId, id);
        this.update(wrapper);
    }

    @Override
    public IPage<?> getPage(AdminRoleDTO params) {
        LambdaQueryWrapper<AdminRole> wrapper = Wrappers.lambdaQuery(AdminRole.class)
                .eq(AdminRole::getDeleted, false)
                .eq(StringUtils.hasText(params.getId()), AdminRole::getId, params.getId())
                .like(StringUtils.hasText(params.getName()), AdminRole::getName, params.getName())
                .orderByDesc(AdminRole::getCreateTime);
        return this.page(params.getPage().page(AdminRole.class), wrapper);
    }

}
