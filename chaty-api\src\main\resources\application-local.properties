server.port=20001

spring.servlet.multipart.max-file-size=300MB
spring.servlet.multipart.max-request-size=300MB
server.servlet.session.timeout=30d

spring.jackson.default-property-inclusion=non_null

mybatis.mapper-locations=classpath:mapper/*.xml
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis.configuration.mapUnderscoreToCamelCase=true

#spring.datasource.url=***********************
spring.datasource.url=***************************************************************************
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

file.local.path=D:\\chaty\\files
file.local.ctxpath=/static
file.tex.path=D:\\chaty\\files

logging.file.name=logs/chaty.log

api.openai.url=http://ora.173177.xyz:20008
api.openai.fallbackModel=gpt-4o
api.openai.proxyed[0].keys=sk-8fQSTtLZ4IrwqE8XSJN71yzS3RSbiEFkdEF9LdlzO20ADTfZ
api.openai.proxyed[0].url=http://hk.saomiaoshijuan.com:13000/v1
api.openai.proxyed[0].models=gemini-1.5-pro,gemini-1.5-flash,gemini-2.0-flash-exp,gpt-4o,gpt-4o-mini,gpt-4o-2024-08-06
api.openai.webclient.key=sk-8fQSTtLZ4IrwqE8XSJN71yzS3RSbiEFkdEF9LdlzO20ADTfZ



server.url=https://www.saomiaoshijuan.com

api.temcentcloud.secretid=AKIDKXWHo23pNhnfJitpWzDHCvc7OQEoAJG1
api.temcentcloud.secretkey=c0FJ9l9RWN2YrwmOluoUwA7yd6LL7eZu

# claude
# api.claude.url=https://api.anthropic.com
api.claude.url=http://************:80/claude
api.claude.key=************************************************************************************************************

# qianwen
api.qianwen.url=https://dashscope.aliyuncs.com
api.qianwen.key=sk-4c903585320d4bb99988b9a9e9cc92a6

# paddle ocr
ocr.paddle.script=/root/service/chaty/ocr.py

# ollama
api.ollama.url=http://127.0.0.1:11434

# doc correct task
doc-correct.task-num=32
doc-correct.queue-size=800

# 答题卡识别
answerCard.executorFile=/root/service/chaty/detect.py

# alist配置
alist.url=https://alist.saomiaoshijuan.com
alist.token=alist-dc175cb7-d8bd-451f-8387-c8894cefd0b6e5lPfuAcDnbI7e4FWNN37L4hH8tp2Ve5VP3FEcUurz12qDstsyC7Ax5TNym4SG4M
alist.list-path=/ftp/in_GM8525cdn_zjedu
alist.out-path=/ftp/out_GM8525cdn_zjedu
alist.root-path=/ftp

# 远程打印机配置
lianke.url=https://cloud.liankenet.com
lianke.api-key=i4BAOLacP9lDh6Hxs6PkAtJGycCzz69s
lianke.openUrl=https://open.liankenet.com
lianke.username=13666680278
lianke.password=1122liankeyun
lianke.devices[0].deviceId=lc11cd15008346
lianke.devices[0].deviceKey=FZCV3dlQNJgPxJ8m
lianke.devices[0].deviceName=defaultPrinter
lianke.devices[1].deviceId=lc11cd29251352
lianke.devices[1].deviceKey=sZZMPRYD65q5FF7c
lianke.devices[1].deviceName=AIHardwareDemonstration
lianke.devices[2].deviceId=lc22ed23216208
lianke.devices[2].deviceKey=EXSj3zXeBQ6H964U
lianke.devices[2].deviceName=Teaching9223OnlyA4

task.correctArea.corePoolSize=128
task.correctArea.maxPoolSize=254

# Redis
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.database=1
# Lettuce
spring.redis.jedis.pool.max-active=128
spring.redis.jedis.pool.max-idle=36
spring.redis.jedis.pool.min-idel=18
spring.redis.jedis.pool.max-wait=3000
spring.redis.timeout=5000

api.tencentcloud.ecc.secretId=AKIDKXWHo23pNhnfJitpWzDHCvc7OQEoAJG1
api.tencentcloud.ecc.secretKey=c0FJ9l9RWN2YrwmOluoUwA7yd6LL7eZu
api.tencentcloud.ecc.endpoint=ecc.tencentcloudapi.com

api.baiduocr.appid=*********
api.baiduocr.apiKey=ehBOrsA77pZwVD00M0NS8oTK
api.baiduocr.secretKey=ftAXxAW9GMaieb9klfiLxxy2UFNfXnvL

api.aliyun.accessKeyId=LTAI5tFqxHUvkZ8jKuLyNqD1
api.aliyun.accessKeySecret=******************************

