package com.chaty.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class RemoteFileWithRemarkDTO extends RemoteFileDTO {
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 文件ID
     */
    private String fileId;
    
    /**
     * 配置包ID
     */
    private String configPackageId;
    
    /**
     * 文件类型 (file/configPackage/essay)
     */
    private String fileType;
    
    /**
     * 名称
     */
    private String name;

    /**
     * ftp_files 的 id
     */
    private Integer Id;
} 