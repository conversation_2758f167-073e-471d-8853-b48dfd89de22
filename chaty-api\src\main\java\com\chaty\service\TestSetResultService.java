package com.chaty.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.entity.TestSetResultEntity;
import com.chaty.enums.TestSetResultStatus;

public interface TestSetResultService extends IService<TestSetResultEntity> {

    /** 分页（支持按 testSetId / status 过滤） */
    IPage<TestSetResultEntity> page(Integer pageNumber, Integer pageSize,
                                    Long testSetId, TestSetResultStatus status);

    /** 新增（仅DB；自动补齐快照与默认状态） */
    Long add(TestSetResultEntity param);

    /** 更新（仅DB；通常不覆盖快照） */
    Long updateOne(TestSetResultEntity param);

    /** 删除（仅DB） */
    void deleteById(Long id);
}
