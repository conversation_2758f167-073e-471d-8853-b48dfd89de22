package com.chaty.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.entity.Contact;
import com.chaty.dto.ContactDTO;
import com.chaty.exception.BaseException;
import com.chaty.mapper.ContactMapper;
import com.chaty.service.ContactService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.util.StrUtil;

import java.time.LocalDateTime;
import java.util.Objects;

@Service
public class ContactServiceImpl extends ServiceImpl<ContactMapper, Contact> implements ContactService {

    @Override
    @Transactional
    public ContactDTO addContact(ContactDTO dto) {
        if (StrUtil.isBlank(dto.getName()) || StrUtil.isBlank(dto.getEmail()) || 
            StrUtil.isBlank(dto.getPhone()) || StrUtil.isBlank(dto.getSchoolName())) {
            throw new BaseException("姓名、邮箱、电话和学校名称不能为空");
        }

        Contact contact = new Contact();
        BeanUtils.copyProperties(dto, contact);
        
        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        contact.setCreateTime(now);
        contact.setUpdateTime(now);

        save(contact);

        ContactDTO result = new ContactDTO();
        BeanUtils.copyProperties(contact, result);
        return result;
    }

    @Override
    @Transactional
    public void deleteContact(Integer id) {
        if (id == null) {
            throw new BaseException("id不能为空");
        }

        Contact contact = getById(id);
        if (contact == null) {
            throw new BaseException("联系记录不存在");
        }

        removeById(id);
    }

    @Override
    @Transactional
    public ContactDTO updateContact(ContactDTO dto) {
        if (dto.getId() == null) {
            throw new BaseException("id不能为空");
        }

        Contact contact = getById(dto.getId());
        if (contact == null) {
            throw new BaseException("联系记录不存在");
        }

        BeanUtils.copyProperties(dto, contact, "id", "createTime");
        
        // 设置更新时间
        contact.setUpdateTime(LocalDateTime.now());

        updateById(contact);

        ContactDTO result = new ContactDTO();
        BeanUtils.copyProperties(contact, result);
        return result;
    }

    @Override
    public IPage<ContactDTO> selectPage(ContactDTO param) {
        LambdaQueryWrapper<Contact> queryWrapper = Wrappers.lambdaQuery();

        queryWrapper.eq(Objects.nonNull(param.getId()), Contact::getId, param.getId());
        queryWrapper.like(StrUtil.isNotBlank(param.getName()), Contact::getName, param.getName());
        queryWrapper.like(StrUtil.isNotBlank(param.getEmail()), Contact::getEmail, param.getEmail());
        queryWrapper.like(StrUtil.isNotBlank(param.getPhone()), Contact::getPhone, param.getPhone());
        queryWrapper.like(StrUtil.isNotBlank(param.getSchoolName()), Contact::getSchoolName, param.getSchoolName());
        queryWrapper.like(StrUtil.isNotBlank(param.getMessage()), Contact::getMessage, param.getMessage());
        queryWrapper.eq(Objects.nonNull(param.getCreateTime()), Contact::getCreateTime, param.getCreateTime());
        queryWrapper.eq(Objects.nonNull(param.getUpdateTime()), Contact::getUpdateTime, param.getUpdateTime());
        queryWrapper.orderByDesc(Contact::getCreateTime);

        if (param.getPage() == null) {
            throw new BaseException("分页参数不能为空");
        }

        IPage<Contact> contactPage = page(param.getPage().page(Contact.class), queryWrapper);

        return contactPage.convert(entity -> {
            ContactDTO dto = new ContactDTO();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        });
    }

    @Override
    public Contact getContactById(Integer id) {
        if (id == null) {
            return null;
        }
        return getById(id);
    }
} 