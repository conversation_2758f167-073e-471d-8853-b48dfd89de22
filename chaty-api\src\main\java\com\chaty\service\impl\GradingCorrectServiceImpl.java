package com.chaty.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.util.StrUtil;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.dto.CorrectAreaDTO;
import com.chaty.dto.CorrectAreaQsDTO;
import com.chaty.dto.DataCounter;
import com.chaty.dto.DocCorrectConfigDTO;
import com.chaty.dto.GradingCorrectDTO;
import com.chaty.entity.DocCorrectConfig;
import com.chaty.entity.DocCorrectFile;
import com.chaty.entity.DocCorrectRecord;
import com.chaty.entity.DocCorrectTask;
import com.chaty.entity.GradingCorrect;
import com.chaty.enums.CorrectEnums;
import com.chaty.exception.BaseException;
import com.chaty.mapper.DocCorrectConfigMapper;
import com.chaty.mapper.DocCorrectFileMapper;
import com.chaty.mapper.DocCorrectRecordMapper;
import com.chaty.mapper.DocCorrectTaskMapper;
import com.chaty.mapper.GradingCorrectMapper;
import com.chaty.service.GradingCorrectService;
import com.chaty.service.PDFService;
import com.chaty.service.PDFService.TexCmd;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class GradingCorrectServiceImpl extends ServiceImpl<GradingCorrectMapper, GradingCorrect>
        implements GradingCorrectService {

    @Resource
    private GradingCorrectMapper gradingCorrectMapper;
    @Resource
    private DocCorrectTaskMapper docCorrectTaskMapper;
    @Resource
    private DocCorrectRecordMapper docCorrectRecordMapper;
    @Resource
    private DocCorrectFileMapper docCorrectFileMapper;
    @Resource
    private DocCorrectConfigMapper docCorrectConfigMapper;
    @Resource
    private PDFService pdfService;
    @Resource
    private GptAskLogServiceImpl gptAskLogService;

    @Override
    public GradingCorrect doGrade(GradingCorrectDTO params) {
        String templateId = params.getTemplateId();
        // 查询批改任务
        DocCorrectFile tempFile = docCorrectFileMapper.selectById(templateId);
        List<DocCorrectTask> tempTasks = docCorrectTaskMapper
                .selectList(Wrappers.lambdaQuery(DocCorrectTask.class)
                        .eq(DocCorrectTask::getFileId, templateId)
                        .orderByAsc(DocCorrectTask::getName));
        // 任务校验
        if (Objects.isNull(tempTasks)) {
            throw new BaseException("未查询到批改任务");
        }
        if (tempTasks.stream().anyMatch(task -> !task.getStatus().equals(CorrectEnums.CorrectTakStatus.FINISH))) {
            throw new BaseException("模板任务未批改完成");
        }
        // 查询批改试卷列表
        List<List<DocCorrectRecord>> tempTaskRecords = tempTasks.stream().map(task -> {
            return docCorrectRecordMapper.selectList(Wrappers.lambdaQuery(DocCorrectRecord.class)
                    .eq(DocCorrectRecord::getTaskId, task.getId())
                    .orderByAsc(DocCorrectRecord::getDocname));
        }).collect(Collectors.toList());
        // 任务评估
        List<GradingCorrectDTO> results = new ArrayList<>();
        DataCounter qsCounter = new DataCounter();
        try {
            for (int docIdx = 0; docIdx < tempTaskRecords.get(0).size(); docIdx++) {
                for (int taskIdx = 0; taskIdx < tempTaskRecords.size(); taskIdx++) {
                    DocCorrectRecord record = tempTaskRecords.get(taskIdx).get(docIdx);
                    GradingCorrectDTO result = gradeRecord(record, qsCounter);
                    results.add(result);
                }
                // 重置计数器
                qsCounter.resetIndex();
            }
        } catch (IndexOutOfBoundsException e) {
            throw new BaseException("批改试卷列表不一致，请检查批改任务");
        }
        // 合并评估结果
        Integer totalNum = 0, fixedNum = 0, failNum = 0, modifyNum = 0;
        for (GradingCorrectDTO result : results) {
            totalNum += result.getTotalNum();
            fixedNum += result.getFixedNum();
            failNum += result.getFailNum();
            modifyNum += result.getModifyNum();
        }
        // 保存评估结果
        GradingCorrect save = new GradingCorrect();
        save.setTemplateId(templateId);
        save.setTemplateName(tempFile.getName());
        save.setTotalNum(totalNum);
        save.setFixedNum(fixedNum);
        save.setFailNum(failNum);
        save.setModifyNum(modifyNum);
        save.setQsStats(qsCounter.toStr());
        if (StrUtil.isNotBlank(params.getRemark())) {
            save.setRemark(params.getRemark());
        }

        try {
            String stats = JSONUtil.toJsonStr(gptAskLogService.getStatsByFileId(params.getTemplateId()));
            save.setStats(stats);
        }catch (Exception e) {
            log.error("统计平均时间失败", e);
        }
        gradingCorrectMapper.insert(save);

        return save;
    }

    /**
     * 任务评估
     */
    public GradingCorrectDTO gradeRecord(DocCorrectRecord record, DataCounter qsCounter) {
//        log.info("评估试卷：{}", record.getDocname());
        Integer totalNum = 0, fixedNum = 0, failNum = 0, modifyNum = 0;
        JSONArray areas = JSONUtil.parseArray(record.getReviewed());
        // 遍历区域
        for (int areaIdx = 0; areaIdx < areas.size(); areaIdx++) {
            CorrectAreaDTO area = CorrectAreaDTO.create(areas.getJSONObject(areaIdx));
//            if (!area.isSuccess()) {
//                log.info("跳过区域，评估试卷 {} 的区域 {} 未批改", record.getDocname(), areaIdx + 1);
//                for (int qsIdx = 0; qsIdx < area.getReviewed().size(); qsIdx++) {
//                    qsCounter.countFailOne();
//                    failNum ++;
//                    fixedNum ++;
//                    totalNum++;
//                }
//                continue;
//            }
            // 试题评估
            for (int qsIdx = 0; qsIdx < area.getReviewed().size(); qsIdx++) {
                CorrectAreaQsDTO qs = CorrectAreaQsDTO.create(area.getReviewed().get(qsIdx));
                if (!area.isSuccess()) {
                    failNum++;
                    fixedNum++;
                } else {
                    if (qs.hasChangeVal()) {
                        fixedNum++; // 修正数量
                        qsCounter.countOne(); // 纠错计数
                    } else {
                        qsCounter.countZero(); // 未纠错计数
                    }
                }
                if (qs.hasChangeVal()) {
                    // 如果区域未批改且试题有修正，则计入失败数量
                    modifyNum++;
                }
                // 总数量
                totalNum++;
            }
        }
        //
        GradingCorrectDTO result = new GradingCorrectDTO();
        result.setTotalNum(totalNum);
        result.setFixedNum(fixedNum);
        result.setFailNum(failNum);
        result.setModifyNum(modifyNum);
        return result;
    }

    @Override
    public IPage<GradingCorrectDTO> getPage(GradingCorrectDTO param) {
        LambdaQueryWrapper<GradingCorrect> wrapper = Wrappers.lambdaQuery(GradingCorrect.class)
                .orderByDesc(GradingCorrect::getCreateTime);
        return gradingCorrectMapper.selectPage(param.getPage().page(GradingCorrect.class), wrapper)
                .convert(entity -> BeanUtil.toBean(entity, GradingCorrectDTO.class));
    }

    @Override
    public Map<String, Object> getFile(String id) {
        GradingCorrect grading = gradingCorrectMapper.selectById(id);
        Optional.ofNullable(grading).orElseThrow(() -> new BaseException("未查询到数据"));
        List<DocCorrectTask> tempTasks = docCorrectTaskMapper.selectList(Wrappers.lambdaQuery(DocCorrectTask.class)
                .eq(DocCorrectTask::getFileId, grading.getTemplateId())
                .orderByAsc(DocCorrectTask::getName));
        List<DocCorrectConfigDTO> configs = tempTasks.stream().map(task -> {
            DocCorrectConfig config = docCorrectConfigMapper.selectById(task.getConfigId());
            Optional.ofNullable(config).orElseThrow(() -> new BaseException("未查询到试卷配置"));
            return BeanUtil.toBean(config, DocCorrectConfigDTO.class);
        }).collect(Collectors.toList());
        Optional.ofNullable(grading.getQsStats()).orElseThrow(() -> new BaseException("未查询到题目统计数据"));
        JSONObject qsStats = JSONUtil.parseObj(grading.getQsStats());
        Map<String, Object> params = MapUtil.builder(new HashMap<String, Object>())
            .put("grading", grading)
            .put("configs", configs)
            .put("qsStats", qsStats)
            .build();
        return pdfService.createDoc(TexCmd.PDFLATEX, "grading", params);
    }

}
