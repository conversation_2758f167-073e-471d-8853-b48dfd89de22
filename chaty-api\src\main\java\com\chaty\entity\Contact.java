package com.chaty.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("contact")
public class Contact {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    private String name;
    
    private String email;
    
    private String phone;
    
    private String schoolName;
    
    private String message;
    
    private LocalDateTime createTime;
    
    private LocalDateTime updateTime;
} 