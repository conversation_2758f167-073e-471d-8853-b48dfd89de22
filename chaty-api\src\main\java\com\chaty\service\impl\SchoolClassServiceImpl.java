package com.chaty.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.chaty.entity.SchoolClass;
import com.chaty.mapper.ClassMapper;
import com.chaty.service.SchoolClassService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class SchoolClassServiceImpl extends ServiceImpl<ClassMapper, SchoolClass> implements SchoolClassService {

    @Override
    public List<SchoolClass> selectAll() {
        return this.list();
    }

    @Override
    public Integer add(SchoolClass schoolClass) {
        boolean saved = this.save(schoolClass);
        return saved ? 1 : 0;
    }

    @Override
    @Transactional
    public List<SchoolClass> addBatch(List<SchoolClass> schoolClass) {
        this.saveOrUpdateBatch(schoolClass);
        return schoolClass;
    }

    @Override
    public boolean classUpdateById(SchoolClass schoolClass) {
        return this.saveOrUpdate(schoolClass);
    }

    @Override
    public Integer deleteById(String id) {
        boolean removed = this.removeById(id);
        return removed ? 1 : 0;
    }

    @Override
    public Integer deleteByBatch(List<String> ids) {
        boolean removed = this.removeByIds(ids);
        return removed ? ids.size() : 0;
    }

    @Override
    public List<SchoolClass> selectBySchool(String schoolId) {
        QueryWrapper<SchoolClass> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("school_id", schoolId);
        return this.list(queryWrapper);
    }
}

