    \documentclass[15pt]{beamer}
\usepackage{tikz}
\usepackage{ctex}
\usepackage{adjustbox}
\usetheme{Madrid}
\usecolortheme{default}
{% comment %} \setCJKmainfont{AR PL UKai CN} {% endcomment %}
\title[] %optional
{Madrid theme + beaver}
\subtitle{Demonstrating larger fonts}
\author[Generated by SolveGPT] % (optional)
{}

\institute[] % (optional)
{
}

\date[] % (optional)
{}

% Use a simple TikZ graphic to show where the logo is positioned
\logo{}
\begin{document}

<#list notes as note>

<#if note.type == 0>

\begin{frame}[allowframebreaks]{题目}

<#if note.question?has_content>

\begin{block}{题目}

${note.question}

\end{block}

</#if>

<#if note.answer?has_content>

\begin{block}{答案}

${note.answer}

\end{block}

</#if>

<#if note.knowledge?has_content>

\begin{block}{知识点}

${note.knowledge}

\end{block}

</#if>

\end{frame}

</#if>

<#if note.type == 1>

\begin{frame}[allowframebreaks]{知识点}

\begin{block}{知识点}

<#if note.knowledgeName?has_content>

${note.knowledgeName}

</#if>

<#if note.knowledgeContent?has_content>

${note.knowledgeContent}

</#if>

\end{block}

\end{frame}

</#if>

<#if note.type == 2>

\begin{frame}[allowframebreaks]{作业批改}

<#if note.name?has_content>

\begin{block}{名称}

${note.name}

\end{block}

</#if>

<#if note.question?has_content>

\begin{block}{题目}

${note.question}

\end{block}

</#if>

<#if note.correctAnswer?has_content>

\begin{block}{正确答案}

${note.correctAnswer}

\end{block}

</#if>

<#if note.knowledge?has_content>

\begin{block}{知识点}

${note.knowledge}

\end{block}

</#if>

<#if note.answer?has_content>

\begin{block}{学生答案}

${note.answer}

\end{block}

</#if>

\begin{block}{是否正确}

<#if note.isTrue == 1>
正确
<#else>
\textcolor{red}{错误}
</#if>

\end{block}

<#if note.errText?has_content>

\begin{block}{错误}

${note.errText}

\end{block}

</#if>

<#if note.comment?has_content>

\begin{block}{评价}

${note.comment}

\end{block}

</#if>

\end{frame}

</#if>

</#list>

\end{document}
