package com.chaty.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.chaty.entity.User;
import com.chaty.security.AuthUtil;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;


@Component
public class MyMetaObjectHandler implements MetaObjectHandler {
    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(MyMetaObjectHandler.class);
    @Override
    public void insertFill(MetaObject metaObject) {
        Date now = new Date();
        if (metaObject.hasSetter("createTime")) {
            this.strictInsertFill(metaObject, "createTime", Date.class, now);
        }

        if (metaObject.hasSetter("updateTime")) {
            this.strictInsertFill(metaObject, "updateTime", Date.class, now);
        }

        if (metaObject.hasSetter("creator")) {
            try {
                String username = AuthUtil.getLoginUser() != null
                        ? AuthUtil.getLoginUser().getId()
                        : null;
                if (username != null) {
                    this.strictInsertFill(metaObject, "creator", String.class, username);
                }
            } catch (Exception e) {
                log.warn("MyMetaObjectHandler 无法获取当前登录用户，跳过 creator 填充", e);
            }
        }
    }
    @Override
    public void updateFill(MetaObject metaObject) {
        Date now = new Date();

        if (metaObject.hasSetter("updateTime")) {
            this.strictUpdateFill(metaObject, "updateTime", Date.class, now);
        }
    }
}
