package com.chaty.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class FtpMessageTitleDTO {
    
    private Integer id;
    
    @JsonProperty("title")
    private String title;
    
    @JsonProperty("remark")
    private String remark;
    
    @JsonProperty("path")
    private String path;
    
    @JsonProperty("weight")
    private Integer weight;
    
    @JsonProperty("createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    @JsonProperty("updateTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    // 分页参数
    private PageDTO<?> page;
} 