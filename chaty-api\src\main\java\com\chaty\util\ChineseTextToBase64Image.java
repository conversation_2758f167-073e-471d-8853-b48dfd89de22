package com.chaty.util;import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.util.Base64;
import javax.imageio.ImageIO;

public class ChineseTextToBase64Image {
    public static String textToBase64Image(String text) {
        int width = 2480 / 3; // A4 纸宽度（300 DPI 除以 3）
        int height = 3508 / 3; // A4 纸高度（300 DPI 除以 3）

        try {
            // 创建 BufferedImage
            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = image.createGraphics();

            // 开启抗锯齿
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

            // 设置背景色（白色）
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, width, height);

            // **加载手写字体**（本地 TTF 文件）
            File fontFile = new File("/root/service/chaty/hetang.ttf"); // 例：华文行楷（或换成你的手写字体）
            Font customFont = Font.createFont(Font.TRUETYPE_FONT, new FileInputStream(fontFile)).deriveFont(Font.BOLD, 36);

            // 设置字体和颜色
            g2d.setFont(customFont);
            g2d.setColor(Color.BLACK);

            // 获取字体度量信息
            FontMetrics fm = g2d.getFontMetrics();
            int lineHeight = fm.getHeight(); // 每行文本高度
            int maxLineWidth = width - 200; // 最大行宽（留边距）

            // 分割文本为多行
            int x = 100; // 左侧留白
            int y = 150; // 顶部留白
            StringBuilder line = new StringBuilder();
            for (char c : text.toCharArray()) {
                if (fm.stringWidth(line.toString() + c) > maxLineWidth) {
                    g2d.drawString(line.toString(), x, y);
                    y += lineHeight; // 换行
                    line = new StringBuilder();
                }
                line.append(c);
            }
            // 绘制最后一行
            if (line.length() > 0) {
                g2d.drawString(line.toString(), x, y);
            }

            // 释放资源
            g2d.dispose();

            // 将图片转换为 JPEG 格式
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, "JPEG", baos);
            byte[] imageBytes = baos.toByteArray();

            // 转换为 Base64 编码
            return Base64.getEncoder().encodeToString(imageBytes);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) {
        String text = "你好，这是一张使用手写字体的 Base64 图片。\n换行测试：这段文字应该会自动换行，而不是全部挤在一行！";
        String base64Image = textToBase64Image(text);
        System.out.println("Base64 JPEG Image:\n" + base64Image);
    }
}
