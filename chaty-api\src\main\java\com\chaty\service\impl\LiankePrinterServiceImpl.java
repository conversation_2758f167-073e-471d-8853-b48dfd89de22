package com.chaty.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.chaty.api.lianke.*;
import com.chaty.dto.PrinterPropsDTO;
import com.chaty.exception.BaseException;
import com.chaty.service.PrinterService;
import feign.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class LiankePrinterServiceImpl implements PrinterService {

    @Resource
    private LiankeApi liankeApi;

    @Resource
    private OpenLiankeApi openLiankeApi;

    @Resource
    private LiankeConfig liankeConfig;

    @Value("${lianke.enable:false}")
    private Boolean enable;


    @PostConstruct
    private void init() {
        if (enable) {
            List<LiankeConfig.Device> devices =  refreshPinterDevices();
        }
//        devices.forEach(device -> {
//            refreshPinterConfig(device.getDeviceId());
//        });
    }

    @Override
    public String print(PrinterPropsDTO props) {
        log.info("Printing file: {}", props.getFilename());
        // 获取文件
        File file = new File(props.getFilename());
        if (!file.exists()) {
            throw new BaseException("文件打印失败,打印文件不存在!");
        }
        checkAndSetDeviceKey(props);
        // 调用lianke的api进行打印
        PrintJobRequest printJobRequest = new PrintJobRequest();
        BeanUtils.copyProperties(props, printJobRequest);
        printJobRequest.setDeviceId(props.getDevice().getDeviceId());
        printJobRequest.setDeviceKey(props.getDevice().getDeviceKey());
        printJobRequest.setJobFile(file);
        LiankeBaseResponse<PrintJobResponse> resp = liankeApi.printJob(liankeConfig.getApiKey(), printJobRequest);
        if (!resp.isSuccess()) {
            log.error("打印远程文件失败: {}", props.getFilename());
            return null;
        }
        return resp.getData().getTask_id();
    }

    @Override
    public String printByLink(PrinterPropsDTO props) {
        log.info("Printing file url: {}", props.getUrl());
        // 获取文件
        if (StrUtil.isBlank(props.getUrl())) {
            throw new BaseException("文件打印失败,打印文件不存在!");
        }
        checkAndSetDeviceKey(props);
        // 调用lianke的api进行打印
        PrintJobRequest printJobRequest = new PrintJobRequest();
        BeanUtils.copyProperties(props, printJobRequest);
        printJobRequest.setDeviceId(props.getDevice().getDeviceId());
        printJobRequest.setDeviceKey(props.getDevice().getDeviceKey());
        printJobRequest.setJobFile(props.getUrl());
        LiankeBaseResponse<PrintJobResponse> resp = liankeApi.printJob(liankeConfig.getApiKey(), printJobRequest);
        if (!resp.isSuccess()) {
            log.error("打印远程文件失败: {}", resp);
            return null;
        }
        log.info("远程打印文件成功: {}", resp);
        return resp.getData().getTask_id();
    }

    @Override
    public List<LiankeConfig.Device> getDevices() {
        List<LiankeConfig.Device> devices = liankeConfig.getDevices();
        // 删除deviceKey
        List<LiankeConfig.Device> result = new ArrayList<>();
        for (LiankeConfig.Device device : devices) {
            LiankeConfig.Device newDevice = new LiankeConfig.Device();
            newDevice.setDeviceKey("*****");
            newDevice.setDeviceId(device.getDeviceId());
            newDevice.setDeviceName(device.getDeviceName());
            newDevice.setOnline(device.getOnline());
            newDevice.setNeedResetName(device.getNeedResetName());
            newDevice.setParams(device.getParams());
            result.add(newDevice);
        }
        return result;
    }

    @Override
    public void refreshPinterConfig(String deviceId) {
        if (StrUtil.isBlank(deviceId)) {
            throw new BaseException("deviceId不能为空");
        }
        PrinterPropsDTO props = new PrinterPropsDTO();
        LiankeConfig.Device device = new LiankeConfig.Device();
        device.setDeviceId(deviceId);
        props.setDevice(device);
        checkAndSetDeviceKey(props);

        LiankeBaseResponse<JSONObject> printerListRes = liankeApi.printerList(props.getDevice().getDeviceKey(), props.getDevice().getDeviceId(), liankeConfig.getApiKey());
        JSONArray printerList = printerListRes.getData().getJSONArray("row");
        if (printerList.isEmpty()) {
            return;
        }
        JSONObject printer = printerList.getJSONObject(0);
        String drivce_name = printer.getStr("driver_name");

        LiankeBaseResponse<JSONObject> printerParamsRes = liankeApi.printerParams(drivce_name, liankeConfig.getApiKey());
        JSONObject printerParams = printerParamsRes.getData();
        setPinertParams(printerParams, deviceId);
    }

    private void setPinertParams(JSONObject printerParams, String deviceId) {
        List<LiankeConfig.Device> devices = liankeConfig.getDevices();
        for (LiankeConfig.Device device : devices) {
            if (device.getDeviceId().equals(deviceId)) {
                device.setParams(printerParams);
            }
        }
    }

    /**
     * 检查设备id和设备key是否为空
     * 如果为空则使用默认打印机
     * @param props
     */
    public void checkAndSetDeviceKey(PrinterPropsDTO props) {
        if (StrUtil.isBlank(props.getDevice().getDeviceId()) ) {
            // 使用默认打印机
            props.setDevice(liankeConfig.getDevices().get(0));
            return;
        }
        setDeviceKeyByDeviceId(props, liankeConfig.getDevices());
        if (StrUtil.isBlank(props.getDevice().getDeviceKey())) {
            throw new BaseException("打印机信息不完整");
        }
    }

    public void setDeviceKeyByDeviceId(PrinterPropsDTO props, List<LiankeConfig.Device> deviceList) {
        for (LiankeConfig.Device deviceItem : deviceList) {
            if (deviceItem.getDeviceId().equals(props.getDevice().getDeviceId())) {
                props.setDevice(deviceItem);
                break;
            }
        }
    }

    /**
     * 调用lianke平台登录接口，刷新打印机列表
     * @return
     */
    @Override
    public List<LiankeConfig.Device> refreshPinterDevices() {
        Response infoResponse = openLiankeApi.getCSRFToken();
        String cookie = parseCookie(infoResponse);
        String csrftoken = parseCSRFToken(cookie);

        JSONObject requestLogin = new JSONObject();
        requestLogin.set("username", liankeConfig.getUsername());
        requestLogin.set("password", liankeConfig.getPassword());
        Response loginResponse = openLiankeApi.login(JSONUtil.toJsonStr(requestLogin), cookie, csrftoken);
        cookie = parseCookie(loginResponse);
        csrftoken = parseCSRFToken(cookie);
        LiankeBaseResponse<JSONObject> deviceListResponse = openLiankeApi.deviceList(cookie, csrftoken);
        if (!deviceListResponse.isSuccess()) {
            throw new BaseException("获取打印机列表失败");
        }
        JSONArray devices = deviceListResponse.getData().getJSONArray("row");
        List<LiankeConfig.Device> result = new ArrayList<>();
        for (int i = 0; i < devices.size(); i++) {
            JSONObject device = devices.getJSONObject(i);
            LiankeConfig.Device deviceInfo = new LiankeConfig.Device();
            deviceInfo.setDeviceId(device.getStr("id"));
            deviceInfo.setDeviceKey(device.getStr("password"));
            deviceInfo.setDeviceName(device.getStr("remark"));
            deviceInfo.setOnline(device.getInt("online"));
            deviceInfo.setNeedResetName(0);
            result.add(deviceInfo);
        }
        compareName(result, liankeConfig.getDevices());
        liankeConfig.setDevices(result);
        return result;
    }

    /**
     * 比对打印机名称，保留老的打印机名称
     * @param newDevices
     * @param oldDevices
     */
    private void compareName(List<LiankeConfig.Device> newDevices, List<LiankeConfig.Device> oldDevices) {
        for (LiankeConfig.Device newDevice : newDevices) {
            for (LiankeConfig.Device oldDevice : oldDevices) {
                if (newDevice.getDeviceId().equals(oldDevice.getDeviceId())) {
                    newDevice.setDeviceName(oldDevice.getDeviceName());
                }
            }
            if (StrUtil.isBlank(newDevice.getDeviceName())) {
                newDevice.setNeedResetName(1);
            }
        }
    }

    private String parseCookie(Response response) {
        int statusCode = response.status();
        if (statusCode != 200) {
            throw new BaseException("无法登录链科平台");
        }

        Collection<String> cookies = response.headers().get("Set-Cookie");
        if (cookies != null && !cookies.isEmpty()) {
            String cookie = cookies.iterator().next();
            return cookie;
        }

        return "";  // 如果没有 cookies，返回空字符串
    }

    private String parseCSRFToken(String cookie) {
        // 判断 cookie 是否为空
        if (cookie == null || cookie.isEmpty()) {
            return null;
        }

        // 分割 cookie 字符串，获取每个 key=value 对
        String[] cookies = cookie.split(";");

        // 遍历 cookies 数组，查找 csrf_token
        for (String cookiePair : cookies) {
            // 去除前后空格
            cookiePair = cookiePair.trim();

            // 如果包含 "csrf_token="，则提取值
            if (cookiePair.startsWith("csrftoken=")) {
                // 提取 csrf_token 的值
                return cookiePair.substring("csrftoken=".length()).trim();
            }
        }

        // 如果没有找到 csrf_token，返回 null
        return null;
    }

    @Override
    public void changePinterDevicesName(String deviceId, String newName) {
        List<LiankeConfig.Device> devices = liankeConfig.getDevices();
        devices.forEach(device -> {
            if (device.getDeviceId().equals(deviceId)) {
                device.setDeviceName(newName);
                device.setNeedResetName(0);
            }
        });
        liankeConfig.setDevices(devices);
    }

}
