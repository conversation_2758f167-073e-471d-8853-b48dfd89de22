package com.chaty.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chaty.common.BaseResponse;
import com.chaty.entity.FtpMessageUserTitle;
import com.chaty.mapper.FtpMessageUserTitleMapper;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.http.HttpStatus;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/api/ftpMessageUserTitle")
public class FtpMessageUserTitleController {

    @Resource
    private FtpMessageUserTitleMapper ftpMessageUserTitleMapper;

    // ===== Create =====
    @PostMapping("/create")
    public BaseResponse<FtpMessageUserTitle> create(@RequestBody FtpMessageUserTitle body) {
        // 由数据库的 DEFAULT CURRENT_TIMESTAMP & ON UPDATE 自动维护时间
        body.setId(null); // 防止误传 id
        int rows = ftpMessageUserTitleMapper.insert(body);
        if (rows != 1) {
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Insert failed");
        }
        return BaseResponse.ok(ftpMessageUserTitleMapper.selectById(body.getId()));
    }

    // ===== Read - detail =====
    @GetMapping("/detail")
    public BaseResponse<FtpMessageUserTitle> detail(@RequestParam Integer id) {
        FtpMessageUserTitle one = ftpMessageUserTitleMapper.selectById(id);
        if (one == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Record not found");
        }
        return BaseResponse.ok(one);
    }

    // ===== Read - list (带可选筛选 + 分页，POST 传参) =====
    @PostMapping("/list")
    public BaseResponse<Page<FtpMessageUserTitle>> list(
            @RequestParam(required = false) String userId,
            @RequestParam(required = false) Integer titleId,
            @RequestParam(defaultValue = "1") long pageNo,
            @RequestParam(defaultValue = "10") long pageSize
    ) {
        LambdaQueryWrapper<FtpMessageUserTitle> qw = new LambdaQueryWrapper<>();
        qw.eq(userId != null && !userId.isEmpty(), FtpMessageUserTitle::getUserId, userId)
                .eq(titleId != null, FtpMessageUserTitle::getTitleId, titleId)
                .orderByDesc(FtpMessageUserTitle::getUpdateTime)
                .orderByDesc(FtpMessageUserTitle::getId);
        Page<FtpMessageUserTitle> page = new Page<>(pageNo, pageSize);
        return BaseResponse.ok(ftpMessageUserTitleMapper.selectPage(page, qw));
    }

    // ===== Update =====
    @PostMapping("/update")
    public BaseResponse<FtpMessageUserTitle> update(@RequestBody FtpMessageUserTitle body) {
        if (body.getId() == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "id is required");
        }
        // 仅按主键更新非空字段；update_time 由 DB 的 ON UPDATE 自动维护
        int rows = ftpMessageUserTitleMapper.updateById(body);
        if (rows != 1) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Record not found or not changed");
        }
        return BaseResponse.ok(ftpMessageUserTitleMapper.selectById(body.getId()));
    }

    // ===== Delete（单条）=====
    @PostMapping("/delete")
    public BaseResponse<Boolean> delete(@RequestParam Integer id) {
        if (id == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "id is required");
        }
        return BaseResponse.ok(ftpMessageUserTitleMapper.deleteById(id) == 1);
    }

    // ===== Delete（批量）=====
    @PostMapping("/deleteBatch")
    public BaseResponse<Boolean> deleteBatch(@RequestBody List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "ids is required");
        }
        return BaseResponse.ok(ftpMessageUserTitleMapper.deleteBatchIds(ids) == ids.size());
    }
}
