package com.chaty.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.dto.ClassInfoDTO;
import com.chaty.dto.SchoolDTO;
import com.chaty.dto.SchoolUserDTO;
import com.chaty.entity.SchoolUser;

public interface SchoolUserService extends IService<SchoolUser> {

    void add(SchoolUserDTO schoolUser);

    void deleteById(String id);

    IPage<?> findPage(SchoolUserDTO params);

    List<SchoolDTO> getSchoolList(String userId);

    void addSchoolUser(String schoolId, String id);

    List<ClassInfoDTO> selectClassByNickname(String nickname);

}
