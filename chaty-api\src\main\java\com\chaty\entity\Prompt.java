package com.chaty.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class Prompt {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 英文名称 */
    private String englishName;

    /** 描述 */
    private String description;

    /** 名称 */
    private String name;

    /** 是否是大题类型(0/1) —— 建议在DB用 TINYINT(1) */
    private Boolean isMajorType;

    /** 题型 */
    private String questionType;

    /** 类别 */
    private String category;

    /** 提示词内容（TEXT） */
    private String promptContent;

    /** 大题阶段类型 */
    private String majorStageType;

    /** 标签（逗号分隔） */
    private String tags;

    /** 提示词角色（string，可allow-create） */
    private String role;

    /** 是否发生图片 */
    private Boolean hasImage;

    /** ★ 新增：模型请求ID（自定义覆盖）；默认提示词为 NULL */
    private Integer modelRequestId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
