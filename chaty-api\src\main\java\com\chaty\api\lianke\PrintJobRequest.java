package com.chaty.api.lianke;

import feign.form.FormProperty;
import lombok.Data;

import java.io.File;

@Data
public class PrintJobRequest {
    /**
     * 设备ID (必填)
     */
    @FormProperty("deviceId")
    private String deviceId;

    /**
     * 设备密钥 (必填)
     */
    private String deviceKey;

    /**
     * 设备端口：1：USB1 2：USB2 ……
     */
    private String devicePort;

    /**
     * 打印机型号（对应打印机列表接口printer_list的driver_name参数）
     */
    private String printerModel;

    /**
     * 任务文件 (必填) 支持图片、Office文件、pdf、html文件 (formdata 格式)；
     * 支持链接地址，多个链接请用换行符\n拼接(字符串格式)
     */
    private Object jobFile;

    /**
     * 打印纸张尺寸 9：A4 11：A5
     */
    private Integer dmPaperSize = 9;

    /**
     * 打印纸张方向 1：竖向 2：横向
     */
    private Integer dmOrientation;

    /**
     * 打印份数 最大不能超过打印机参数：Capabilities -> Copies
     */
    private Integer dmCopies;

    /**
     * 纸张来源
     */
    private String dmDefaultSource;

    /**
     * 打印颜色 1：黑白 2：彩色
     */
    private Integer dmColor;

    /**
     * 双面打印 1：关闭 2：长边 3：短边
     */
    private Integer dmDuplex;

    /**
     * 打印纸张类型
     */
    private Integer dmMediaType;

    /**
     * 自定义高，dmPaperSize等于0时生效，单位0.1mm
     */
    private Integer dmPaperLength;

    /**
     * 自定义宽，dmPaperSize等于0时生效，单位0.1mm
     */
    private Integer dmPaperWidth;

    /**
     * 打印质量，可选值-1，-2，-3，-4；-1质量最低，-4质量最高
     */
    private String dmPrintQuality;

    /**
     * 预览，默认0，isPreview=1任务结果返回预览图片
     */
    private Boolean isPreview;

    /**
     * HTML转换内核，默认chrometopdf
     * 可选参数：wkhtml, chrometopdf，wkhtmltopdf
     */
    private String htmlKernel;

    /**
     * 如需发送到局域网对应的网口打印机，请填写打印机IP，此时device_port固定填1
     */
    private String targetIp;

    /**
     * 文档页数范围，例如：1,2,3,4,5-10
     */
    private String jpPageRange;

    /**
     * 自动缩放, 参数值范围：4 : 自适应, 0 : 原图打印, 1: 宽度优先, 2: 高度优先, 3 : 拉伸全图, xx% : 自定义百分比
     */
    private String jpAutoScale = "4";

    /**
     * 自动缩放 (取代 jpAutoScale， 优先使用这个参数) fit : 自适应, fitw :宽度优先, fith : 高度优先,
     * fill : 拉伸全图, cover : 自动裁剪, xx% : 自定义百分比, none : 关闭缩放
     */
    private String jpScale;

    /**
     * 自动对齐 <空>：左上，同z1；z1: 左上, z4: 左中, z7: 左下, z2: 中上, z5: 中, z8: 中下, z3: 右上, z6:
     * 右中, z9: 右下
     */
    private String jpAutoAlign;

    /**
     * 默认开启；拦截设备状态异常任务； 5001 设备未配网, 5002 设备已离线
     */
    private Boolean reportDeviceStatus;

    /**
     * 拦截打印机状态异常任务； 5011 该USB接口未插入打印机
     */
    private Boolean reportPrinterStatus;

    /**
     * 打印结果回调，必须为https链接
     */
    private String callbackUrl;

    /**
     * 预览返回的任务凭证
     */
    private String taskTicket;

    /**
     * 允许连续出错的任务数最大值，默认为30
     */
    private Integer errLimitNum;

    /**
     * 文档逆序功能，仅对文档类型文件有效
     */
    private Boolean pdfRev;

    /**
     * 自动旋转功能，建议打印文档时才打开
     */
    private Boolean jpAutoRotate;

}
