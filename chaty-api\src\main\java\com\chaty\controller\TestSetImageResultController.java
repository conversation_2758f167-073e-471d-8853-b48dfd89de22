package com.chaty.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.common.BaseResponse;
import com.chaty.entity.TestSetImageResultEntity;
import com.chaty.enums.TestSetResultStatus;
import com.chaty.service.TestSetImageResultService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/** 测试集图片批改结果 CRUD（仅DB） */
@RestController
@RequestMapping("/api/testsetImageResult")
public class TestSetImageResultController {

    @Resource
    private TestSetImageResultService service;

    /** 分页：支持 testSetId / imageId / questionType / status / success 过滤 */
    @GetMapping("/page")
    public BaseResponse<IPage<TestSetImageResultEntity>> page(
            @RequestParam(required = false) Integer pageNumber,
            @RequestParam(required = false) Integer pageSize,
            @RequestParam(required = false) Long testSetId,
            @RequestParam(required = false) Long imageId,
            @RequestParam(required = false) String questionType,
            @RequestParam(required = false) TestSetResultStatus status,
            @RequestParam(required = false) Boolean success
    ) {
        return BaseResponse.ok(
                service.page(pageNumber, pageSize, testSetId, imageId, questionType, status, success)
        );
    }

    /** 详情 */
    @GetMapping("/{id}")
    public BaseResponse<TestSetImageResultEntity> get(@PathVariable Long id) {
        return BaseResponse.ok(service.getById(id));
    }

    /** 新增（自动补齐图片与模型参数快照） */
    @PostMapping("/add")
    public BaseResponse<Long> add(@RequestBody TestSetImageResultEntity param) {
        return BaseResponse.ok(service.add(param));
    }

    /** 更新（不主动覆盖快照） */
    @PostMapping("/update")
    public BaseResponse<Long> update(@RequestBody TestSetImageResultEntity param) {
        return BaseResponse.ok(service.updateOne(param));
    }

    /** 删除 */
    @DeleteMapping("/{id}")
    public BaseResponse<?> delete(@PathVariable Long id) {
        service.deleteById(id);
        return BaseResponse.ok("删除成功");
    }
}
