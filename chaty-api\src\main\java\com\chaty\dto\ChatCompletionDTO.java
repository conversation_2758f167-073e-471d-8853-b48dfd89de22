package com.chaty.dto;

import java.util.List;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.volcengine.ark.runtime.model.completion.chat.ChatTool;
import lombok.Data;

@Data
public class ChatCompletionDTO {

    private String model;

    private List<MessageDTO> messages;

    private List<FunctionDTO> functions;

    private JSONObject responseFormat;

    private Float temperature;

    private Float topp;

    private Float topk;

    private Float penaltyScore;

    private Boolean stream = false;

    private String userid;

    private Integer maxTokens;

    private Boolean enableSearch;

    private Boolean logprobs;

    private Integer seed;

    private List<ChatTool> tools;

    private JSONObject modelRequestObj;

     @JsonProperty("jsonobject")
     private Boolean jsonObject = false;
     @JsonProperty("jsonschema")
     private Boolean jsonSchema = false;

//    //新增字段，增加function calling开关标识
//    private Boolean useFunctionCalling = false;
}
