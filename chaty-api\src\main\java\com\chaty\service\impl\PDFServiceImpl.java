package com.chaty.service.impl;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import cn.hutool.core.util.IdUtil;
import com.aspose.words.Document;
import com.aspose.words.SaveFormat;
import com.chaty.dto.EssayWordDTO;
import com.chaty.util.WordUtil;
import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.html2pdf.resolver.font.DefaultFontProvider;
import com.itextpdf.layout.font.FontProvider;
import freemarker.cache.FileTemplateLoader;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.commonmark.Extension;
import org.commonmark.ext.gfm.tables.TablesExtension;
import org.commonmark.node.Node;
import org.commonmark.parser.Parser;
import org.commonmark.renderer.html.HtmlRenderer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import com.chaty.exception.BaseException;
import com.chaty.service.PDFService;

import cn.hutool.core.map.MapUtil;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;

import static com.chaty.util.HtmlTemplateProcessor.convertHtmlToPdf;
import static com.chaty.util.HtmlTemplateProcessor.processHtmlTemplate;
import static com.chaty.util.WordUtil.getLicense;

@Slf4j
@Service
public class PDFServiceImpl implements PDFService {

    @Value("${file.tex.path}")
    private String texPath;
    @Value("${file.local.ctxpath}")
    private String fileCtxPath;
    @Value("${file.local.path}")
    private String filePath;
    @Value("${file.tex.envPath:/tmp}")
    private String texEnvPath;

    private Map<String, Object> properties;

    @Resource
    private Configuration freemakerConfig;

    @PostConstruct
    public void init() {
        properties = new HashMap<String, Object>();
        properties.put("texPath", texPath);
        properties.put("fileCtxPath", fileCtxPath);
        properties.put("filePath", filePath);
        properties.put("texEnvPath", texEnvPath);
    }

    private Map<String, String> REVIEW_VERS = MapUtil
            .builder("v1", "review.tex")
            .put("v2", "review_v2.tex")
            .build();

    @Override
    public Map<String, Object> createPDF(Map<String, Object> params) {
        try {
            String templateName = Optional.ofNullable(params.get("version")).map(v -> REVIEW_VERS.get(v))
                    .orElse("review_2.tex");
            Template template = freemakerConfig.getTemplate(templateName);
            String content = FreeMarkerTemplateUtils.processTemplateIntoString(template, params);
            log.debug("review content: {}", content);
            String fileUrl = writePDF(content, properties);
            return Collections.singletonMap("fileUrl", fileUrl);
        } catch (Exception e) {
            log.error("生成PDF失败", e);
            throw new BaseException("PDF文件生成失败!", e);
        }
    }

    private Map<String, String> SOLVEALL_VERS = MapUtil
            .builder("v1", "solveall.tex")
            .put("v2", "solveall_v2.tex")
            .build();

    @Override
    public Map<String, Object> solveAllPDF(Map<String, Object> params) {
        try {
            // params.put("accuracy", RandomUtil.randomInt(90, 100));
            String templateName = Optional.ofNullable(params.get("version")).map(v -> SOLVEALL_VERS.get(v))
                    .orElse("solveall_v2.tex");
            Template template = freemakerConfig.getTemplate(templateName);
            String content = FreeMarkerTemplateUtils.processTemplateIntoString(template, params);
            log.debug("solveall content: {}", content);
            String fileUrl = writePDF(content, properties);
            return Collections.singletonMap("fileUrl", fileUrl);
        } catch (Exception e) {
            log.error("生成PDF失败", e);
            throw new BaseException("生成PDF失败!", e);
        }
    }

    private Map<String, String> COURSENOTE_VERS = MapUtil
            .builder("v1", "coursenote.tex")
            .put("v2", "coursenote_v2.tex")
            .build();

    @Override
    public Map<String, Object> coursenotePDF(Map<String, Object> params) {
        try {
            String templateName = Optional.ofNullable(params.get("version")).map(v -> COURSENOTE_VERS.get(v))
                    .orElse("coursenote_v2.tex");
            Template template = freemakerConfig.getTemplate(templateName);
            String content = FreeMarkerTemplateUtils.processTemplateIntoString(template, params);
            log.debug("coursenote content: {}", content);
            String fileUrl = writePDF(content, properties);
            return Collections.singletonMap("fileUrl", fileUrl);
        } catch (Exception e) {
            log.error("生成PDF失败", e);
            throw new BaseException("生成PDF失败!", e);
        }
    }

    private final String DOC_REVIEW_TEX = "docreview.tex";

    @Override
    public Map<String, Object> docReviewPDF(Map<String, Object> params) {
        try {
            Template template = freemakerConfig.getTemplate(DOC_REVIEW_TEX);
            String content = FreeMarkerTemplateUtils.processTemplateIntoString(template, params);
            log.debug("doc review content: {}", content);
            String fileUrl = writePDF(TexCmd.PDFLATEX, content, properties);
            return Collections.singletonMap("fileUrl", fileUrl);
        } catch (Exception e) {
            log.error("生成PDF失败", e);
            throw new BaseException("生成PDF失败!", e);
        }
    }

    private final String REVIEW_STATS_TEX = "reviewstats_v0125.tex";

    @Override
    public Map<String, Object> reviewStatsPDF(Map<String, Object> params) {
        try {
            Template template = freemakerConfig.getTemplate(REVIEW_STATS_TEX);
            String content = FreeMarkerTemplateUtils.processTemplateIntoString(template, params);
            log.debug("doc review content: {}", content);
            String fileUrl = writePDF(content, properties);
            return Collections.singletonMap("fileUrl", fileUrl);
        } catch (Exception e) {
            log.error("生成PDF失败", e);
            throw new BaseException("生成PDF失败!", e);
        }
    }

    @Override
    public Map<String, Object> createDoc(String texCmd, String texTemplate, Map<String, Object> params) {
        try {
//            Configuration configuration = new Configuration(Configuration.VERSION_2_3_32);
//            configuration.setTemplateLoader(new FileTemplateLoader(new File("D:/chaty/files")));
//            Template template = configuration.getTemplate(String.format("%s.tex", texTemplate));
            Template template = freemakerConfig.getTemplate(String.format("%s.tex", texTemplate));

            String content = FreeMarkerTemplateUtils.processTemplateIntoString(template, params);
            log.debug("doc review content: {}", content);
//            String fileUrl = writePDF("D:\\pdfLatex\\miktex\\bin\\x64\\pdflatex", content, properties);
//            String fileUrl = writePDF("D:\\latex\\miktex\\bin\\x64\\pdflatex", content, properties);
            String fileUrl = writePDF(texCmd, content, properties);
            return MapUtil.<String, Object>builder("fileUrl", fileUrl).build();
        } catch (Exception e) {
            log.error("生成PDF失败", e);
            throw new BaseException("生成PDF失败!", e);
        }
    }

    @Override
    public Map<String, Object> createEssayReport(List<EssayWordDTO> params) {
        if (params.isEmpty()) {
            log.error("生成PDF失败，参数为空");
        }
        try {
            List<String> paths = new ArrayList<>();
            params.forEach((param) -> {
                String pdfOutPath = filePath + File.separator + IdUtil.fastSimpleUUID() + ".pdf";
//                String htmlOutPath = filePath + File.separator + IdUtil.fastSimpleUUID() + ".html";
                // 处理HTML模板
//                processHtmlTemplate("essayReport.html", param, htmlOutPath);
                // 将HTML文件转换为PDF
//                convertHtmlToPdf(htmlOutPath, pdfOutPath);
                String wordOutPath = filePath + File.separator + IdUtil.fastSimpleUUID() + ".docx";
                Boolean isEnglishEssay = params.get(0).getIsEnglishEssay();
                WordUtil.processWordTemplateIntoPdf(isEnglishEssay ? "essay.docx" : "chineseEssay.docx", param, wordOutPath, pdfOutPath);
                paths.add(pdfOutPath);
            });
            String filename = IdUtil.fastSimpleUUID() + ".pdf";
            String pdfPath = filePath + File.separator + filename;
            writeWords2PDF(paths, pdfPath);
            String staticPath = "/static/" + filename;
            return MapUtil.<String, Object>builder("fileUrl", staticPath).build();
        } catch (Exception e) {
            log.error("生成PDF失败", e);
            throw new BaseException("生成PDF失败!", e);
        }
    }

    @Override
    public Map<String, Object> createEssayAnalyticalReport(String content) {
        try {
            String uuid = IdUtil.fastSimpleUUID();
            String path = filePath + File.separator + uuid;
            String mdOutPath = path + ".md";
            String pdfOutPath = path + ".pdf";

            File mdFile = new File(mdOutPath);
            File parentDir = mdFile.getParentFile();
            if (!parentDir.exists()) {
                parentDir.mkdirs();
            }
            try (FileWriter writer = new FileWriter(mdFile)) {
                writer.write(content);
            } catch (IOException e) {
                log.error("写入Markdown文件失败", e);
                throw new BaseException("写入Markdown文件失败!", e);
            }

            convertMarkdownToPdf(content, pdfOutPath);

            Map<String, Object> properties = new HashMap<>();
            properties.put("mdOutPath", "/static/" + uuid + ".md");
            properties.put("pdfOutPath", "/static/" + uuid + ".pdf");
            return properties;
        } catch (Exception e) {
            log.error("生成PDF失败", e);
            throw new BaseException("生成PDF失败!", e);
        }
    }

    /**
     * 将 Markdown 文件转换为 PDF 文件，支持中文字符。
     *
     * @param markdownText  Markdown 文件路径
     * @param pdfOutputPath 输出的 PDF 文件路径
     */
    public static void convertMarkdownToPdf(String markdownText, String pdfOutputPath) throws Exception {
        List<Extension> extensions = new ArrayList<>();
        extensions.add(TablesExtension.create());
        Parser parser = Parser.builder().extensions(extensions).build();
        HtmlRenderer renderer = HtmlRenderer.builder().extensions(extensions).build();
        String html = renderer.render(parser.parse(markdownText));

        String css = "<style>" +
                "table {" +
                "    width: 100%;" +
                "    border-collapse: collapse;" + /* 合并边框 */
                "}" +
                "th, td {" +
                "    border: 1px solid black;" + /* 定义单元格边框 */
                "    padding: 8px;" + /* 内边距 */
                "    text-align: left;" + /* 文本对齐方式 */
                "}" +
                "th {" +
                "    background-color: #f2f2f2;" + /* 表头背景色 */
                "}" +
                "code {" +
                "    background-color: #f9f2f4;" + /* 代码背景色 */
                "    padding: 2px 4px;" +
                "    border-radius: 4px;" +
                "}" +
                "</style>";

        // 构建完整的 HTML 文档
        String completeHtml = "<!DOCTYPE html>\n" +
                "<html>\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <title>Markdown 转 PDF</title>\n" +
                css + /* 添加 CSS 样式 */
                "</head>\n" +
                "<body>\n" +
                html + /* 插入生成的 HTML 内容 */
                "</body>\n" +
                "</html>";

        // 输出 PDF 文件
        File outputFile = new File(pdfOutputPath);
        try (FileOutputStream outputStream = new FileOutputStream(outputFile)) {
            ConverterProperties converterProperties = new ConverterProperties();
            FontProvider fontProvider = new DefaultFontProvider(true, true, true);
            converterProperties.setFontProvider(fontProvider);
            HtmlConverter.convertToPdf(completeHtml, outputStream, converterProperties);
        } catch (IOException e) {
            e.printStackTrace();
            throw new Exception("Markdown 转 PDF 失败: " + e.getMessage(), e);
        }
    }

    public static void main(String[] args) throws Exception {
        // 定义数组大小
//        convertMarkdownToPdf("C:\\Users\\<USER>\\Downloads\\file2(页1) 统计结果.md", "C:\\Users\\<USER>\\Desktop\\test2.pdf");

        String markdown = "## Amost always imports\n | Command | Description |\n" +
                "| --- | --- |\n" +
                "| `git status` | List all *new or modified* files |\n" +
                "| `git diff` | Show file differences that **haven't been** staged |";

        List<Extension> extensions = new ArrayList<>();
        extensions.add(TablesExtension.create());
        Parser parser = Parser.builder().extensions(extensions).build();
        HtmlRenderer renderer = HtmlRenderer.builder().extensions(extensions).build();

        // 将 Markdown 转换为 HTML
        String html = renderer.render(parser.parse(markdown));
        System.out.println(html);  // 查看转换结果
    }

    /**
     * 将多个 pdf 文件合并并转换为单个 PDF 文件。
     *
     * @param paths   pdf 文件路径列表
     * @param outPath 输出的 PDF 文件路径
     */
    @Override
    public void writeWords2PDF(List<String> paths, String outPath) {
        PDFMergerUtility merger = new PDFMergerUtility();
        try {
            // Add all source PDF files
            for (String path : paths) {
                File pdfFile = new File(path);
                if (!pdfFile.exists()) {
                    log.warn("PDF file not found: {}", path);
                    continue; // Skip missing files
                }
                merger.addSource(pdfFile);
            }

            // Set the destination PDF file
            merger.setDestinationFileName(outPath);

            // Merge documents
            merger.mergeDocuments(MemoryUsageSetting.setupTempFileOnly());

            log.info("Successfully merged PDFs into: {}", outPath);
        } catch (IOException e) {
            log.error("Error merging PDF files", e);
            throw new BaseException("合并PDF文件失败!", e);
        } finally {
            // Optionally, delete the individual PDF files after merging
            for (String path : paths) {
                try {
                    Files.deleteIfExists(Paths.get(path));
                    log.debug("Deleted temporary PDF file: {}", path);
                } catch (IOException e) {
                    log.warn("Failed to delete temporary PDF file: {}", path, e);
                }
            }
        }
    }
}
