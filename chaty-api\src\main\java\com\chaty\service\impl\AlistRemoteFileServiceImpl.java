package com.chaty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chaty.api.alist.AlistApi;
import com.chaty.api.alist.AlistBaseResponse;
import com.chaty.api.alist.FsListRequest;
import com.chaty.api.alist.FsListResponse;
import com.chaty.dto.RemoteFileDTO;
import com.chaty.dto.RemoteFileWithRemarkDTO;
import com.chaty.dto.SaveRemoteFileDTO;
import com.chaty.entity.FtpFiles;
import com.chaty.exception.BaseException;
import com.chaty.mapper.FtpFilesMapper;
import com.chaty.service.RemoteFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AlistRemoteFileServiceImpl implements RemoteFileService {

    @Resource
    private AlistApi alistApi;
    @Resource
    private TaskExecutor taskExecutor;
    @Resource
    private FtpFilesMapper ftpFilesMapper;

    @Value("${alist.list-path}")
    private String alistListPath;
    @Value("${alist.out-path}")
    private String alistOutPath;


    @Override
    public List<RemoteFileDTO> list(String path, String type) {
        FsListRequest request = new FsListRequest();
        if (type.equals("in")) {
            request.setPath(alistListPath + Optional.ofNullable(path).orElse(""));
        } else if (type.equals("out")) {
            request.setPath(alistOutPath + Optional.ofNullable(path).orElse(""));
        } else {
            request.setPath(Optional.ofNullable(path).orElse(""));
        }
        request.setRefresh(true);
        AlistBaseResponse<FsListResponse> resp = alistApi.fsList(request);
        FsListResponse data = resp.getData();

        if (Objects.isNull(data.getContent())) {
            return Collections.emptyList();
        }
        return data.getContent().stream()
                .map(content -> BeanUtil.copyProperties(content, RemoteFileDTO.class))
                .collect(Collectors.toList());
    }

    @Override
    public void upload(String path, File file) {
        log.info("alist upload file: {} {}", path, file.getName());
        if (!path.startsWith(("/"))) {
            path = "/" + path;
        }
        AlistBaseResponse<?> alistBaseResponse = alistApi.fsForm(path, false, file);
        log.info("alist upload response: {}", alistBaseResponse);
    }

    @Override
    public void save2Remote(SaveRemoteFileDTO params) {
        log.info("alist save2Remote: {}", params);

        String savePath = StrUtil.isEmpty(params.getSaveFileName()) ? params.getFilename() : params.getSaveFileName();
        if (!StrUtil.isEmpty(params.getPath())) {
            savePath = String.format("%s/%s", params.getPath(), savePath);
        }

        File file = new File(params.getFilename());
        if (!file.exists()) {
            throw new BaseException("文件上传失败，上传文件不存在！");
        }

        if (savePath.startsWith("/")) {
            savePath = savePath.substring(1);
        }

        String finalSavePath = savePath;
        int maxRetries = 5;
        final long[] retryDelayMillis = {2000};

        taskExecutor.execute(() -> {
            int attempt = 0;
            boolean success = false;

            while (attempt < maxRetries && !success) {
                try {
                    log.info("第 {} 次尝试上传文件到远程路径: {}", attempt + 1, finalSavePath);
                    upload(finalSavePath, file);

                    // 检查是否上传成功
                    List<RemoteFileDTO> remoteFiles = list(finalSavePath.substring(0, finalSavePath.lastIndexOf("/")), "in");
                    Optional<RemoteFileDTO> matchedFile = remoteFiles.stream()
                            .filter(f -> f.getName().equals(file.getName()) && f.getSize() > 0)
                            .findFirst();

                    if (matchedFile.isPresent()) {
                        log.info("文件上传成功: {}", finalSavePath);
                        success = true;
                    } else {
                        throw new RuntimeException("远程未检测到上传文件");
                    }
                } catch (Exception e) {
                    log.error("第 {} 次上传失败: {}", attempt + 1, e.getMessage());
                    attempt++;
                    retryDelayMillis[0] = retryDelayMillis[0] * 2;
                    if (attempt < maxRetries) {
                        try {
                            Thread.sleep(retryDelayMillis[0]);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            log.error("上传重试被中断");
                            return;
                        }
                    } else {
                        log.error("多次上传失败，终止操作: {}", finalSavePath);
                        throw new BaseException(params.getSaveFileName() + " 文件多次重试依旧上传失败！！");
                    }
                }
            }
        });
    }


    @Override
    public boolean createFolder(String path, String folderName) {
        String fullPath = path + File.separator + folderName;
        try {
            // Assuming `AlistApi` has a method for creating a folder, such as `createFolder`
            Map<String, String> req = new HashMap<>();
            req.put("path", fullPath);
            AlistBaseResponse<?> response = alistApi.createFolder(req);
            if (response.getCode() == 200) {
                log.info("Successfully created folder: {}", fullPath);
                return true;
            } else {
                log.error("Failed to create folder: {}. Error: {}", fullPath, response.getMessage());
                return false;
            }
        } catch (Exception e) {
            log.error("Error creating folder: {}. Exception: {}", fullPath, e.getMessage());
            return false;
        }
    }

    @Override
    public List<RemoteFileWithRemarkDTO> listWithRemark(String path, String type) {
        List<RemoteFileDTO> originalList = list(path, type);

        // 收集所有需要查询的文件信息
        List<String> filenames = new ArrayList<>();

        for (RemoteFileDTO file : originalList) {
            filenames.add(file.getName());
        }

        // 构建批量查询条件
        LambdaQueryWrapper<FtpFiles> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(!filenames.isEmpty(), FtpFiles::getFilename, filenames);
        wrapper.eq(path != null, FtpFiles::getPath, path);

        // 如果所有条件都为空，直接返回原始列表的转换结果
        if (filenames.isEmpty() && path == null) {
            return originalList.stream()
                    .map(file -> {
                        RemoteFileWithRemarkDTO dto = new RemoteFileWithRemarkDTO();
                        BeanUtil.copyProperties(file, dto);
                        return dto;
                    })
                    .collect(Collectors.toList());
        }

        // 执行数据库查询
        List<FtpFiles> ftpFiles = ftpFilesMapper.selectList(wrapper);

        // 创建文件名到FtpFiles的映射，用于快速查找
        Map<String, FtpFiles> ftpFilesMap = ftpFiles.stream()
                .collect(Collectors.toMap(
                        FtpFiles::getFilename,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));

        // 转换结果
        List<RemoteFileWithRemarkDTO> resultList = originalList.stream()
                .map(file -> {
                    RemoteFileWithRemarkDTO dto = new RemoteFileWithRemarkDTO();
                    BeanUtil.copyProperties(file, dto);

                    FtpFiles ftpFile = ftpFilesMap.get(file.getName());
                    if (ftpFile != null) {
                        try {
                            dto.setRemark(ftpFile.getRemark());
                            dto.setFileId(ftpFile.getFileId());
                            dto.setConfigPackageId(ftpFile.getConfigPackageId());
                            dto.setFileType(ftpFile.getType());
                            dto.setId(ftpFile.getId());
                        } catch (Exception e) {
                            log.error("Error comparing modified dates for file: {}", file.getName(), e);
                        }
                    } else {
                        log.info("No matching ftp_files record found for {}", file.getName());
                    }

                    return dto;
                })
                .collect(Collectors.toList());

        return resultList;
    }
}
