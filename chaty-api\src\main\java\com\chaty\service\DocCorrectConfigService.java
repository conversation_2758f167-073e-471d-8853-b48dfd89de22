package com.chaty.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.dto.DocCorrectConfigDTO;
import com.chaty.entity.DocCorrectConfig;

public interface DocCorrectConfigService extends IService<DocCorrectConfig> {

    IPage<DocCorrectConfig> page(DocCorrectConfigDTO param);

    String add(DocCorrectConfigDTO param);

    String update(DocCorrectConfigDTO param);

    void delete(String id);

    DocCorrectConfig getById(String id);

}
