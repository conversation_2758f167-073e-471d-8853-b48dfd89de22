package com.chaty.dto;

import lombok.Data;

import java.util.Date;

@Data
public class ComprehensiveResultDTO {
    private String id;
    private String name;
    private String url;
    private Integer status;
    private Integer isEssay;
    private String creator;
    private String config;
    private Date createTime;
    private Date updateTime;
    private Integer type;

    private Integer isCorrectFinish;

    private String remark;

    private String configIds;

    private String classId;

    private String className;

    private String schoolName;

    private Date lastCorrectTime;

    private String configPackageId;

    private Integer recordSize;

    private Integer modelRequestId;
}

