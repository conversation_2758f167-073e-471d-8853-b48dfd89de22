package com.chaty.service.impl;

import com.chaty.exception.BaseException;
import com.chaty.exception.RetryException;
import com.chaty.service.TencentEssayScoreService;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.ecc.v20181213.EccClient;
import com.tencentcloudapi.ecc.v20181213.models.CorrectData;
import com.tencentcloudapi.ecc.v20181213.models.ECCRequest;
import com.tencentcloudapi.ecc.v20181213.models.ECCResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

@Slf4j
@Primary
@Service("TencentEssayScoreService")
public class TencentEssayScoreServiceImpl implements TencentEssayScoreService {
    @Value("${api.tencentcloud.ecc.endpoint:ecc.tencentcloudapi.com}")
    public String endpoint;
    @Value("${api.tencentcloud.ecc.secretId}")
    public String secretid;
    @Value("${api.tencentcloud.ecc.secretKey}")
    public String secretKey;


    private HttpProfile httpProfile;

    @PostConstruct
    public void postConstruct() {
        log.info("tencent cloud api configuration: \n endpoint: {} \n secretid: {} \n secretKey: {} \n region: {} \n",
                endpoint, secretid, secretKey, "");
    }


    private EccClient createEccClient() {
        Credential cred = new Credential(secretid, secretKey);
        httpProfile = new HttpProfile();
        httpProfile.setEndpoint(endpoint);
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        return new EccClient(cred, "", clientProfile);
    }

    @Override
    @Retryable(value = RetryException.class, backoff = @Backoff(delay = 1000))
    public CorrectData correctEssay(ECCRequest req) {
        try {
            EccClient client = createEccClient();
            ECCResponse resp = client.ECC(req);
            // 返回批改结果
            if (resp != null && resp.getData() != null) {
                return resp.getData();  // 返回批改的详细信息
            } else {
                log.warn("No correction data returned for essay: {}", resp);
                return null;
            }
        } catch (TencentCloudSDKException e) {
            log.error("Error calling Tencent Cloud Essay Correction API: {}", e.getMessage());
            throw new BaseException("作文批改失败", e);
        }
    }
}
