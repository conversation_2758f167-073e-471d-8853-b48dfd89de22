package com.chaty.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aliyun.ocr_api20210707.models.*;
import com.chaty.task.correct.SemaphoreManager;
import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import com.aliyun.teaopenapi.models.Config;
import com.chaty.exception.BaseException;
import com.chaty.service.OCRService;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;

@Slf4j
@Primary
@Service("aliOcrService")
public class AliOcrServiceImpl implements OCRService {

    @Value("${api.aliyun.accessKeyId}")
    private String accessKeyId;

    @Value("${api.aliyun.accessKeySecret}")
    private String accessKeySecret;

    private com.aliyun.ocr_api20210707.Client client;

    @PostConstruct
    public void postConstruct() {
        try {
            Config config = new Config()
                    .setAccessKeyId(accessKeyId)
                    .setAccessKeySecret(accessKeySecret)
                    .setEndpoint("ocr-api.cn-hangzhou.aliyuncs.com");
            client = new com.aliyun.ocr_api20210707.Client(config);
            log.info("Aliyun OCR API initialized successfully.");
        } catch (Exception e) {
            log.error("Failed to initialize Aliyun OCR API: {}", e.getMessage());
            throw new BaseException("阿里云 OCR 初始化失败", e);
        }
    }

    @Override
    public String ocrForText(String url) {
        SemaphoreManager manager = SemaphoreManager.getInstance();
        RateLimiter rateLimiter = manager.getAliHandNormalLimiter();
        try {
            RecognizeAdvancedRequest request = new RecognizeAdvancedRequest().setUrl(url);
            rateLimiter.acquire();
            RecognizeAdvancedResponse response = client.recognizeAdvanced(request);
            return parseOcrResponse(response);
        } catch (Exception e) {
            log.error("Aliyun OCR API error: {}", e.getMessage());
            throw new BaseException("OCR 识别失败", e);
        }
    }

    @Override
    public String ocrForHandwritingText(String base64OrUrl) throws InterruptedException {
        SemaphoreManager manager = SemaphoreManager.getInstance();
        RateLimiter rateLimiter = manager.getAliHandWrittenrateLimiter();
        try {
            com.aliyun.ocr_api20210707.models.RecognizeHandwritingRequest request = new RecognizeHandwritingRequest();
            if (base64OrUrl.startsWith("http")) {
                // 传入 URL
                request.setUrl(base64OrUrl);
            } else {
                // 传入 Base64 进行解码
                byte[] imageData = Base64.getDecoder().decode(
                        base64OrUrl.replaceFirst("^data:image/\\w+;base64,", "")
                );
                InputStream imageStream = new ByteArrayInputStream(imageData);
                request.setBody(imageStream);
            }
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            rateLimiter.acquire();
            RecognizeHandwritingResponse response = client.recognizeHandwritingWithOptions(request, runtime);
            return parseOcrResponse(response);
        } catch (Exception e) {
            log.error("Aliyun Handwriting OCR API error: {}", e.getMessage());
            throw new BaseException("手写体 OCR 识别失败", e);
        }
    }

    @Override
    public String ocrForArithmetic(String base64) {
        return null;
    }

    private String parseOcrResponse(RecognizeAdvancedResponse response) {
        String result = null;
        if (response.getBody() != null && response.getBody().data != null) {
            try {
                JSONObject data = JSONUtil.parseObj( response.getBody().getData().getBytes(StandardCharsets.UTF_8) );
                result = data.getStr("content");
            } catch (Exception e) {
                log.error("Aliyun OCR API error: {}", e.getMessage());
            }
        }
        return result;
    }

    private String parseOcrResponse(RecognizeHandwritingResponse response) {
        String result = null;
        if (response.getBody() != null && response.getBody().data != null) {
            try {
                JSONObject data = JSONUtil.parseObj( response.getBody().getData().getBytes(StandardCharsets.UTF_8) );
                result = data.getStr("content");
            } catch (Exception e) {
                log.error("Aliyun OCR API error: {}", e.getMessage());
            }
        }
        return result;
    }
}
