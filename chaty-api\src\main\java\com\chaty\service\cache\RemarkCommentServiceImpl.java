package com.chaty.service.cache;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.entity.RemarkComment;
import com.chaty.mapper.RemarkCommentMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.security.SecureRandom;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RemarkCommentServiceImpl
        extends ServiceImpl<RemarkCommentMapper, RemarkComment>
        implements RemarkCommentService {

    /** Hash：RCM:ALL  field=id  value=JSON(RemarkComment) */
    private static final String KEY_ALL = "RCM:ALL";

    @Resource
    private RemarkCommentMapper mapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ObjectMapper objectMapper;

    private final SecureRandom random = new SecureRandom();

    // ===================== 启动预热 =====================
    @PostConstruct
    public void initCache() {
        try {
            rebuildAllCaches();
            log.info("[RemarkComment] Cache warmed on startup.");
        } catch (Exception e) {
            log.warn("[RemarkComment] Cache warm-up failed: {}", e.getMessage());
        }
    }

    @Override
    public void rebuildAllCaches() {
        try {
            stringRedisTemplate.delete(KEY_ALL);
        } catch (Exception ignore) {}

        List<RemarkComment> all = this.list();
        if (all == null || all.isEmpty()) return;

        Map<String, String> map = new HashMap<>();
        for (RemarkComment rc : all) {
            try {
                map.put(String.valueOf(rc.getId()), objectMapper.writeValueAsString(rc));
            } catch (Exception ignore) {}
        }
        if (!map.isEmpty()) {
            try {
                stringRedisTemplate.opsForHash().putAll(KEY_ALL, map);
            } catch (DataAccessException ex) {
                log.warn("Redis 预热失败: {}", ex.getMessage());
            }
        }
    }

    // ===================== Page（走 DB） =====================
    @Override
    public IPage<RemarkComment> page(Integer pageNumber, Integer pageSize, String keyword) {
        Page<RemarkComment> page = new Page<>(
                pageNumber == null ? 1 : pageNumber,
                pageSize == null ? 10 : pageSize
        );

        LambdaQueryWrapper<RemarkComment> qw = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(keyword)) {
            qw.like(RemarkComment::getContent, keyword);
        }
        qw.orderByDesc(RemarkComment::getCreateTime);
        return this.page(page, qw);
    }

    // ===================== 写操作：DB + Cache =====================
    @Override
    public Long add(RemarkComment param) {
        if (param.getHitProb() == null || param.getHitProb() < 0) {
            param.setHitProb(0);
        }
        this.save(param);
        RemarkComment db = this.getById(param.getId());
        putCache(db);
        return param.getId();
    }

    @Override
    public Long updateOne(RemarkComment param) {
        if (param.getHitProb() != null && param.getHitProb() < 0) {
            param.setHitProb(0);
        }
        this.updateById(param);
        RemarkComment db = this.getById(param.getId());
        putCache(db);
        return param.getId();
    }

    @Override
    public void deleteById(Long id) {
        if (id == null) return;
        RemarkComment old = this.getById(id);
        this.removeById(id);
        if (old != null) {
            evictCache(old.getId());
        }
    }

    // ===================== 加权随机 =====================
    @Override
    public RemarkComment randomOne() {
        List<RemarkComment> list = getAllFromCache();
        if (list == null || list.isEmpty()) {
            // 兜底：回源 DB 并重建缓存
            list = this.list();
            if (list == null || list.isEmpty()) return null;
            rebuildAllCaches();
        }

        // 过滤命中概率 <= 0 的项
        List<RemarkComment> candidates = list.stream()
                .filter(e -> e.getHitProb() != null && e.getHitProb() > 0)
                .collect(Collectors.toList());
        if (candidates.isEmpty()) return null;

        int total = candidates.stream().mapToInt(RemarkComment::getHitProb).sum();
        int r = random.nextInt(total) + 1; // [1, total]
        int acc = 0;
        for (RemarkComment e : candidates) {
            acc += e.getHitProb();
            if (r <= acc) {
                return e;
            }
        }
        // 理论上不会到达这里
        return candidates.get(candidates.size() - 1);
    }

    // ===================== Redis helper =====================
    private List<RemarkComment> getAllFromCache() {
        try {
            List<Object> values = stringRedisTemplate.opsForHash().values(KEY_ALL);
            if (values == null || values.isEmpty()) return Collections.emptyList();
            List<RemarkComment> res = new ArrayList<>(values.size());
            for (Object v : values) {
                try {
                    res.add(objectMapper.readValue(String.valueOf(v), RemarkComment.class));
                } catch (Exception ignore) {}
            }
            return res;
        } catch (Exception e) {
            log.warn("读取缓存失败: {}", e.getMessage());
            return Collections.emptyList();
        }
    }

    private void putCache(RemarkComment e) {
        if (e == null || e.getId() == null) return;
        try {
            String json = objectMapper.writeValueAsString(e);
            stringRedisTemplate.opsForHash().put(KEY_ALL, String.valueOf(e.getId()), json);
        } catch (Exception ex) {
            log.warn("写缓存失败: {}", ex.getMessage());
        }
    }

    private void evictCache(Long id) {
        if (id == null) return;
        try {
            stringRedisTemplate.opsForHash().delete(KEY_ALL, String.valueOf(id));
        } catch (DataAccessException ex) {
            log.warn("删缓存失败: {}", ex.getMessage());
        }
    }
}
