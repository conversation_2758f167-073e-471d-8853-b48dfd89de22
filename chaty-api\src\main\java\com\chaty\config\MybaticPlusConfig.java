package com.chaty.config;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.chaty.tenant.CustomTenantHandler;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

@Configuration
@MapperScan(
        basePackages = "com.chaty.mapper",
        sqlSessionFactoryRef = "mysqlSqlSessionFactory",
        sqlSessionTemplateRef = "mysqlSqlSessionTemplate"
)
public class MybaticPlusConfig {

    @Autowired
    private CustomTenantHandler customTenantHandler;
    @Autowired
    private MyMetaObjectHandler myMetaObjectHandler;


    @Bean("mysqlSqlSessionFactory")
    @Primary
    public SqlSessionFactory mysqlSqlSessionFactory(
            @Qualifier("mysqlDataSource") DataSource ds,
            MybatisPlusInterceptor mysqlInterceptor
    ) throws Exception {
        MybatisSqlSessionFactoryBean fb = new MybatisSqlSessionFactoryBean();
        fb.setDataSource(ds);
        fb.setPlugins(mysqlInterceptor);

        // MyBatis-Plus的Configuration
        MybatisConfiguration configuration = new MybatisConfiguration();
        configuration.setMapUnderscoreToCamelCase(true);
        fb.setConfiguration(configuration);

        GlobalConfig globalConfig = new GlobalConfig();
        GlobalConfig.DbConfig dbConfig = new GlobalConfig.DbConfig();
        dbConfig.setInsertStrategy(FieldStrategy.NOT_NULL);
        dbConfig.setUpdateStrategy(FieldStrategy.NOT_NULL);
        dbConfig.setWhereStrategy(FieldStrategy.NOT_NULL);
        dbConfig.setLogicDeleteField("deleted");
        dbConfig.setLogicDeleteValue("1");
        dbConfig.setLogicNotDeleteValue("0");
        globalConfig.setDbConfig(dbConfig);

        globalConfig.setMetaObjectHandler(myMetaObjectHandler);
        fb.setGlobalConfig(globalConfig);

        return fb.getObject();
    }


    @Bean("mysqlSqlSessionTemplate")
    @Primary
    public SqlSessionTemplate mysqlSqlSessionTemplate(
            @Qualifier("mysqlSqlSessionFactory") SqlSessionFactory sf
    ) {
        return new SqlSessionTemplate(sf);
    }

    @Bean("mysqlTransactionManager")
    public PlatformTransactionManager mysqlTxManager(
            @Qualifier("mysqlDataSource") DataSource ds
    ) {
        return new DataSourceTransactionManager(ds);
    }

    @Bean("mysqlInterceptor")
    @Primary
    public MybatisPlusInterceptor mysqlInterceptor(CustomTenantHandler handler) {
        MybatisPlusInterceptor it = new MybatisPlusInterceptor();
        it.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        TenantLineInnerInterceptor tli = new TenantLineInnerInterceptor();
        tli.setTenantLineHandler(handler);
        it.addInnerInterceptor(tli);
        return it;
    }
}
