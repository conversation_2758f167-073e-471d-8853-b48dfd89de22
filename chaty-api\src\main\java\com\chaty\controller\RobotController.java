package com.chaty.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chaty.common.BaseResponse;
import com.chaty.completion.CompletionService;
import com.chaty.dto.PaperTopicDTO;
import com.chaty.dto.RemoteFileWithRemarkDTO;
import com.chaty.dto.VikingMatchResult;
import com.chaty.entity.FtpMessage;
import com.chaty.entity.FtpMessageTitle;
import com.chaty.entity.GptAskLogEntity;
import com.chaty.enums.AIModelConsts;
import com.chaty.mapper.FtpMessageMapper;
import com.chaty.mapper.FtpMessageTitleMapper;
import com.chaty.service.*;
import com.chaty.entity.FtpFiles;
import com.chaty.mapper.FtpFilesMapper;
import com.chaty.tenant.IgnoreTenant;
import com.chaty.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.chaty.enums.GptAskLogType.standardVolumeMatching;

@Slf4j
@RestController
@RequestMapping("/api/robot")
public class RobotController {

    @Value("${robot.access_token:baaa1bb44167b544ac0e08a63da58798b398731b2f5189152b15477eeffc4e34}")
    private String rightAccessToken;


    @Resource
    private FtpMessageMapper ftpMessageMapper;
    @Resource
    private FtpMessageTitleMapper ftpMessageTitleMapper;
    @Resource
    private FtpMessageService ftpMessageService;
    @Resource
    VikingVectorService vikingVectorService;
    @Resource
    FileUtil fileUtil;


    @PostMapping("/send")
    @IgnoreTenant
    public BaseResponse<?> send(@RequestBody JSONObject body, @RequestParam("access_token") String accessToken) throws Exception {
        log.info("get robot body {}", body);

        if (StrUtil.isBlank(accessToken) || !rightAccessToken.equals(accessToken)) {
            return BaseResponse.error("accessToken错误");
        }
        JSONObject jsonObject = JSONUtil.parseObj(body.getStr("content"));


        FtpMessage message = new FtpMessage();
        if (jsonObject.containsKey("path")) {
            message.setFilePath(jsonObject.get("path").toString());
        } else {
            return BaseResponse.error("path路径为空");
        }

        if (jsonObject.containsKey("filename")) {
            message.setFileName(jsonObject.get("filename").toString());
        } else {
            return BaseResponse.error("filename路径为空");
        }

        if (jsonObject.containsKey("title")) {
            message.setSchoolName(jsonObject.get("title").toString());
        }

        if (jsonObject.containsKey("time")) {
            String time = jsonObject.get("time").toString();
            try {
                LocalDateTime localDateTime = LocalDateTime.parse(time);
                message.setNotificationTime(localDateTime);
            } catch (Exception e) {
                log.error("time解析失败 e:{}", e.getMessage());
                message.setNotificationTime(LocalDateTime.now());
            }
        } else {
            message.setNotificationTime(LocalDateTime.now());
        }

        if (jsonObject.containsKey("count")) {
            try {
                Integer count = Integer.valueOf(jsonObject.get("count").toString());
                message.setNotificationCount(count);
            } catch (Exception e) {
                log.error("count解析失败 e:{}", e.getMessage());
                message.setNotificationCount(1);
            }
        } else {
            message.setNotificationCount(1);
        }
        String path = jsonObject.get("path").toString();
        int idx = path.lastIndexOf(File.separator);
        String parentPath = (idx != -1)
                ? path.substring(0, idx)
                : path;
        Map<String, Object> res = ftpMessageService.downloadAndAttachPdfDetail(
                message, parentPath
        );
        if (res == null) {
            log.error("文件下载或校验失败，可能无法正常解析 PDF");
        } else {
            message.setFileDetail(JSONUtil.toJsonStr(res));
            if (res.containsKey("firstPage")) {
                try {
                    Map<String, Object> firstPage = (Map<String, Object>) res.get("firstPage");
                    if (firstPage.containsKey("imageInfo")) {
                        Map<String, Object> imageInfo = (Map<String, Object>) firstPage.get("imageInfo");
                        Double width = imageInfo.containsKey("width") ? Double.valueOf(imageInfo.get("width").toString()) : null;
                        Double height = imageInfo.containsKey("height") ? Double.valueOf(imageInfo.get("height").toString()) : null;
                        String docType = imageInfo.containsKey("docType") ? imageInfo.get("docType").toString() : null;
                        message.setWidth(width);
                        message.setHeight(height);
                        message.setDocType(docType);
                    }
                    String docpath = firstPage.containsKey("docpath") ? firstPage.get("docpath").toString() : null;
                    String url = firstPage.containsKey("url") ? firstPage.get("url").toString() : null;
                    message.setDocUrl(docpath);
                    message.setImgUrl(url);
                } catch (Exception e) {
                    log.error("解析第一页图片失败 e:{}", e.getMessage());
                }
            }
            Integer pdfPaperSize = res.containsKey("pdfPaperSize")
                    ? Integer.valueOf(res.get("pdfPaperSize").toString())
                    : null;
            message.setPdfPaperSize(pdfPaperSize);
        }
        message.setMatchStatus("matching");
        // 先插入
        int row = ftpMessageMapper.insert(message);

        // 向量化
        try {
            VikingMatchResult vikingMatchResult = vikingVectorService.matchOrUpsert(String.valueOf(message.getId()), fileUtil.ctxUrl2Path(message.getImgUrl()));
            if (StrUtil.isNotBlank(vikingMatchResult.getImageUrl())) {
                message.setImgUrl(vikingMatchResult.getImageUrl());
            }
            if (StrUtil.isNotBlank(vikingMatchResult.getMatchId())) {
                message.setSameToOthersPaperId(vikingMatchResult.getMatchId());
                ftpMessageService.handleSamePaper(message, vikingMatchResult.getMatchId());
            }
            if (StrUtil.isNotBlank(vikingMatchResult.getMatchedImageUrl())) {
                message.setMatchedImageUrl(vikingMatchResult.getMatchedImageUrl());
            }
            if (Objects.nonNull(vikingMatchResult.getMatchedScore())) {
                message.setMatchedScore(vikingMatchResult.getMatchedScore());
            }
            if (Objects.nonNull(vikingMatchResult.getMatchResult()) && !vikingMatchResult.getMatchResult().isEmpty()) {
                message.setMatchResult(JSONUtil.toJsonStr(vikingMatchResult.getMatchResult()));
            }
            message.setMatchStatus("matchSuccess");
        } catch (Exception e) {
            log.error("向量化查询失败：{}", e.getMessage());
            message.setMatchStatus("matchFailed");
        }
        ftpMessageMapper.updateById(message);

        try {
            PaperTopicDTO paperTopicDTO = ftpMessageService.getTopic(message);
            if (Objects.nonNull(paperTopicDTO)) {
                message.setTopic(JSONUtil.toJsonStr(paperTopicDTO));
                try {
                    message.setConfigPackagePaperNumber(paperTopicDTO.getPageNumber());
                } catch (Exception e) {
                    log.error("获取configPackagePaperNumber失败 e:{}", e.getMessage());
                }
                message.setSameToOthersPaperId(paperTopicDTO.getSameToOthersPaperId());
                String remark = "";
                if (StrUtil.isNotBlank(paperTopicDTO.getSubject())) {
                    remark += paperTopicDTO.getSubject();
                }
                if (StrUtil.isNotBlank(paperTopicDTO.getClassName())) {
                    remark += "-" + paperTopicDTO.getClassName();
                }
                if (StrUtil.isNotBlank(paperTopicDTO.getName())) {
                    remark += "-" + paperTopicDTO.getName();
                }
                if (remark.startsWith("-")) {
                    remark = remark.substring(1);
                }
                message.setRemark(remark);

                if (Boolean.TRUE.equals(paperTopicDTO.getIsSameToOthersPaper())) {
                    try {
                        // 处理相同标准卷
                        ftpMessageService.handleSamePaper(message, paperTopicDTO.getSameToOthersPaperId());
                    } catch (Exception e) {
                        log.error("handleSamePaper 失败 e:{}", e.getMessage());
                    }
                }

                // 更新
                ftpMessageMapper.updateById(message);
            }
        } catch (Exception e) {
            log.error("获取topic失败 e:{}", e.getMessage());
        }


        // 检查并插入标题
        try {
            if (StrUtil.isNotBlank(message.getSchoolName())) {
                // 检查标题是否已存在
                QueryWrapper<FtpMessageTitle> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("title", message.getSchoolName());
                FtpMessageTitle existingTitle = ftpMessageTitleMapper.selectOne(queryWrapper);

                if (existingTitle == null) {
                    // 标题不存在，创建新记录
                    FtpMessageTitle newTitle = new FtpMessageTitle();
                    newTitle.setTitle(message.getSchoolName());
                    String fullPath = message.getFilePath();
                    int lastSeparatorIndex = fullPath.lastIndexOf('/');
                    String titleParentPath = (lastSeparatorIndex != -1) ? fullPath.substring(0, lastSeparatorIndex) : fullPath;
                    newTitle.setPath(titleParentPath);
                    newTitle.setWeight(0); // 默认权重为0
                    ftpMessageTitleMapper.insert(newTitle);
                }
            }
        } catch (Exception e) {
            log.error("处理标题记录失败 e:{}", e.getMessage());
            // 不影响主流程，继续执行
        }


        if (row > 0) {
            return BaseResponse.ok("接受成功");
        } else {
            return BaseResponse.error("插入数据库失败");
        }
    }


}
