#spring.datasource.url=********************************************************************************************************
#spring.datasource.username=root
#spring.datasource.password=123456
#spring.datasource.url=jdbc:mysql://*************:3308/chaty?useSSL=false&serverTimezone=Asia/Shanghai
#spring.datasource.username=chaty
#spring.datasource.password=J5KtHc7TK3kRTYbZ

spring.datasource.url=***************************************************************************
spring.datasource.username=root
spring.datasource.password=123456

#spring.datasource.url=***************************************************************************
#spring.datasource.username=root
#spring.datasource.password=Ju8NCrL!CdiK$jo

spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.servlet.multipart.max-file-size=20MB
spring.servlet.multipart.max-request-size=50MB
server.servlet.session.timeout=30d

spring.jackson.default-property-inclusion=NON_NULL

mybatis.mapper-locations=classpath:mapper/*.xml
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis.configuration.mapUnderscoreToCamelCase=true
#
config.logerrdetail=true

# tencent api
api.temcentcloud.secretid=AKID43yjHk0lZBYnriKhroWzMsHYpwqYDb2S
api.temcentcloud.secretkey=UwITQAmEUVIUbWDj5IIF7nIgx765zUEs

# openai api
# api.openai.url=https://service-foisqhuu-1318509419.hk.apigw.tencentcs.com
# api.openai.url=https://api.openai.com
api.openai.url=https://api.openai.com/v1
api.openai.key=sk-8fQSTtLZ4IrwqE8XSJN71yzS3RSbiEFkdEF9LdlzO20ADTfZ
api.openai.proxyed[0].keys=sk-8fQSTtLZ4IrwqE8XSJN71yzS3RSbiEFkdEF9LdlzO20ADTfZ
api.openai.proxyed[0].url=http://hk.saomiaoshijuan.com:13000/v1
api.openai.proxyed[0].models=gemini-1.5-pro,gemini-1.5-flash,gemini-2.0-flash-exp,gpt-4o,gpt-4o-mini,gpt-4o-2024-08-06
api.openai.webclient.key=sk-8fQSTtLZ4IrwqE8XSJN71yzS3RSbiEFkdEF9LdlzO20ADTfZ

# mathpix api
api.mathpix.app-id=zhejianguniversity_536b41_49f14d
api.mathpix.app-key=9f036bf314c1a04f9f54667dba482ef3c35f18770fbd8b2aa516f5d77c53f9bd

# local file
file.local.path=C:\\Users\\<USER>\\Desktop\\chaty\\files
file.local.ctxpath=/static
file.tex.path=C:\\Users\\<USER>\\Desktop\\chaty\\files
file.tex.envPath=C:\\Users\\<USER>\\Desktop\\chaty\\files
#
api.baiduai.clientId=R0GbYUyG8eyjpwmCnHukl3eF
api.baiduai.clientSecret=yLsDhaqB7Cp83d5OWF0s7qtFiacNRfah

# aws
api.aws.ak=5f08d3af531411eeb4eb0242ac120004
api.aws.sk=0LRim57ZFt%CIUA%8kN0pNUj/w-n47Is
api.aws.endpoint=cpm-bee-230916092609TMPI
api.aws.host=http://saas-1222385307.us-west-2.elb.amazonaws.com

# Thymeleaf
spring.freemarker.template-loader-path=classpath:/templates
spring.freemarker.suffix=.tex

logging.level.com.chaty.api=debug

# server.url=http://localhost:8080
server.url=http://127.0.0.1:20001
server.port=20001

# claude
api.claude.url=https://api.anthropic.com
api.claude.key=************************************************************************************************************

# qianwen
api.qianwen.url=https://dashscope.aliyuncs.com
api.qianwen.key=sk-4c903585320d4bb99988b9a9e9cc92a6
# paddle ocr
ocr.paddle.script=/home/<USER>/ocr.py

# ollama
api.ollama.url=http://**********:11434

# 答题卡识别
answerCard.executorFile=/home/<USER>/workspace/AnswerCardDetect/detect1.py

# alist配置
alist.url=https://alist.saomiaoshijuan.com
alist.token=alist-dc175cb7-d8bd-451f-8387-c8894cefd0b6e5lPfuAcDnbI7e4FWNN37L4hH8tp2Ve5VP3FEcUurz12qDstsyC7Ax5TNym4SG4M
alist.list-path=/ftp/in_GM8525cdn_zjedu
alist.out-path=/ftp/out_GM8525cdn_zjedu
alist.root-path=/ftp

# 远程打印机配置
lianke.url=https://cloud.liankenet.com
lianke.openUrl=https://open.liankenet.com
lianke.username=13666680278
lianke.password=1122liankeyun
lianke.api-key=i4BAOLacP9lDh6Hxs6PkAtJGycCzz69s
lianke.devices[0].deviceId=lc11cd15008346
lianke.devices[0].deviceKey=FZCV3dlQNJgPxJ8m
lianke.devices[0].deviceName=default
lianke.devices[1].deviceId=lc11cd29251352
lianke.devices[1].deviceKey=sZZMPRYD65q5FF7c
lianke.devices[1].deviceName=AIHardwareDemonstration

# Redis
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.database=0

# Lettuce
spring.redis.jedis.pool.max-active=10
spring.redis.jedis.pool.max-idle=5
spring.redis.jedis.pool.min-idle=5
spring.redis.jedis.pool.max-wait=3000
spring.redis.timeout=5000

essayScoring.url=http://127.0.0.1:8002

#spring.profiles.active=dev

metrics.record.size=2
metrics.area.size=2

api.baiduocr.appid=*********
api.baiduocr.apiKey=ehBOrsA77pZwVD00M0NS8oTK
api.baiduocr.secretKey=ftAXxAW9GMaieb9klfiLxxy2UFNfXnvL

watch.dir.path=C:\\Users\\<USER>\\Documents\\Intel-AIPC-Agent
watch.enableWatch=true

