package com.chaty.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chaty.dto.ErrorCorrectionDTO;
import com.chaty.entity.DocCorrectConfig;
import com.chaty.entity.DocCorrectRecord;
import com.chaty.entity.ErrorCorrection;
import com.chaty.exception.BaseException;
import com.chaty.mapper.DocCorrectConfigMapper;
import com.chaty.mapper.DocCorrectRecordMapper;
import com.chaty.mapper.ErrorCorrectionMapper;
import com.chaty.service.ErrorCorrectionService;
import com.chaty.util.FileUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class ErrorCorrectionServiceImpl implements ErrorCorrectionService {

    @Resource
    private ErrorCorrectionMapper errorCorrectionMapper;
    @Resource
    private DocCorrectRecordMapper docCorrectRecordMapper;
    @Resource
    private DocCorrectConfigMapper docCorrectConfigMapper;
    @Value("${file.local.ctxpath}")
    public String ctxPath;
    @Value("${server.url}")
    public String serverUrl;

    @Override
    public ErrorCorrection add(ErrorCorrection param) {
        int cnt = errorCorrectionMapper.insert(param);
        if (cnt == 0) {
            throw new BaseException("添加失败");
        }
        return param;
    }

    @Override
    public void updateById(ErrorCorrection param) {
        int cnt = errorCorrectionMapper.updateById(param);
        if (cnt == 0) {
            throw new BaseException("添加失败");
        }
    }

    @Override
    public void deleteById(Long id) {
        int cnt = errorCorrectionMapper.deleteById(id);
        if (cnt == 0) {
            throw new BaseException("添加失败");
        }
    }

    @Override
    public IPage<ErrorCorrection> page(ErrorCorrectionDTO param) {
        LambdaQueryWrapper<ErrorCorrection> queryWrapper = Wrappers.<ErrorCorrection>lambdaQuery()
                .eq(Objects.nonNull(param.getRecordId()), ErrorCorrection::getRecordId, param.getRecordId())
                .like(StrUtil.isNotBlank(param.getDocName()), ErrorCorrection::getDocName, param.getDocName())
                .orderByDesc(ErrorCorrection::getUpdateTime);
        IPage<ErrorCorrection> res = errorCorrectionMapper.selectPage(param.getPage().page(), queryWrapper);
        res.setRecords(checkImgUrl(res.getRecords()));
        return res;
    }

    private List<ErrorCorrection> checkImgUrl(List<ErrorCorrection> data) {
        for (ErrorCorrection item : data) {
            if (StrUtil.isBlank(item.getAreaImgUrl())) {
                if (StrUtil.isNotBlank(item.getRecordId()) && Objects.nonNull(item.getAreaIdx())) {
                    DocCorrectRecord record = docCorrectRecordMapper.selectById(item.getRecordId());
                    if (Objects.isNull(record)) {
                        continue;
                    }
                    // recordId和areaIdx找图片
                    if (StrUtil.isNotEmpty(item.getConfigId())) {
                        DocCorrectConfig docCorrectConfig = docCorrectConfigMapper.selectById(item.getConfigId());
                        if (Objects.nonNull(docCorrectConfig)) {
                            // 截图图片
                            String areas = docCorrectConfig.getAreas();
                            JSONArray areaArray = JSONUtil.parseArray(areas);
                            JSONObject area = areaArray.getJSONObject(item.getAreaIdx());
                            JSONObject areaObj = area.getJSONObject("area");
                            Integer offsetX = Objects.isNull(record.getOffsetX()) ? 0 : record.getOffsetX();
                            Integer offsetY = Objects.isNull(record.getOffsetY()) ? 0 : record.getOffsetY();
                            String docUrl = record.getDocurl().substring(ctxPath.length() + 1);
                            String img = FileUtil.INSTANCE.docAreaImg(docUrl, areaObj.getInt("x") + offsetX, areaObj.getInt("y") + offsetY,
                                    areaObj.getInt("width"), areaObj.getInt("height"));
                            String imgUrl = String.format("%s/%s", ctxPath, img);
                            item.setAreaImgUrl(imgUrl);
                        }
                    }
                }
            }
        }
        return data;
    }
}
