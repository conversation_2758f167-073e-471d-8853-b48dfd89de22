package com.chaty.service.admin.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.dto.AdminRoleDTO;
import com.chaty.dto.AdminRoleUserDTO;
import com.chaty.entity.admin.AdminRoleUser;
import com.chaty.exception.BaseException;
import com.chaty.mapper.admin.AdminRoleUserMapper;
import com.chaty.service.admin.AdminRoleUserService;

import cn.hutool.core.bean.BeanUtil;

@Service
public class AdminRoleUserServiceImpl extends ServiceImpl<AdminRoleUserMapper, AdminRoleUser>
		implements AdminRoleUserService {

	@Override
	public void add(AdminRoleUserDTO params) {
		AdminRoleUser add = BeanUtil.copyProperties(params, AdminRoleUser.class);
		AdminRoleUser existed = selectExisted(params.getRoleId(), params.getUserId());
		if (existed != null) {
			throw new BaseException("用户已关联该角色");
		}
		this.save(add);
	}

	public AdminRoleUser selectExisted(String roleId, String userId) {
		QueryWrapper<AdminRoleUser> wrapper = Wrappers.query(AdminRoleUser.class)
				.eq("role_id", roleId)
				.eq("user_id", userId)
				.eq("deleted", false);
		return this.getOne(wrapper, false);
	}

	@Override
	public List<?> list(AdminRoleUserDTO params) {
		QueryWrapper<?> wrapper = Wrappers.query()
				.eq("aru.deleted", false)
				.eq(StringUtils.hasText(params.getRoleId()), "aru.role_id", params.getRoleId())
				.eq(StringUtils.hasText(params.getUserId()), "aru.user_id", params.getUserId())
				.like(StringUtils.hasText(params.getUsername()), "u.username", params.getUsername())
				.orderByDesc("aru.create_time");
		return this.baseMapper.listRoleUser(wrapper);
	}

	@Override
	public void delete(String id) {
		LambdaUpdateWrapper<AdminRoleUser> wrapper = Wrappers.lambdaUpdate(AdminRoleUser.class)
				.set(AdminRoleUser::getDeleted, true)
				.eq(AdminRoleUser::getId, id);
		this.update(wrapper);
	}

	@Override
	public List<AdminRoleDTO> getRoleList(String userId) {
		Wrapper<?> wrapper = Wrappers.query()
				.eq("aru.deleted", false)
				.eq("ar.deleted", false)
				.eq("aru.user_id", userId)
				.orderByDesc("aru.create_time");
		return this.baseMapper.getRoleList(wrapper);
	}

}
