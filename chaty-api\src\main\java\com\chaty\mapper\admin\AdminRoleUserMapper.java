package com.chaty.mapper.admin;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.chaty.dto.AdminRoleDTO;
import com.chaty.dto.AdminRoleUserDTO;
import com.chaty.entity.admin.AdminRoleUser;

@Mapper
public interface AdminRoleUserMapper extends BaseMapper<AdminRoleUser> {

    @Select("select aru.*, ar.name as rolename, ar.role_auth, u.username from admin_role_user aru left join admin_role ar on aru.role_id = ar.id left join user u on aru.user_id = u.id ${ew.customSqlSegment}")
    List<AdminRoleUserDTO> listRoleUser(@Param(Constants.WRAPPER) Wrapper<?> wrapper);

    @Select("select ar.* from admin_role_user aru left join admin_role ar on aru.role_id = ar.id ${ew.customSqlSegment}")
    List<AdminRoleDTO> getRoleList(@Param(Constants.WRAPPER) Wrapper<?> wrapper);

}
