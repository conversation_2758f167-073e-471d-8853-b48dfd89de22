package com.chaty.service.impl;


import java.util.*;
import javax.annotation.Resource;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.chaty.api.douBao.DouBaoApi;
import com.chaty.api.openai.OpenaiApi;
import com.chaty.dto.MessageDTO;
import com.chaty.entity.GptAskLogEntity;
import com.chaty.enums.AIModelConsts;
import com.chaty.service.GptAskLogService;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import com.chaty.dto.ChatCompletionDTO;
import com.chaty.service.BasicAiService;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import static com.chaty.service.impl.OpenaiServiceImpl.parseFunctions;
import static com.chaty.util.ModelRequestUtil.mergeModelRequest;

@Slf4j
@Service("doubaoService")
public class DoubaoServiceImpl implements BasicAiService {

    @Resource
    private DouBaoApi douBaoApi;
    @Resource
    private OpenaiApi openaiApi;
    @Resource
    private WebClient douBaoWebClient;
    @Resource
    private GptAskLogService gptAskLogService;


    @Override
    public Map<String, Object> chatForCompletion(ChatCompletionDTO param, GptAskLogEntity gptAskLogEntity) {
        Map<String, Object> apiParam = null;
        Map<String, Object> resp = null;
        try {
            apiParam = convert2CompletionParam(param);
            // 记录问答开始时间
            gptAskLogEntity.setStartTime(System.currentTimeMillis());
//        log.info("请求 doubao 日志{}", gptAskLogEntity);
            resp = douBaoApi.chatCompletionsV1(apiParam);

//        log.info("response doubao chat completion:{}", JSONUtil.toJsonStr(resp));
            // 异常处理
            Boolean isFunctionCall = AIModelConsts.functionCallModels.contains(param.getModel());
            if (isFunctionCall) {
                resp.put("$function_call", JSONUtil.getByPath(JSONUtil.parseObj(resp), "choices[0].message.function_call"));
                resp.put("$response", JSONUtil.getByPath(JSONUtil.parseObj(resp), "choices[0].message"));
            } else {
                resp.put("$response", JSONUtil.getByPath(JSONUtil.parseObj(resp), "choices[0].message.content"));
            }
        } catch (Exception e) {
            log.error("请求 doubao chat completion 异常: {}", e.getMessage(), e);
            gptAskLogEntity.setError("请求 doubao chat completion 异常: {}" + e.getMessage());
            gptAskLogService.add(BasicAiService.getGptAskLogEntityInAsk(apiParam, resp, gptAskLogEntity));
            throw e;
        }
        gptAskLogService.add(BasicAiService.getGptAskLogEntityInAsk(apiParam, resp, gptAskLogEntity));
        resp.put("$logprobs", JSONUtil.getByPath(JSONUtil.parseObj(resp), "choices[0].logprobs.content"));
        return resp;
    }


    @Override
    public Map<String, Object> getFinalCompletion(ChatCompletionDTO param) {
        return convert2CompletionParam(param);
    }

    @Override
    public Boolean isSupport(String model) {
        return AIModelConsts.douBaoJSONModels.contains(model);
    }


    @Override
    public Flux<String> streamCompletetion(ChatCompletionDTO param) {
        Map<String, Object> apiParam = convert2CompletionParam(param);
        apiParam.put("stream", true);

        String url = "/api/v3/chat/completions";

        return douBaoWebClient.post()
                .uri(url)
                .contentType(MediaType.APPLICATION_JSON)
                .body(Mono.just(apiParam), Map.class)
                .retrieve()
                .bodyToFlux(String.class)
                .map(s -> {
                    log.info("request doubao chat completion, received steam data: {}", s);
                    if (JSONUtil.isTypeJSONObject(s)) {
                        JSONObject parsed = JSONUtil.parseObj(s);

                        return parsed.toString();
                    } else {
                        return s;
                    }
                });
    }

    private Map<String, Object> convert2CompletionParam(ChatCompletionDTO param) {
        Map<String, Object> apiParam = new HashMap<>();
        apiParam.put("model", param.getModel());
        List<MessageDTO> messageDTOS = param.getMessages();

        if (Boolean.TRUE.equals(param.getJsonSchema())) {
            // 需要改为豆包支持的格式
            List<MessageDTO> messages = param.getMessages();
            JSONArray doubaoMessages = new JSONArray();
            for (MessageDTO message : messages) {
                JSONObject doubaoMessage = new JSONObject();
                doubaoMessage.set("role", message.getRole());
                JSONArray contents = new JSONArray();
                if (Objects.nonNull(message.getContent()) && message.getContent() instanceof JSONArray) {
                    JSONArray contentParam = (JSONArray) message.getContent();
                    contents.addAll( contentParam);
                } else if (Objects.nonNull(message.getContent()) && message.getContent() instanceof String) {
                    // 如果是字符串，直接使用
                    JSONObject content = new JSONObject();
                    content.set("type", "text");
                    content.set("text", message.getContent());
                    contents.add(content);
                } else {
                    // 其它类型的内容，转换为字符串
                    JSONObject content = new JSONObject();
                    content.set("type", "text");
                    content.set("text", JSONUtil.toJsonStr(message.getContent()));
                    contents.add(content);
                }
                doubaoMessage.set("content", contents);
                doubaoMessages.add(doubaoMessage);
            }
            apiParam.put("messages", doubaoMessages);
        } else {
            apiParam.put("messages", param.getMessages());
        }
        if (Objects.nonNull(param.getLogprobs())) {
            apiParam.put("logprobs", param.getLogprobs());
        }
//        if (Objects.nonNull(param.getTopp())) {
//            apiParam.put("top_p", param.getTopp());
//        }
//        if (Objects.nonNull(param.getTemperature())) {
//            apiParam.put("temperature", param.getTemperature());
//        }
        apiParam.put("temperature", 0);
        if (Objects.nonNull(param.getFunctions())) {
            apiParam.put("functions", parseFunctions(param.getFunctions()));
        }
        if (Objects.nonNull(param.getMaxTokens())) {
            apiParam.put("max_tokens", param.getMaxTokens());
        }
        if (Objects.nonNull(param.getResponseFormat()) && AIModelConsts.douBaoAskFromFunctionCallModels.contains(param.getModel())) {
            JSONArray tools = new JSONArray();
            tools.put(toDoubaoFunctionSchema(param.getResponseFormat()));
            apiParam.put("tools", tools);
        }

        //处理jsonobject
        if (Objects.nonNull(param.getResponseFormat()) && Boolean.TRUE.equals(param.getJsonObject())) {
            JSONObject responseFormat = new JSONObject();
            responseFormat.put("type", "json_object"); // 固定为 json_object
            apiParam.put("response_format", responseFormat);
        }

        //处理jsonschema
        if (Objects.nonNull(param.getResponseFormat()) && Boolean.TRUE.equals(param.getJsonSchema())) {
            JSONObject apiSchema = new JSONObject();
            apiSchema.set("name", "最终思考结果");
            apiSchema.set("schema", param.getResponseFormat().getByPath("json_schema.schema"));
            apiSchema.set("description ", "请在次填写你的最终思考后的结果");
            apiSchema.set("strict", true);

            JSONObject responseFormat = new JSONObject();
            responseFormat.set("type", "json_schema");
            responseFormat.set("json_schema", apiSchema);
            apiParam.put("response_format", responseFormat);
        }


        if (Objects.nonNull(param.getModelRequestObj())) {
            apiParam = mergeModelRequest(apiParam, param);
        }
        return apiParam;
    }

    /**
     * 顶层转换方法
     *
     * @param openapiJson 形如：
     *                    {
     *                    "type":"json_schema",
     *                    "json_schema":{
     *                    "name":"read_questions",
     *                    "schema":{ ...nested schema... }
     *                    }
     *                    }
     */
    public static JSONObject toDoubaoFunctionSchema(JSONObject openapiJson) {
        if (!"json_schema".equals(openapiJson.getStr("type"))
                || !openapiJson.containsKey("json_schema")) {
            throw new IllegalArgumentException("输入不是预期的 json_schema 格式");
        }

        JSONObject meta = openapiJson.getJSONObject("json_schema");
        String name = meta.getStr("name");
        JSONObject schema = meta.getJSONObject("schema");

        // 递归 normalize 一下 schema
        JSONObject normalized = normalizeSchema(schema);

        JSONObject fn = new JSONObject()
                .set("name", name)
                .set("description", "最终结果")
                .set("parameters", normalized);

        return new JSONObject()
                .set("type", "function")
                .set("function", fn);
    }

    /**
     * 递归处理 JSON Schema：
     * - 碰到 properties 下的每个子 schema，如果是 JSONObject，就再 recurse
     * - 碰到 items 下的 schema，也 recurse
     * - 其它字段原样拷贝
     */
    private static JSONObject normalizeSchema(JSONObject schema) {
        JSONObject result = new JSONObject();

        for (String key : schema.keySet()) {
            Object val = schema.get(key);

            if ("properties".equals(key) && val instanceof JSONObject) {
                JSONObject props = (JSONObject) val;
                JSONObject newProps = new JSONObject();
                for (String propName : props.keySet()) {
                    Object child = props.get(propName);
                    if (child instanceof JSONObject) {
                        newProps.set(propName, normalizeSchema((JSONObject) child));
                    } else {
                        newProps.set(propName, child);
                    }
                }
                result.set(key, newProps);

            } else if ("items".equals(key) && val instanceof JSONObject) {
                // 数组里的 items schema
                result.set(key, normalizeSchema((JSONObject) val));

            } else {
                // type、required、additionalProperties、enum、description 等都直接拷贝
                result.set(key, val);
            }
        }

        return result;
    }


}

