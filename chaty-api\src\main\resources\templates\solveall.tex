\documentclass[15pt]{beamer}
\usepackage{tikz}
\usepackage{ctex}
\usepackage{adjustbox}
\usetheme{Madrid}
\usecolortheme{default}
{% comment %} \setCJKmainfont{AR PL UKai CN} {% endcomment %}
\title[] %optional
{Madrid theme + beaver}
\subtitle{Demonstrating larger fonts}
\author[Generated by SolveGPT] % (optional)
{}

\institute[] % (optional)
{
}
    
\date[] % (optional)
{}

% Use a simple TikZ graphic to show where the logo is positioned
\logo{}
\begin{document}

\begin{frame}[allowframebreaks]
\frametitle{题目}

\begin{block}{题目}

${config.question}

\end{block}

\end{frame}

\begin{frame}[allowframebreaks]
\frametitle{最终答案}
${"\\framesubtitle{Accuracy=" + accuracy + "}"}

\begin{block}{最终答案}

${finalAnswer}

\end{block}

\end{frame}

\begin{frame}[allowframebreaks]
\frametitle{最终答案}

<#if models[finalAnswerIndex].definition?has_content>

\begin{block}{定义}

${models[finalAnswerIndex].definition}

\end{block}

</#if>

<#if models[finalAnswerIndex].theorem?has_content>

\begin{block}{定理}

${models[finalAnswerIndex].theorem}

\end{block}

</#if>

<#if models[finalAnswerIndex].process?has_content>

\begin{block}{解题过程}

${models[finalAnswerIndex].process}

\end{block}

</#if>

<#if models[finalAnswerIndex].answer?has_content>

\begin{block}{答案}

${models[finalAnswerIndex].answer}

\end{block}

</#if>

\end{frame}

\end{document}
