package com.chaty;

import com.volcengine.tos.TOSV2;
import com.volcengine.tos.TOSV2ClientBuilder;
import com.volcengine.tos.model.object.PutObjectInput;
import com.volcengine.tos.model.object.PutObjectOutput;

import java.io.File;
import java.io.FileInputStream;

public class ManualUploadTest {
    public static void main(String[] args) throws Exception {
        String region = "cn-beijing";
        String endpoint = "https://tos-cn-beijing.volces.com";
        String accessKey = "AKLTZTIyNTg3ZjRiMTU0NGI1YWI4NjhkZmQ1Y2E1ZGIwMTE";
        String secretKey = "T0RWaE5XRmxOelEwTmpBeE5EYzVNemt4TW1VNE1UZzRZV1k1TkdJMk16Yw==";
        String bucket = "embedding";
        String localPath = "C:\\Users\\<USER>\\Desktop\\tetsBucket\\23.jpg";
        String key = "images/test-23.jpg";

        TOSV2 tos = new TOSV2ClientBuilder().build(region, endpoint, accessKey, secretKey);

        File file = new File(localPath);
        try (FileInputStream fis = new FileInputStream(file)) {
            PutObjectInput putObjectInput = new PutObjectInput()
                    .setBucket(bucket)
                    .setKey(key)
                    .setContent(fis)
                    .setContentLength(file.length());

            PutObjectOutput output = tos.putObject(putObjectInput);
            System.out.println("Upload succeed. etag=" + output.getEtag() + ", crc64=" + output.getHashCrc64ecma());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
