package com.chaty.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;

import java.util.Date;

/** 测试集图片信息实体（不走 Redis） */
@Data
@TableName(value = "test_set_image", autoResultMap = true)
public class TestSetImageEntity {

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 所属测试集ID */
    private Long testSetId;

    /** 图片地址 */
    private String imgUrl;

    /** 正确答案(JSON) */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JsonNode rightAnswer;

    /** 题目信息(JSON) */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JsonNode questionDetail;

    /** 题型 */
    private String questionType;

    /** 是否使用题目信息（默认true） */
    private Boolean useQuestionDetail;

    /** 来源试卷ID */
    private String fromRecordId;

    /** 来源试卷区域号 */
    private Integer fromRecordAreaIdx;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新时间 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
