package com.chaty.enums;


public class AIModelMaxTokenConsts {

    Integer GPT_4_V = 4096;

    Integer GPT_4O = 16384;

    Integer GPT_4O_MINI = 16384;

    public static final Integer GPT_4O_20240806 = 16384;

    Integer GPT_4O_20240806_3 = 16384;  // 批改模型，三倍批改

    Integer GEMINI_15_PRO = 4096;

    Integer GEMINI_15_FLASH = 4096;

    public static Integer getDoubaoMaxToken(String aiModel) {
        Integer res = 4096;
        switch (aiModel) {
            case AIModelConsts.doubao_15_pro:
                res = 12 * 1024;
                break;
            case AIModelConsts.doubao_15_pro_25032:
                res = 16 * 1024;
                break;
            case AIModelConsts.doubao_1_5_thinking_pro_m_250415:
                res = 16 * 1024;
                break;
            case AIModelConsts.doubao_1_5_thinking_pro_250415:
                res = 16 * 1024;
                break;
            case AIModelConsts.doubao_vision_pro_32k_241028:
                res = 4 * 1024;
                break;
            case AIModelConsts.doubao_vision_lite_32k_241015:
                res = 4 * 1024;
                break;
            case AIModelConsts.doubao_15_ui_tars_250328:
                res = 4 * 1024;
                break;
            case AIModelConsts.doubao_embedding_vision_250328:
                res = 2 * 1024;
                break;
            case AIModelConsts.doubao_15_vision_lite_250315:
                res = 16 * 1024;
                break;
            case AIModelConsts.doubao_15_thinking_vision_pro_250428:
                res = 16 * 1024;
                break;

            case AIModelConsts.doubao_Seed_16:
                res = 16 * 1024;
                break;

            case AIModelConsts.doubao_Seed_16_flash:
                res = 16 * 1024;
                break;

            case AIModelConsts.doubao_Seed_16_thinking:
                res = 16 * 1024;
                break;
        }
        return res;
    }
}
