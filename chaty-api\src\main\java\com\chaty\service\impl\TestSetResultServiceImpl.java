package com.chaty.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.entity.TestSetEntity;
import com.chaty.entity.TestSetResultEntity;
import com.chaty.enums.TestSetResultStatus;
import com.chaty.mapper.TestSetMapper;
import com.chaty.mapper.TestSetResultMapper;
import com.chaty.service.TestSetResultService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class TestSetResultServiceImpl
        extends ServiceImpl<TestSetResultMapper, TestSetResultEntity>
        implements TestSetResultService {

    @Resource
    private TestSetResultMapper resultMapper;

    @Resource
    private TestSetMapper testSetMapper; // 用于抓取测试集快照

    @Override
    public IPage<TestSetResultEntity> page(Integer pageNumber, Integer pageSize,
                                           Long testSetId, TestSetResultStatus status) {
        Page<TestSetResultEntity> page = new Page<>(
                pageNumber == null ? 1 : pageNumber,
                pageSize == null ? 10 : pageSize
        );

        LambdaQueryWrapper<TestSetResultEntity> qw = new LambdaQueryWrapper<>();
        if (testSetId != null) {
            qw.eq(TestSetResultEntity::getTestSetId, testSetId);
        }
        if (status != null) {
            qw.eq(TestSetResultEntity::getStatus, status);
        }
        qw.orderByDesc(TestSetResultEntity::getCreateTime);

        return this.page(page, qw);
    }

    @Override
    public Long add(TestSetResultEntity param) {
        // 兜底：计数字段
        if (param.getCorrectCount() == null) param.setCorrectCount(0);
        if (param.getTotalCount() == null) param.setTotalCount(0);
        if (param.getImageCount() == null) param.setImageCount(0);
        if (param.getImageSuccessCount() == null) param.setImageSuccessCount(0);

        // 默认状态：PROCESSING
        if (param.getStatus() == null) {
            param.setStatus(TestSetResultStatus.PROCESSING);
        }

        // 自动写入测试集快照（仅当未显式传入 & 有 testSetId）
        if (param.getTestSetId() != null &&
                isBlank(param.getTestSetName()) &&
                param.getTestSetDescription() == null &&
                param.getTestSetQuestionTypes() == null) {

            TestSetEntity ts = testSetMapper.selectById(param.getTestSetId());
            if (ts != null) {
                param.setTestSetName(ts.getName() == null ? "" : ts.getName());
                param.setTestSetDescription(ts.getDescription());
                param.setTestSetQuestionTypes(ts.getQuestionTypes());
            }
        }

        this.save(param);
        return param.getId();
    }

    @Override
    public Long updateOne(TestSetResultEntity param) {
        // 一般不覆盖快照，避免历史报告随测试集变化而改变
        this.updateById(param);
        return param.getId();
    }

    @Override
    public void deleteById(Long id) {
        if (id != null) this.removeById(id);
    }

    private boolean isBlank(String s) {
        return s == null || s.trim().isEmpty();
    }
}
