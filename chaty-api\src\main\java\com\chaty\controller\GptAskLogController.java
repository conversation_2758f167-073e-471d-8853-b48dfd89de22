package com.chaty.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.common.BaseResponse;
import com.chaty.dto.GptAskLogDTO;
import com.chaty.dto.RecordTimeStatsDTO;
import com.chaty.entity.GptAskLogEntity;
import com.chaty.enums.GptAskLogType;
import com.chaty.service.GptAskLogService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.lang.reflect.Modifier;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * GPT 问答日志控制器
 */
@RestController
@RequestMapping("/api/gptAskLog")
public class GptAskLogController {

    @Resource
    private GptAskLogService gptAskLogService;

    /**
     * 分页查询 GPT 问答日志
     */
    @PostMapping("/page")
    public BaseResponse<?> page(@RequestBody GptAskLogDTO param) {
        return BaseResponse.ok(gptAskLogService.page(param));
    }

    /**
     * 添加新的 GPT 问答日志
     */
    @PostMapping("/add")
    public BaseResponse<?> add(@RequestBody GptAskLogEntity param) {
        return BaseResponse.ok( gptAskLogService.add(param));
    }

    /**
     * 更新已有的 GPT 问答日志
     */
    @PostMapping("/update")
    public BaseResponse<?> update(@RequestBody GptAskLogEntity param) {
        return BaseResponse.ok(gptAskLogService.update(param));
    }

    /**
     * 根据 ID 删除 GPT 问答日志
     */
    @DeleteMapping("/{id}")
    public BaseResponse<?> delete(@PathVariable Long id) {
        gptAskLogService.delete(id);
        return BaseResponse.ok("删除成功");
    }

    /**
     * 根据 ID 获取单条 GPT 问答日志
     */
    @GetMapping("/getById/{id}")
    public BaseResponse<?> getById(@PathVariable Long id) {
        return BaseResponse.ok(gptAskLogService.getById(id));
    }

    @GetMapping("/allType")
    public BaseResponse<List<String>> getAllType() {
        List<String> types = Arrays.stream(GptAskLogType.class.getFields())
                // 只拿 public static final String 字段
                .filter(f -> Modifier.isStatic(f.getModifiers())
                        && Modifier.isPublic(f.getModifiers())
                        && f.getType().equals(String.class))
                .map(f -> {
                    try {
                        return (String) f.get(null);
                    } catch (IllegalAccessException e) {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return BaseResponse.ok(types);
    }

    /**
     * 按 recordName 汇总统计平均耗时、总耗时及总条数
     * GET /api/gptAskLog/stats?recordName=xxx
     */
    @GetMapping("/stats")
    public BaseResponse<RecordTimeStatsDTO> stats(
            @RequestParam("recordName") String recordName) {
        RecordTimeStatsDTO stats = gptAskLogService.getStatsByRecordName(recordName);
        return BaseResponse.ok(stats);
    }

    /**
     * 按 recordName 汇总统计平均耗时、总耗时及总条数
     * GET /api/gptAskLog/stats?recordName=xxx
     */
    @GetMapping("/statsByFileId")
    public BaseResponse<RecordTimeStatsDTO> getStatsByFileId(
            @RequestParam("fileId") String fileId) {
        RecordTimeStatsDTO stats = gptAskLogService.getStatsByFileId(fileId);
        return BaseResponse.ok(stats);
    }

    /**
     * 按用户模糊搜索串，查询去重后的 recordName 列表
     * GET /api/gptAskLog/distinct?search={模糊串}
     */
    @GetMapping("/distinct")
    public BaseResponse<List<String>> distinct(
            @RequestParam(value = "search", required = false) String search) {
        List<String> names = gptAskLogService.getDistinctRecordNames(search);
        return BaseResponse.ok(names);
    }

    /**
     * 统计 gpt_ask_log 表全体记录数量
     * GET /api/gptAskLog/requestCount
     */
    @GetMapping("/requestCount")
    public BaseResponse<Long> requestCount() {
        Long count = gptAskLogService.getRequestCount();
        return BaseResponse.ok(count);
    }
}
