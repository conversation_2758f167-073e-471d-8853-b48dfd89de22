package com.chaty.dto;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;

@Data
public class AdminUserDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 3L;
    private String id;
    private String username;
    private String password;
    private String nickname;
    private String avatar;
    private String email;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    private String accessToken;

}
