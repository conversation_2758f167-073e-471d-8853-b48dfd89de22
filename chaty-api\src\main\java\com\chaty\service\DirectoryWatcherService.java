package com.chaty.service;

import com.chaty.entity.FtpMessage;
import com.chaty.mapper.FtpMessageMapper;
import com.chaty.service.impl.LocalFileServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.nio.file.*;
import java.time.LocalDateTime;

@Component
public class DirectoryWatcherService implements InitializingBean, DisposableBean {

    private static final Logger logger = LoggerFactory.getLogger(DirectoryWatcherService.class);

    /**
     * 要监听的目录，默认指向当前用户文档下的 Intel-AIPC-Agent 文件夹
     */
    @Value("${watch.dir.path:C:\\Users\\<USER>\\Documents\\Intel-AIPC-Agent}")
    private String watchDirPath;

    /**
     * 是否启用目录监听
     */
    @Value("${watch.enableWatch:false}")
    private Boolean enableWatch;
    @Resource
    private FtpMessageMapper ftpMessageMapper;
    @Resource
    private LocalFileServiceImpl localFileService;
    @Resource
    private FtpMessageService ftpMessageService;
    private WatchService watchService;
    private Thread watchThread;

    @Override
    public void afterPropertiesSet() throws Exception {
        if (!Boolean.TRUE.equals(enableWatch)) {
            logger.info("Directory watching is disabled (watch.enableWatch=false). Path [{}] will not be monitored.", watchDirPath);
            return;
        }

        Path dir = Paths.get(watchDirPath);
        if (!Files.exists(dir) || !Files.isDirectory(dir)) {
            logger.warn("Watch directory does not exist or is not a directory: {}", watchDirPath);
            return;
        }

        // 1. 创建 WatchService 并注册 ENTRY_CREATE 事件
        watchService = FileSystems.getDefault().newWatchService();
        dir.register(watchService, StandardWatchEventKinds.ENTRY_CREATE);
        logger.info("Started watching directory: {}", watchDirPath);

        // 2. 启动后台线程，轮询 WatchKey
        watchThread = new Thread(() -> {
            try {
                while (!Thread.currentThread().isInterrupted()) {
                    WatchKey key = watchService.take();  // 阻塞直到有事件
                    for (WatchEvent<?> event : key.pollEvents()) {
                        if (event.kind() == StandardWatchEventKinds.ENTRY_CREATE) {
                            Path newPath = dir.resolve((Path) event.context());
                            File newFile = newPath.toFile();
                            logger.info("Detected new file: {}", newFile.getAbsolutePath());
                            importFile(newFile);
                        }
                    }
                    if (!key.reset()) {
                        logger.warn("WatchKey no longer valid, stopping directory watcher.");
                        break;
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.info("Directory watcher thread interrupted, exiting.");
            } catch (Exception ex) {
                logger.error("Error in directory watcher thread", ex);
            }
        }, "dir-watcher-thread");

        watchThread.setDaemon(true);
        watchThread.start();
    }

    @Override
    public void destroy() throws Exception {
        if (watchService != null) {
            watchService.close();
            logger.info("WatchService closed.");
        }
        if (watchThread != null && watchThread.isAlive()) {
            watchThread.interrupt();
            logger.info("Directory watcher thread interrupted.");
        }
    }

    /**
     * 新文件入库的业务方法
     */
    private void importFile(File newFile) {
        // TODO: 实现具体的入库逻辑
        String localFilePath = localFileService.localFilePath(newFile.getName());
        FtpMessage needInsert = new FtpMessage();
        needInsert.setFileName(newFile.getName());
        needInsert.setFilePath(newFile.getAbsolutePath());
        needInsert.setIsInAgent(true);
        needInsert.setSchoolName("本地");
        needInsert.setNotificationTime(LocalDateTime.now());
        needInsert.setNotificationCount(1);
        ftpMessageMapper.insert(needInsert);
        // 进入智能体
    }
}
