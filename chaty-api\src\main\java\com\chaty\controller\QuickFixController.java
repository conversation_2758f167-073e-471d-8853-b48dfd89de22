package com.chaty.controller;

import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.StrUtil;
import com.chaty.entity.QuickFix;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.common.BaseResponse;
import com.chaty.dto.QuickFixDTO;
import com.chaty.service.QuickFixServce;

@RequestMapping("/api/quickfix")
@RestController
public class QuickFixController {

    @Resource
    private QuickFixServce quickFixServce;

    @PostMapping("/page")
    public BaseResponse<?> getPage(@RequestBody QuickFixDTO params) {
        IPage<QuickFixDTO> page = quickFixServce.getPage(params);
        return BaseResponse.ok(page);
    }

    @PostMapping("/fix")
    public BaseResponse<?> doFix(@RequestBody QuickFixDTO params) {
        quickFixServce.doFix(params);
        return BaseResponse.ok("纠错成功");
    }

    @GetMapping("/delete")
    public BaseResponse<?> delete(@RequestParam String id) {
        quickFixServce.removeById(id);
        return BaseResponse.ok("删除成功");
    }

    @GetMapping("/dwonload")
    public BaseResponse<?> download(@RequestParam String id, HttpServletResponse response) {
        Map<String, Object> res = quickFixServce.getFile(id);
        return BaseResponse.ok(res);
    }

    @PostMapping("/update")
    public BaseResponse<?> update(@RequestBody QuickFix param) {
        if (StrUtil.isBlank(param.getId())) {
            return BaseResponse.error("id不能为空");
        }
        quickFixServce.updateById(param);
        return BaseResponse.ok(param);
    }
}
