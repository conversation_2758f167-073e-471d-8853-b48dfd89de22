package com.chaty.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.common.BaseResponse;
import com.chaty.dto.ContactDTO;
import com.chaty.entity.Contact;
import com.chaty.service.ContactService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/api/contact")
public class ContactController {

    @Resource
    private ContactService contactService;

    @PostMapping("/add")
    public BaseResponse<?> addContact(@RequestBody ContactDTO dto) {
        ContactDTO result = contactService.addContact(dto);
        return BaseResponse.ok(result);
    }

    @GetMapping("/delete")
    public BaseResponse<?> deleteContact(@RequestParam Integer id) {
        contactService.deleteContact(id);
        return BaseResponse.ok("删除成功");
    }

    @PostMapping("/update")
    public BaseResponse<?> updateContact(@RequestBody ContactDTO dto) {
        ContactDTO result = contactService.updateContact(dto);
        return BaseResponse.ok(result);
    }

    @PostMapping("/select")
    public BaseResponse<IPage<ContactDTO>> selectPage(@RequestBody ContactDTO param) {
        IPage<ContactDTO> page = contactService.selectPage(param);
        return BaseResponse.ok(page);
    }

    @GetMapping("/getById")
    public BaseResponse<?> getById(@RequestParam Integer id) {
        Contact contact = contactService.getContactById(id);
        return BaseResponse.ok(contact);
    }
} 