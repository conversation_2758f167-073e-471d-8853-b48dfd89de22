package com.chaty.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.chaty.entity.GptAskLogEntity;
import com.chaty.service.GptAskLogService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import com.chaty.api.baiduai.QianFanApi;
import com.chaty.dto.ChatCompletionDTO;
import com.chaty.dto.FunctionDTO;
import com.chaty.dto.MessageDTO;
import com.chaty.exception.BaseException;
import com.chaty.service.BasicAiService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import static com.chaty.util.ModelRequestUtil.mergeModelRequest;

@Slf4j
@Service
public class BaiduAiServiceImpl implements BasicAiService {

    @Value("${api.baiduai.clientId}")
    private String clientId;
    @Value("${api.baiduai.clientSecret}")
    private String clientSecret;
    // token 过期时间 29天 官方30天
    @Value("${api.baiduai.authexpire:2505600}")
    private long authExpireSecs;

    private Long authExpireTimestamp;
    private String accessToken;

    private Object lock = new Object();

    @Resource
    private QianFanApi qianFanApi;
    @Resource
    private WebClient qianFanWebClient;
    @Resource
    private GptAskLogService gptAskLogService;
    private Map<String, Object> models = MapUtil
            .builder("ERNIE-Bot", null)
            .put("ERNIE-Bot-turbo", null)
            // .put("Embedding-V1", null)
            .put("BLOOMZ-7B", null)
            .build();

    @Override
    public Map<String, Object> chatForCompletion(ChatCompletionDTO param, GptAskLogEntity gptAskLogEntity) {
        String accessToken = getAccessToken();
        String model = param.getModel();
        // 记录问答开始时间
        gptAskLogEntity.setStartTime(System.currentTimeMillis());

        Map<String, Object> res = null;

        if (Objects.equals(model, "ERNIE-Bot")) {
            res = chatWithErnie(accessToken, param, false, gptAskLogEntity);
        }
        if (Objects.equals(model, "ERNIE-Bot-turbo")) {
            res = chatWithErnie(accessToken, param, true, gptAskLogEntity);
        }
        if (Objects.equals(model, "Embedding-V1")) {
            res = chatWithEbV1(accessToken, param, gptAskLogEntity);
        }
        if (Objects.equals(model, "BLOOMZ-7B")) {
            res = chatWithBloomz7b1(accessToken, param, gptAskLogEntity);
        }

        if (Objects.nonNull(param.getModelRequestObj())) {
            res = mergeModelRequest(res, param);
        }
        return res;
    }

    @Override
    public Map<String, Object> getFinalCompletion(ChatCompletionDTO param) {
        return createApiParam(param);
    }

    public String getAccessToken() {
        long current = System.currentTimeMillis();
        if (Objects.isNull(authExpireTimestamp) || current > authExpireTimestamp) {
            synchronized (lock) {
                if (Objects.isNull(authExpireTimestamp) || current > authExpireTimestamp) {
                    Map<String, Object> res = qianFanApi.getAccessToken(clientId, clientSecret);
                    if (res.containsKey("error")) {
                        throw new RuntimeException(
                                String.format("API授权失败: %s", MapUtil.getStr(res, "error_description")));
                    }
                    this.accessToken = MapUtil.getStr(res, "access_token");
                    this.authExpireTimestamp = current + (authExpireSecs * 1000);
                }
            }
        }
        return accessToken;
    }

    @Override
    public Boolean isSupport(String model) {
        return models.containsKey(model);
    }

    private Map<String, Object> chatWithErnie(String accessToken, ChatCompletionDTO param, boolean isTurbo, GptAskLogEntity gptAskLogEntity) {
        Map<String, Object> apiParam = createApiParam(param);
        Map<String, Object> resp = null;
        if (isTurbo) {
            resp = qianFanApi.chatWithErniePro(accessToken, apiParam);
        } else {
            resp = qianFanApi.chatWithErnie(accessToken, apiParam);
        }
        afterResponse(resp);

        resp.put("$response", resp.get("result"));
        resp.put("$function_call", JSONUtil.getByPath(JSONUtil.parseObj(resp), "function_call"));

        gptAskLogService.add(BasicAiService.getGptAskLogEntityInAsk(apiParam, resp, gptAskLogEntity));

        return resp;
    }

    /**
     * 这玩意不能用
     */
    @Deprecated
    private Map<String, Object> chatWithEbV1(String accessToken, ChatCompletionDTO param, GptAskLogEntity gptAskLogEntity) {
        Map<String, Object> apiParam = new HashMap<String, Object>();
        List<?> input = param.getMessages().stream().map(MessageDTO::getContent).collect(Collectors.toList());
        apiParam.put("input", input);
        if (Objects.nonNull(param.getUserid())) {
            apiParam.put("user_id", param.getUserid());
        }

        Map<String, ?> resp = qianFanApi.chatWithEbV1(accessToken, apiParam);
        afterResponse(resp);

        gptAskLogService.add(BasicAiService.getGptAskLogEntityInAsk(apiParam,(Map<String, Object>) resp, gptAskLogEntity));

        return null;
    }

    private Map<String, Object> chatWithBloomz7b1(String accessToken, ChatCompletionDTO param, GptAskLogEntity gptAskLogEntity) {
        Map<String, Object> apiParam = new HashMap<String, Object>();
        List<Map<String, ?>> messages = param.getMessages().stream().map(m -> {
            return MapUtil.builder("role", m.getRole()).put("content", (String)m.getContent()).build();
        }).collect(Collectors.toList());
        apiParam.put("messages", messages);
        apiParam.put("stream", Optional.ofNullable(param.getStream()).orElse(false));
        if (Objects.nonNull(param.getUserid())) {
            apiParam.put("user_id", param.getUserid());
        }

        Map<String, Object> resp = qianFanApi.chatWithBloomz7b1(accessToken, apiParam);
        afterResponse(resp);

        resp.put("$response", resp.get("result"));

        gptAskLogService.add(BasicAiService.getGptAskLogEntityInAsk(apiParam, resp, gptAskLogEntity));
        return resp;
    }

    private void afterResponse(Map<String, ?> resp) {
        if (resp.containsKey("error_code")) {
            throw new BaseException(
                    String.format("发起对话失败: %s", MapUtil.getStr(resp, "error_msg")));
        }
    }

    @Override
    public Flux<String> streamCompletetion(ChatCompletionDTO param) {
        Map<String, Object> apiParam = createApiParam(param);
        apiParam.put("stream", true);

        String url = getBaseUrl(param.getModel());

        return qianFanWebClient.post()
                .uri(url)
                .contentType(MediaType.APPLICATION_JSON)
                .body(Mono.just(apiParam), Map.class)
                .retrieve()
                .bodyToFlux(String.class)
                .map(s -> {
                    log.info("request openai chat completion, received steam data: {}", s);
                    if (JSONUtil.isTypeJSONObject(s)) {
                        JSONObject parsed = JSONUtil.parseObj(s);                     
                        parsed.set("$end", JSONUtil.getByPath(parsed, "is_end", false));
                        String content = JSONUtil.getByPath(parsed, "result", "");
                        parsed.set("$content", content);
                        return parsed.toString();
                    } else {
                        return s;
                    }
                });
    }

    private String getBaseUrl(String model) {
        if (Objects.equals(model, "ERNIE-Bot")) {
            return String.format("/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions?access_token=%s",
                    getAccessToken());
        }
        if (Objects.equals(model, "ERNIE-Bot-turbo")) {
            return String.format("/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/eb-instant?access_token=%s",
                    getAccessToken());
        }
        throw new BaseException("不支持的模型");
    }

    private Map<String, Object> createApiParam(ChatCompletionDTO param) {
        Map<String, Object> apiParam = new HashMap<String, Object>();
        List<Map<String, ?>> messages = param.getMessages().stream().map(m -> {
            return MapUtil.builder("role", m.getRole()).put("content", (String)m.getContent()).build();
        }).collect(Collectors.toList());
        apiParam.put("messages", messages);
        if (Objects.nonNull(param.getTemperature())) {
            apiParam.put("temperature", param.getTemperature());
        }
        if (Objects.nonNull(param.getTopp())) {
            apiParam.put("top_p", param.getTopp());
        }
        if (Objects.nonNull(param.getPenaltyScore())) {
            apiParam.put("penalty_score", param.getPenaltyScore());
        }
        if (Objects.nonNull(param.getFunctions())) {
            apiParam.put("functions", parseFunctions(param.getFunctions()));
        }
        apiParam.put("stream", Optional.ofNullable(param.getStream()).orElse(false));
        if (Objects.nonNull(param.getUserid())) {
            apiParam.put("user_id", param.getUserid());
        }

        return apiParam;
    }

    private Object parseFunctions(List<FunctionDTO> functions) {
        return functions.stream().map(func -> BeanUtil.beanToMap(func)).collect(Collectors.toList());
    }


}
