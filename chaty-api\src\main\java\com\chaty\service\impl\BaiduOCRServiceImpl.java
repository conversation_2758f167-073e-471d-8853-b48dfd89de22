package com.chaty.service.impl;

import java.util.Arrays;
import java.util.Base64;
import java.util.concurrent.Semaphore;

import javax.annotation.PostConstruct;

import com.baidu.aip.ocr.AipOcr;
import com.chaty.task.correct.SemaphoreManager;
import com.google.common.util.concurrent.RateLimiter;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import com.chaty.exception.BaseException;
import com.chaty.exception.RetryException;
import com.chaty.service.OCRService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Primary
@Service("baiduOCRService")
public class BaiduOCRServiceImpl implements OCRService {

    @Value("${api.baiduocr.appid}")
    private String appId;
    @Value("${api.baiduocr.apiKey}")
    private String apiKey;
    @Value("${api.baiduocr.secretKey}")
    private String secretKey;

    private AipOcr client;

    @PostConstruct
    public void postConstruct() {
        log.info("baidu OCR api configuration: \n appId: {} \n apiKey: {} \n secretKey: {} \n",
                appId, apiKey, secretKey);
        client = new AipOcr(appId, apiKey, secretKey);
    }

    @Override
    public String ocrForText(String url) {
        try {
            // 使用百度OCR接口识别图片
            JSONObject response = client.basicGeneral(url, new java.util.HashMap<>());
            return parseOcrResponse(response);
        } catch (RetryException e) {
            log.error("baidu ocr api error: {}", e.getMessage());
            throw new BaseException("OCR 识别失败", e);
        }
    }

    @Override
    public String ocrForHandwritingText(String base64) throws InterruptedException {
        JSONObject response = null;
        SemaphoreManager semaphoreManager = SemaphoreManager.getInstance();
        RateLimiter rateLimiter = semaphoreManager.getBaiduHandWrittenrateLimiter();
        log.info("start baiduOCRService getQueueLength:{}", rateLimiter.getRate());
        try {
            log.info("star baiduOCRService");
            // 使用百度OCR接口进行手写文字识别
            base64 = base64.replaceFirst("^data:image/jpeg;base64,", "");
            byte[] data = Base64.getDecoder().decode(base64);
            rateLimiter.acquire();
            response = client.handwriting(data, new java.util.HashMap<>());
            log.info("end baiduOCRService: {}", response);
            if (!response.isNull("error_code")) {
                throw new RetryException("手写体 OCR 识别失败");
            }
        } catch (Exception e) {
            log.error("baidu handwriting ocr api error: {}", e.getMessage());
            throw new BaseException("手写体 OCR 识别失败", e);
        } finally {
        }
        return parseOcrResponse(response);
    }

    @Override
    public String ocrForArithmetic(String base64) throws InterruptedException {
        try {
            // 使用百度OCR接口进行算式识别
            JSONObject response = client.basicGeneral(base64, new java.util.HashMap<>());
            return parseOcrResponse(response);
        } catch (RetryException e) {
            log.error("baidu arithmetic ocr api error: {}", e.getMessage());
            throw new BaseException("算式 OCR 识别失败", e);
        } finally {
        }
    }

    // 解析OCR返回结果
    private String parseOcrResponse(JSONObject response) {
        StringBuilder result = new StringBuilder();
        JSONArray wordsArray = response.getJSONArray("words_result");
        for (int i = 0; i < wordsArray.length(); i++) {
            JSONObject word = wordsArray.getJSONObject(i);
            result.append(word.getString("words") + "\n");
        }
        return result.toString();
    }
}
