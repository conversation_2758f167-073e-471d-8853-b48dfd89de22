package com.chaty.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.chaty.entity.DocLibrary;
import com.chaty.mapper.DocLibraryMapper;
import com.chaty.service.DocLibraryService;

import cn.hutool.core.util.IdUtil;

@Service
public class DocLibraryServiceImpl implements DocLibraryService {

    @Resource
    private DocLibraryMapper docLibraryMapper;

    @Override
    public Map<String, Object> add(DocLibrary params) {
        String id = IdUtil.fastSimpleUUID();
        params.setId(id);
        docLibraryMapper.insert(params);
        return Collections.singletonMap("id", id);
    }

    @Override
    public void delete(String id) {
        docLibraryMapper.deleteById(id);
    }

    @Override
    public DocLibrary getById(String id) {
        return docLibraryMapper.selectById(id);
    }

    @Override
    public List<DocLibrary> list(DocLibrary params) {
        return docLibraryMapper.list(params);
    }

    @Override
    public void update(DocLibrary params) {
        String id = params.getId();
        DocLibrary exist = docLibraryMapper.selectById(id);
        if (Objects.nonNull(exist) && Objects.equals(exist.getDeleted(), 1)) {
            params.setDeleted(0);
        }
        docLibraryMapper.updateById(params);
    }

}
