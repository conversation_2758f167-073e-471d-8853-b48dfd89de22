package com.chaty.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("error_correction_time")
public class ErrorCorrectionTime {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    private String fileId;
    
    private String taskId;
    
    private String recordId;
    
    private LocalDateTime createTime;
    
    private LocalDateTime updateTime;
} 