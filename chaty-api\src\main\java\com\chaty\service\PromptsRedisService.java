package com.chaty.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chaty.dto.ModelSettingDTO;
import com.chaty.entity.Prompt;
import com.chaty.entity.PromptChangeLog;
import com.chaty.mapper.PromptChangeLogMapper;
import com.chaty.mapper.PromptMapper;
import com.chaty.service.cache.PromptCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.Collator;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PromptsRedisService {

    private static final String KEY_PREFIX = "prompts:";
    private static final String MODEL_REQUEST_KEY_PREFIX = "modelRequest:";

    private static final String PFX = "prompt:";
    private static final String H = PFX + "h:";               // Hash：prompt:h:{id}
    private static final String ALL = PFX + "all";            // Set：全部 id
    private static final String ZUPD = PFX + "z:update";      // ZSet：按更新时间
    private static String IDX(String f, String v) { return PFX + "i:" + f + ":" + v; }
    private static String IDX_BOOL(String f, boolean b) { return PFX + "i:" + f + ":" + (b ? "1" : "0"); }
    private static String EN_MR(String enLower, String mrKey) { return PFX + "x:enmr:" + enLower + ":" + mrKey; }

    @Resource private RedisTemplate<String, String> redis;
    @Resource private PromptMapper promptMapper;                    // 写入用
    @Resource private PromptChangeLogMapper promptChangeLogMapper;
    @Resource private PromptCacheService promptCacheService;

    private static String norm(String s) { return Optional.ofNullable(s).map(v -> v.trim().toLowerCase()).orElse(""); }
    private static String str(Object o) { return o == null ? "" : String.valueOf(o); }
    private static String mrKey(Integer mrId) { return mrId == null ? "null" : String.valueOf(mrId); }

    /** 通过 (englishName, modelRequestId) 定位 id；未命中则返回 null */
    private Long idByEnAndMr(String englishName, Integer modelRequestId) {
        if (!StringUtils.hasText(englishName)) return null;
        String id = redis.opsForValue().get(EN_MR(norm(englishName), mrKey(modelRequestId)));
        return StringUtils.hasText(id) ? Long.valueOf(id) : null;
    }

    private Map<Object, Object> readHash(Long id) { return redis.opsForHash().entries(H + id); }

    /* ---------------- 批量覆盖（兼容老接口） ---------------- */
    public void setModelRequest(ModelSettingDTO ModelSettingDTO) {
        if (ModelSettingDTO == null || ModelSettingDTO.getId() == null) return;
        deleteModelRequest(ModelSettingDTO.getId());
        JSONObject obj = ModelSettingDTO.getPromptObj();
        if (obj == null) return;
        for (String key : obj.keySet()) {
            String val = String.valueOf(obj.getOrDefault(key, ""));
            redis.opsForValue().set(KEY_PREFIX + MODEL_REQUEST_KEY_PREFIX + ModelSettingDTO.getId() + ":" + key, val);
        }
    }
    public void deleteModelRequest(Integer modelRequestId) {
        if (modelRequestId == null) return;
        Set<String> keys = redis.keys(KEY_PREFIX + MODEL_REQUEST_KEY_PREFIX + modelRequestId + ":*");
        if (keys != null && !keys.isEmpty()) redis.delete(keys);
    }

    /* ---------------- 统一读取（按 englishName） ---------------- */
    public String getAutoByKey(Integer modelRequestId, String englishName) {
        if (!StringUtils.hasText(englishName)) return null;

        Long id = idByEnAndMr(englishName, modelRequestId);
        if (id != null) {
            String v = str(redis.opsForHash().get(H + id, "promptContent"));
            if (!v.isEmpty()) return v;
        }
        Long idDefault = idByEnAndMr(englishName, null);
        if (idDefault != null) {
            String v = str(redis.opsForHash().get(H + idDefault, "promptContent"));
            if (!v.isEmpty()) return v;
        }
        return redis.opsForValue().get(KEY_PREFIX + englishName);
    }

    /* ---------------- 新增：按组合条件获取提示词（优先 modelRequestId） ---------------- */
    /**
     * 根据 (modelRequestId + 类别 + 题型 + 阶段 + 角色 + 是否发送图片) 精确检索提示词内容。
     * 优先命中指定 modelRequestId；若无则回退默认（modelRequestId = null）。
     * 任意入参为空/空串则不参与过滤。
     */
    public String getPromptByMeta(Integer modelRequestId,
                                  String category,
                                  String questionType,
                                  String majorStageType,
                                  String role,
                                  Boolean hasImage) {
        String v = getPromptByMetaInternal(modelRequestId, category, questionType, majorStageType, role, hasImage);
        if (v != null) return v;
        if (modelRequestId != null) {
            return getPromptByMetaInternal(null, category, questionType, majorStageType, role, hasImage);
        }
        return null;
    }

    private String getPromptByMetaInternal(Integer modelRequestId,
                                           String category,
                                           String questionType,
                                           String majorStageType,
                                           String role,
                                           Boolean hasImage) {
        List<String> andKeys = new ArrayList<>();
        andKeys.add(IDX("modelRequestId", mrKey(modelRequestId)));
        if (StringUtils.hasText(category)) andKeys.add(IDX("category", norm(category)));
        if (StringUtils.hasText(questionType)) andKeys.add(IDX("questionType", norm(questionType)));
        if (StringUtils.hasText(majorStageType)) andKeys.add(IDX("majorStageType", norm(majorStageType)));
        if (StringUtils.hasText(role)) andKeys.add(IDX("role", norm(role)));
        if (hasImage != null) andKeys.add(IDX_BOOL("hasImage", hasImage));

        Set<String> base = andKeys.size() == 1
                ? redis.opsForSet().members(andKeys.get(0))
                : redis.opsForSet().intersect(andKeys);
        if (CollectionUtils.isEmpty(base)) return null;

        String pickId = base.stream()
                .max((a, b) -> {
                    Double sa = Optional.ofNullable(redis.opsForZSet().score(ZUPD, a)).orElse(0D);
                    Double sb = Optional.ofNullable(redis.opsForZSet().score(ZUPD, b)).orElse(0D);
                    return Double.compare(sa, sb);
                })
                .orElse(null);
        if (pickId == null) return null;

        Object content = redis.opsForHash().get(H + pickId, "promptContent");
        return content == null ? null : String.valueOf(content);
    }

    /* ---------------- 新增：返回当前 questionType 列表（可筛选） ---------------- */
    /**
     * 去重后的题型列表。默认仅看“默认提示词”（modelRequestId = null）。
     * 你可以附带筛选：isMajorType / category / majorStageType / role / hasImage。
     */
    public List<String> listQuestionTypes(Integer modelRequestId,
                                          Boolean isMajorType,
                                          String category,
                                          String majorStageType,
                                          String role,
                                          Boolean hasImage) {
        // 默认只看默认提示词
        String mr = mrKey(modelRequestId);

        List<String> andKeys = new ArrayList<>();
        andKeys.add(IDX("modelRequestId", mr));
        if (isMajorType != null) andKeys.add(IDX_BOOL("isMajorType", isMajorType));
        if (StringUtils.hasText(category)) andKeys.add(IDX("category", norm(category)));
        if (StringUtils.hasText(majorStageType)) andKeys.add(IDX("majorStageType", norm(majorStageType)));
        if (StringUtils.hasText(role)) andKeys.add(IDX("role", norm(role)));
        if (hasImage != null) andKeys.add(IDX_BOOL("hasImage", hasImage));

        Set<String> base = andKeys.size() == 1
                ? redis.opsForSet().members(andKeys.get(0))
                : redis.opsForSet().intersect(andKeys);
        if (CollectionUtils.isEmpty(base)) return Collections.emptyList();

        // 去重、去空
        Set<String> qs = new HashSet<>();
        for (String id : base) {
            String qt = str(redis.opsForHash().get(H + id, "questionType")).trim();
            if (!qt.isEmpty()) qs.add(qt);
        }

        // 中文友好排序
        List<String> list = new ArrayList<>(qs);
        Collator zh = Collator.getInstance(Locale.SIMPLIFIED_CHINESE);
        list.sort(zh::compare);
        return list;
    }

    /* ---------------- 原有 Getter 与写操作（保留） ---------------- */
    public String getDocCorrect() { return redis.opsForValue().get(KEY_PREFIX + "DOC_CORRECT"); }
    public String getDocCorrectContentTemplate() { return redis.opsForValue().get(KEY_PREFIX + "DOC_CORRECT_CONTENT_TEMPLATE"); }
    public String getDocCorrectV1() { return redis.opsForValue().get(KEY_PREFIX + "DOC_CORRECT_V1"); }
    public String getDocCorrectContentTemplateV1() { return redis.opsForValue().get(KEY_PREFIX + "DOC_CORRECT_CONTENT_TEMPLATE_V1"); }
    public String getExtraQsTemplate() { return redis.opsForValue().get(KEY_PREFIX + "EXTRA_QS_TEMPLATE"); }

    public String getExtraQsRespformatTemplete() { return getAutoByKey(null, "EXTRA_QS_RESPFORMAT_TEMPLETE"); }
    public String getExtraQsRespformatDoubaoSchema() { return getAutoByKey(null, "EXTRA_QS_RESPFORMAT_DOUBAO_SCHEMA"); }
    public String getExtraQsRespformatSchema() { return getAutoByKey(null, "EXTRA_QS_RESPFORMAT_SCHEMA"); }
    public String getDocCorrectV2() { return getAutoByKey(null, "DOC_CORRECT_V2"); }

    public String getDocCorrectResponseFormat(Integer modelRequestId) { return getAutoByKey(modelRequestId, "DOC_CORRECT_RESPONSE_FORMAT"); }
    public String getDocCorrectResponseFormatNormalQsFirstRequest(Integer modelRequestId) { return getAutoByKey(modelRequestId, "DOC_CORRECT_RESPONSE_FORMAT_NORMAL_QS_FIRST_REQUEST"); }
    public String getDocCorrectResponseFormatTemplate(Integer modelRequestId) { return getAutoByKey(modelRequestId, "DOC_CORRECT_RESPONSE_FORMAT_TEMPLATE"); }
    public String getDocCorrectResponseFormatSchema1(Integer modelRequestId) { return getAutoByKey(modelRequestId, "DOC_CORRECT_RESPONSE_FORMAT_SCHEMA1"); }
    public String getDocCorrectResponseFormatSchema2(Integer modelRequestId) { return getAutoByKey(modelRequestId, "DOC_CORRECT_RESPONSE_FORMAT_SCHEMA2"); }
    public String getDocCorrectResponseFormatSchema3(Integer modelRequestId) { return getAutoByKey(modelRequestId, "DOC_CORRECT_RESPONSE_FORMAT_SCHEMA3"); }
    public String getDocCorrectResponseFormatSchema4(Integer modelRequestId) { return getAutoByKey(modelRequestId, "DOC_CORRECT_RESPONSE_FORMAT_SCHEMA4"); }
    public String getDocCorrectResponseFormatSchema5(Integer modelRequestId) { return getAutoByKey(modelRequestId, "DOC_CORRECT_RESPONSE_FORMAT_SCHEMA5"); }

    public String getDocCorrectWriteQsResponseFormat(Integer modelRequestId) { return getAutoByKey(modelRequestId, "DOC_CORRECT_WRITE_QS_RESPONSE_FORMAT"); }
    public String getDocCorrectWriteQsResponseDoubaoFormat(Integer modelRequestId) { return getAutoByKey(modelRequestId, "DOC_CORRECT_WRITE_QS_RESPONSE_DOUBAO_FORMAT"); }
    public String getDocCorrectWriteQsResponseFormat2(Integer modelRequestId) { return getAutoByKey(modelRequestId, "DOC_CORRECT_WRITE_QS_RESPONSE_FORMAT2"); }
    public String getDocCorrectWriteQsResponseDoubaoFormat2(Integer modelRequestId) { return getAutoByKey(modelRequestId, "DOC_CORRECT_WRITE_QS_RESPONSE_DOUBAO_FORMAT2"); }
    public String getDocCorrectWriteQsSystemPrompt(Integer modelRequestId) { return getAutoByKey(modelRequestId, "DOC_CORRECT_WRITE_QS_SYSTEM_PROMPT"); }
    public String getDocCorrectWriteQsSystemPrompt2(Integer modelRequestId) { return getAutoByKey(modelRequestId, "DOC_CORRECT_WRITE_QS_SYSTEM_PROMPT2"); }

    public String getDocCorrectExtractStudentNamePrompt(Integer modelRequestId) { return getAutoByKey(modelRequestId, "DOC_CORRECT_EXTRACT_STUDENT_NAME_PROMPT"); }
    public String getDocCorrectExtractStudentNameFormat(Integer modelRequestId) { return getAutoByKey(modelRequestId, "DOC_CORRECT_EXTRACT_STUDENT_NAME_FORMAT"); }
    public String getDocCorrectExtractStudentNumberPrompt(Integer modelRequestId) { return getAutoByKey(modelRequestId, "DOC_CORRECT_EXTRACT_STUDENT_NUMBER_PROMPT"); }
    public String getDocCorrectExtractStudentNumberFormat(Integer modelRequestId) { return getAutoByKey(modelRequestId, "DOC_CORRECT_EXTRACT_STUDENT_NUMBER_FORMAT"); }

    public String getExtractPaperTopicPrompt() { return getAutoByKey(null, "DOC_EXTRACT_PAPER_TROPIC_PROMPT"); }
    public String getExtractPaperTopicJsonStructure() { return getAutoByKey(null, "DOC_EXTRACT_PAPER_TROPIC_JSON_STRUCTURE"); }

    public String getDocCorrectScoreSystemPrompt(Integer modelRequestId) { return getAutoByKey(modelRequestId, "DOC_CORRECT_SCORE_SYSTEM_PROMPT"); }
    public String getDocCorrectScoreResponseJSONPrompt() { return getAutoByKey(null, "DOC_CORRECT_SCORE_RESPONSE_JSON_PROMPT"); }
    public String getDocCorrectScoreResponseFormat() { return getAutoByKey(null, "DOC_CORRECT_SCORE_RESPONSE_FORMAT"); }

    public String getDocCorrectEssayReportPrompt() { return getAutoByKey(null, "DOC_CORRECT_ESSAY_REPORT_PROMPT"); }
    public String getDocCorrectEssayReportDoubaoPrompt() { return getAutoByKey(null, "DOC_CORRECT_ESSAY_REPORT_DOUBAO_PROMPT"); }
    public String getDocCorrectEssayReportResponseFormat() { return getAutoByKey(null, "DOC_CORRECT_ESSAY_REPORT_RESPONSE_FORMAT"); }

    public String getDoubaoCommonQuestionReturnStructure() { return getAutoByKey(null, "Doubao_Common_Question_Return_Structure"); }

    public String getCommonQuestionByKey(Integer modelRequestId, String key) { return getAutoByKey(modelRequestId, key); }

    public String getCommonQuestion(Integer modelRequestId, String typeName) {
        if (StrUtil.isBlank(typeName)) return getDocCorrectResponseFormat(modelRequestId);
        return findCommonQuestion(modelRequestId, typeName, false);
    }
    public String getCommonQuestionFirstRequest(Integer modelRequestId, String typeName) {
        if (StrUtil.isBlank(typeName)) return getDocCorrectResponseFormatNormalQsFirstRequest(modelRequestId);
        return findCommonQuestion(modelRequestId, typeName, true);
    }
    public String getCommonQuestionSecondRequest(Integer modelRequestId) {
        return getDocCorrectResponseFormatNormalQsFirstRequest(modelRequestId);
    }

    private String findCommonQuestion(Integer modelRequestId, String typeName, boolean first) {
        Set<String> ids = redis.opsForSet().members(ALL);
        if (CollectionUtils.isEmpty(ids)) {
            return first ? getDocCorrectResponseFormatNormalQsFirstRequest(modelRequestId)
                    : getDocCorrectResponseFormat(modelRequestId);
        }
        String prefix = first ? "common_Question_first_request" : "common_Question";

        List<String> matchSameMr = new ArrayList<>();
        List<String> matchDefault = new ArrayList<>();
        String targetMr = mrKey(modelRequestId);

        for (String id : ids) {
            String en = str(redis.opsForHash().get(H + id, "englishName"));
            if (!en.startsWith(prefix)) continue;
            String desc = str(redis.opsForHash().get(H + id, "description"));
            if (!typeName.equals(desc)) continue;

            String mr = str(redis.opsForHash().get(H + id, "modelRequestId"));
            if (targetMr.equals(mr)) matchSameMr.add(id);
            else if ("null".equals(mr)) matchDefault.add(id);
        }

        Comparator<String> byUpdateDesc = (a, b) -> {
            Double sa = Optional.ofNullable(redis.opsForZSet().score(ZUPD, a)).orElse(0D);
            Double sb = Optional.ofNullable(redis.opsForZSet().score(ZUPD, b)).orElse(0D);
            return Double.compare(sb, sa);
        };

        matchSameMr.sort(byUpdateDesc);
        matchDefault.sort(byUpdateDesc);

        String pickId = !matchSameMr.isEmpty() ? matchSameMr.get(0)
                : !matchDefault.isEmpty() ? matchDefault.get(0)
                : null;
        if (pickId == null) {
            return first ? getDocCorrectResponseFormatNormalQsFirstRequest(modelRequestId)
                    : getDocCorrectResponseFormat(modelRequestId);
        }

        String enPick = str(redis.opsForHash().get(H + pickId, "englishName"));
        return getAutoByKey(modelRequestId, enPick);
    }

    public void updateValue(String completeKey, String newValue) {
        if (!StringUtils.hasText(completeKey)) return;
        String keySuffix = completeKey.startsWith(KEY_PREFIX) ? completeKey.substring(KEY_PREFIX.length()) : completeKey;

        Long id = idByEnAndMr(keySuffix, null);
        String oldValue;

        if (id != null) {
            Map<Object, Object> h = readHash(id);
            oldValue = str(h.get("promptContent"));

            LambdaUpdateWrapper<Prompt> uw = Wrappers.<Prompt>lambdaUpdate()
                    .set(Prompt::getPromptContent, newValue == null ? "" : newValue)
                    .eq(Prompt::getId, id);
            promptMapper.update(null, uw);

            Prompt p = new Prompt();
            p.setId(id);
            p.setEnglishName(str(h.get("englishName")));
            p.setDescription(str(h.get("description")));
            p.setName(str(h.get("name")));
            p.setIsMajorType("1".equals(str(h.get("isMajorType"))));
            p.setQuestionType(str(h.get("questionType")));
            p.setCategory(str(h.get("category")));
            p.setPromptContent(newValue == null ? "" : newValue);
            p.setMajorStageType(str(h.get("majorStageType")));
            p.setTags(str(h.get("tags")));
            p.setRole(str(h.get("role")));
            p.setHasImage("1".equals(str(h.get("hasImage"))));
            p.setModelRequestId(null);
            p.setUpdateTime(LocalDateTime.now());
            try { promptCacheService.index(p); } catch (Exception ignore) {}
        } else {
            oldValue = redis.opsForValue().get(KEY_PREFIX + keySuffix);
            redis.opsForValue().set(KEY_PREFIX + keySuffix, newValue == null ? "" : newValue);
        }

        try {
            PromptChangeLog log = new PromptChangeLog();
            log.setPromptKey(completeKey);
            log.setOldValue(oldValue);
            log.setNewValue(newValue);
            promptChangeLogMapper.insert(log);
        } catch (Exception e) {
            log.error("更新prompt日志失败: {}", e.getMessage());
        }
    }

    public void resetValue(String completeKey) {
        if (!StringUtils.hasText(completeKey)) return;
        String keySuffix = completeKey.startsWith(KEY_PREFIX) ? completeKey.substring(KEY_PREFIX.length()) : completeKey;

        String oldOverride = redis.opsForValue().get(KEY_PREFIX + keySuffix);
        String defaultFromCache = null;

        Long id = idByEnAndMr(keySuffix, null);
        if (id != null) defaultFromCache = str(redis.opsForHash().get(H + id, "promptContent"));

        if (defaultFromCache != null) redis.opsForValue().set(KEY_PREFIX + keySuffix, defaultFromCache);
        else redis.delete(KEY_PREFIX + keySuffix);

        try {
            PromptChangeLog log = new PromptChangeLog();
            log.setPromptKey(completeKey);
            log.setOldValue(oldOverride);
            log.setNewValue(defaultFromCache);
            promptChangeLogMapper.insert(log);
        } catch (Exception e) {
            log.error("重置prompt日志失败: {}", e.getMessage());
        }
    }

    public Map<String, Map<String, String>> getAllKeyListWithChineseNames() {
        Map<String, Map<String, String>> result = new LinkedHashMap<>();
        Set<String> ids = redis.opsForSet().members(ALL);
        if (CollectionUtils.isEmpty(ids)) return result;

        for (String id : ids) {
            String mr = str(redis.opsForHash().get(H + id, "modelRequestId"));
            if (!"null".equals(mr)) continue;
            String en = str(redis.opsForHash().get(H + id, "englishName"));
            if (en.isEmpty()) continue;

            Map<String, String> meta = new HashMap<>(4);
            meta.put("key", KEY_PREFIX + en);
            meta.put("desc", str(redis.opsForHash().get(H + id, "description")));
            meta.put("type", str(redis.opsForHash().get(H + id, "category")));
            meta.put("value", str(redis.opsForHash().get(H + id, "promptContent")));
            result.put(KEY_PREFIX + en, meta);
        }
        return result;
    }

    public List<String> listAllCommonQsName() {
        Set<String> ids = redis.opsForSet().members(ALL);
        if (CollectionUtils.isEmpty(ids)) return Collections.emptyList();
        return ids.stream()
                .map(id -> {
                    String mr = str(redis.opsForHash().get(H + id, "modelRequestId"));
                    if (!"null".equals(mr)) return null;
                    String en = str(redis.opsForHash().get(H + id, "englishName"));
                    String desc = str(redis.opsForHash().get(H + id, "description"));
                    return en.startsWith("common_Question_first_request") ? desc : null;
                })
                .filter(s -> s != null && !s.isEmpty())
                .distinct()
                .collect(Collectors.toList());
    }

    public Map<String, Map<String, String>> listAutoByKeyNameAndType() {
        Map<String, Map<String, String>> result = new LinkedHashMap<>();
        Set<String> ids = redis.opsForSet().members(ALL);
        if (CollectionUtils.isEmpty(ids)) return result;

        for (String id : ids) {
            String mr = str(redis.opsForHash().get(H + id, "modelRequestId"));
            if (!"null".equals(mr)) continue;
            String en = str(redis.opsForHash().get(H + id, "englishName"));
            if (en.isEmpty()) continue;
            Map<String, String> info = new HashMap<>(3);
            info.put("name", str(redis.opsForHash().get(H + id, "description")));
            info.put("type", str(redis.opsForHash().get(H + id, "category")));
            info.put("value", str(redis.opsForHash().get(H + id, "promptContent")));
            result.put(en, info);
        }
        return result;
    }

    public Map<String, String> getAllKeyList() {
        Map<String, String> result = new LinkedHashMap<>();
        Set<String> ids = redis.opsForSet().members(ALL);
        if (!CollectionUtils.isEmpty(ids)) {
            for (String id : ids) {
                String mr = str(redis.opsForHash().get(H + id, "modelRequestId"));
                if (!"null".equals(mr)) continue;
                String en = str(redis.opsForHash().get(H + id, "englishName"));
                if (en.isEmpty()) continue;
                String val = str(redis.opsForHash().get(H + id, "promptContent"));
                result.put(en, val);
            }
        }
        Set<String> keys = redis.keys(KEY_PREFIX + "*");
        if (keys != null) {
            for (String k : keys) {
                if (k.startsWith(KEY_PREFIX + MODEL_REQUEST_KEY_PREFIX)) continue;
                String suffix = k.substring(KEY_PREFIX.length());
                result.putIfAbsent(suffix, redis.opsForValue().get(k));
            }
        }
        return result;
    }
}
