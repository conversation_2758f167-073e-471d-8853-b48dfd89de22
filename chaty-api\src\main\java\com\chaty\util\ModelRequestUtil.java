package com.chaty.util;

import com.chaty.dto.ChatCompletionDTO;

import java.util.Map;

public class ModelRequestUtil {

    public static Map<String, Object> mergeModelRequest(Map<String, Object> res, ChatCompletionDTO param) {
        Map<String, Object> reqObj = param.getModelRequestObj();
        if (res != null && reqObj != null) {
            res.putAll(reqObj);
        }
        return res;
    }
}
