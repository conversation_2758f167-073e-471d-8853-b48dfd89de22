package com.chaty.controller;

import com.chaty.common.BaseResponse;
import com.chaty.dto.ErrorCorrectionTimeDTO;
import com.chaty.service.ErrorCorrectionTimeService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/errorCorrectionTime")
public class ErrorCorrectionTimeController {

    @Resource
    private ErrorCorrectionTimeService errorCorrectionTimeService;

    @PostMapping("/add")
    public BaseResponse<?> add(@RequestBody ErrorCorrectionTimeDTO dto) {
        ErrorCorrectionTimeDTO result = errorCorrectionTimeService.add(dto);
        return BaseResponse.ok(result);
    }

    @GetMapping("/statisticsByFileId")
    public BaseResponse<List<Map<String, Object>>> statisticsByFileId(@RequestParam String fileId) {
        List<Map<String, Object>> times = errorCorrectionTimeService.getStatisticsByFileIds(Arrays.asList(fileId));
        return BaseResponse.ok(times);
    }
} 