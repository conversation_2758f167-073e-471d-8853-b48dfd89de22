package com.chaty.task.correct;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import com.chaty.dto.DocCorrectConfigDTO;
import com.chaty.dto.DocCorrectRecordDTO;

import cn.hutool.json.JSONArray;

@Component
public class CorrectCacheService {

    private final String CACHE_KEY = "CORRECT_RECORD:";

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    public String getRecordKey(String id) {
        return CACHE_KEY + id;
    }

    public void onCorrectRecord(DocCorrectRecordDTO record) {
        String cacheKey = getRecordKey(record.getId());
        DocCorrectConfigDTO config = record.getConfig();
        JSONArray areasObj = config.getAreasObj();
        // 记录缓存
        redisTemplate.opsForHash().put(cacheKey, "areaNum", areasObj.size());
        redisTemplate.opsForHash().put(cacheKey, "areaCorrected", 0);
    }

    public void onAreaCorrected(DocCorrectRecordDTO record) {
        String cacheKey = getRecordKey(record.getId());
        redisTemplate.opsForHash().increment(cacheKey, "areaCorrected", 1);
    }

    public void onRecordCorrected(DocCorrectRecordDTO record) {
        String cacheKey = getRecordKey(record.getId());
        redisTemplate.delete(cacheKey);
    }

    public Map<String, Object> getRecordCache(String id) {
        String cacheKey = getRecordKey(id);
        return (Map) redisTemplate.opsForHash().entries(cacheKey);
    }

    public void onRpmRecorded(String taskId, int count) {
        long now = System.currentTimeMillis() / 1000;
        String key = "CORRECT_RECORD:rpm:zset";

        // 声明成 Object 泛型
        Set<ZSetOperations.TypedTuple<Object>> tuples = new HashSet<>(count);
        for (int i = 0; i < count; i++) {
            String member = taskId + ":" + UUID.randomUUID();
            tuples.add(new DefaultTypedTuple<Object>( (Object) member, (double) now ));
        }

        // 这时就能匹配到 add(String, Set<TypedTuple<Object>>())
        redisTemplate.opsForZSet().add(key, tuples);

        // 清理过期记录
        long expireBefore = now - 30L * 24 * 3600;
        redisTemplate.opsForZSet().removeRangeByScore(key, 0, expireBefore);
    }


    /**
     * 获取近 60 秒该 taskId 的 rpm 数量
     */
    public Long getCurrentRpmForTask(String taskId) {
        long now = System.currentTimeMillis() / 1000;
        long start = now - 60;
        String key = "CORRECT_RECORD:rpm:zset";

        // 取出所有近60秒的数据，再按 taskId 前缀过滤
        Set<Object> rawMembers = redisTemplate.opsForZSet().rangeByScore(key, start, now);
        Set<String> members = rawMembers.stream()
                .map(Object::toString) // 或者直接强转：obj -> (String) obj
                .collect(Collectors.toSet());

        return members.stream().filter(m -> m.startsWith(taskId + ":")).count();
    }

    /**
     * 获取近 60 秒所有 taskId 的 rpm 总和
     */
    public Long getCurrentRpmForAllTasks() {
        long now = System.currentTimeMillis() / 1000;
        long start = now - 60;
        String key = "CORRECT_RECORD:rpm:zset";

        return redisTemplate.opsForZSet().count(key, start, now);
    }

}
