package com.chaty.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.dto.PromptDTO;
import com.chaty.entity.Prompt;
import com.chaty.entity.PromptChangeLog;
import com.chaty.mapper.PromptChangeLogMapper;
import com.chaty.mapper.PromptMapper;
import com.chaty.service.PromptService;
import com.chaty.service.cache.PromptCacheService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

@Service
@RequiredArgsConstructor
public class PromptServiceImpl extends ServiceImpl<PromptMapper, Prompt> implements PromptService {

    private final PromptChangeLogMapper promptChangeLogMapper;
    private final PromptCacheService promptCacheService;

    @Override
    public IPage<Prompt> page(PromptDTO param) {
        return promptCacheService.page(param);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(Prompt param) {
        // 标签默认值
        if (!StringUtils.hasText(param.getTags())) {
            param.setTags("string");
        }
        // DB
        this.save(param);
        Prompt saved = this.getById(param.getId());

        // 日志
        saveLog(resolvePromptKey(saved), null, saved.getPromptContent());

        // 缓存索引
        promptCacheService.index(saved);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Prompt param) {
        // 读取旧值用于日志 & 合并 tags 默认策略
        Prompt old = this.getById(param.getId());
        if (param.getTags() == null) {
            // 未传则沿用旧值
            param.setTags(old == null ? "string" : (StringUtils.hasText(old.getTags()) ? old.getTags() : "string"));
        } else if (!StringUtils.hasText(param.getTags())) {
            // 传了空串则默认 string
            param.setTags("string");
        }

        this.updateById(param);
        Prompt saved = this.getById(param.getId());

        // 日志
        saveLog(resolvePromptKey(saved, old), old == null ? null : old.getPromptContent(), saved.getPromptContent());

        // 缓存索引
        promptCacheService.index(saved);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        Long lid = Long.valueOf(id);
        this.removeById(lid);
        promptCacheService.remove(lid);
    }

    @Override
    public void rebuildCache() {
        promptCacheService.rebuildAll();
    }

    // ---------- helpers ----------

    private void saveLog(String promptKey, String oldValue, String newValue) {
        if (!StringUtils.hasText(promptKey)) return;
        PromptChangeLog log = new PromptChangeLog();
        log.setPromptKey(promptKey);
        log.setOldValue(oldValue);
        log.setNewValue(newValue);
        promptChangeLogMapper.insert(log);
    }

    private String resolvePromptKey(Prompt cur) {
        return resolvePromptKey(cur, null);
    }
    private String resolvePromptKey(Prompt cur, Prompt old) {
        if (cur != null && cur.getId() != null) return String.valueOf(cur.getId());
        if (cur != null && StringUtils.hasText(cur.getEnglishName())) return cur.getEnglishName();
        if (cur != null && StringUtils.hasText(cur.getName())) return cur.getName();
        if (old != null && StringUtils.hasText(old.getEnglishName())) return old.getEnglishName();
        if (old != null && StringUtils.hasText(old.getName())) return old.getName();
        return null;
    }
}
