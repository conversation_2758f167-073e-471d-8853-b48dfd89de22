package com.chaty.task.correct;

import com.chaty.dto.DocCorrectRecordDTO;
import com.chaty.dto.DocCorrectTaskDTO;
import com.chaty.entity.DocCorrectRecord;

public interface RecordCorrector {

    void correct(DocCorrectRecord record);

    /**
     * 用于重新识别姓名/学号
     * @param record
     * @param type
     */
    void identifyNameOrStudentNumberType(DocCorrectRecord record, String type);

    String ocrForIdentify(DocCorrectRecordDTO record);

    void correctDocName(DocCorrectTaskDTO param);

    void cancelCorrection(String recordId);

    void deleteCorrect(String taskId);
}
