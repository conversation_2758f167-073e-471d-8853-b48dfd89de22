package com.chaty.api.openai;

import java.nio.charset.StandardCharsets;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import feign.Request;
import feign.RequestTemplate;

public class OpenaiUtil {
    
    public static JSONObject getRequestObj(RequestTemplate template) {
        return JSONUtil.parseObj(new String(template.body(), StandardCharsets.UTF_8));
    }

    public static void setRequestBody(RequestTemplate template, JSONObject bodyObj) {
        template.body(JSONUtil.toJsonStr(bodyObj));
    }

    public static String getApiKey(Request request) {
        return request.headers().get("Authorization").iterator().next().substring(7);
    }

}
