package com.chaty.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@Data
@TableName("doc_correct_task")
public class DocCorrectTask {

    @TableId
    private String id;

    private String name;

    private Integer status;

    private String configId;

    private String correctConfig;

    private String fileId;

    @TableField(fill = FieldFill.INSERT, updateStrategy = FieldStrategy.NEVER)
    private String creator;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @TableLogic
    private Integer deleted;

    private String similarPaperId;

    /**
     * 是否是作文 类型，0是否，1 true
     */
    private Integer isEssay;

    private String tenantId;

    private Integer offsetX;

    private Integer offsetY;

    private Integer modelRequestId;
}
