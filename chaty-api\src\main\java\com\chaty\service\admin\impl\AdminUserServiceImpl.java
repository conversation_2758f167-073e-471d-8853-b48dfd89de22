package com.chaty.service.admin.impl;

import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.dto.AdminUserDTO;
import com.chaty.entity.admin.AdminUser;
import com.chaty.exception.BaseException;
import com.chaty.mapper.admin.AdminUserMapper;
import com.chaty.service.admin.AdminUserService;

@Service
public class AdminUserServiceImpl extends ServiceImpl<AdminUserMapper, AdminUser> implements AdminUserService {

    @Resource
    private PasswordEncoder passwordEncoder;

    @Override
    public AdminUser login(AdminUserDTO params) {
        if (Objects.isNull(params.getUsername()) || Objects.isNull(params.getPassword())) {
            throw new BaseException("用户名或密码不能为空");
        }
        AdminUser user = this.lambdaQuery().eq(AdminUser::getUsername, params.getUsername()).one();
        if (Objects.isNull(user)) {
            throw new BaseException("用户名或密码错误");
        }
        if (!passwordEncoder.matches(params.getPassword(), user.getPassword())) {
            throw new BaseException("用户名或密码错误");
        }
        return user;
    }

}
