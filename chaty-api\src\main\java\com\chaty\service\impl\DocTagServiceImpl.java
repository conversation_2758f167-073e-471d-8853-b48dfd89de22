package com.chaty.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.dto.DocTagDTO;
import com.chaty.entity.DocTag;
import com.chaty.entity.DocTagRecord;
import com.chaty.mapper.DocTagMapper;
import com.chaty.mapper.DocTagRecordMapper;
import com.chaty.service.DocTagRecordService;
import com.chaty.service.DocTagService;

import cn.hutool.core.collection.CollUtil;

@Service
public class DocTagServiceImpl extends ServiceImpl<DocTagMapper, DocTag> implements DocTagService {

    @Resource
    private DocTagMapper docTagMapper;
    @Resource
    private DocTagRecordMapper docTagRecordMapper;
    @Resource
    private DocTagRecordService docTagRecordService;

    @Transactional
    @Override
    public void addTag(DocTagDTO params) {
        docTagMapper.insert(params);
        setTag(params);
    }

    @Transactional
    @Override
    public void deleteTag(DocTagDTO params) {
        docTagMapper.deleteById(params.getId());
        docTagRecordMapper.delete(Wrappers.lambdaQuery(DocTagRecord.class).eq(DocTagRecord::getTagId, params.getId()));
    }

    @Override
    public List<DocTag> list(DocTagDTO params) {
        Wrapper<DocTag> wrapper = Wrappers.lambdaQuery(DocTag.class)
                .like(Objects.nonNull(params.getName()), DocTag::getName, params.getName());
        return docTagMapper.selectList(wrapper);
    }

    @Override
    public Map<String, List<DocTagDTO>> listByDocs(DocTagDTO params) {
        QueryWrapper<?> wrapper = Wrappers.query()
                .nested(i -> i.in(CollUtil.isNotEmpty(params.getTaskIds()), "dtr.task_id", params.getTaskIds())
                        .or()
                        .in(CollUtil.isNotEmpty(params.getDocIds()), "dtr.doc_id", params.getDocIds()));
        List<DocTagDTO> tags = docTagMapper.listByDocs(wrapper);
        return tags.stream().collect(Collectors
                .groupingBy(tag -> Optional.ofNullable(tag.getDocId()).orElse(tag.getTaskId()), Collectors.toList()));
    }

    @Transactional
    @Override
    public void setTag(DocTagDTO params) {
        if (Objects.nonNull(params.getDocIds()) && !params.getDocIds().isEmpty()) {
            List<DocTagRecord> records = params.getDocIds().stream().map(docId -> {
                DocTagRecord docTagRecord = new DocTagRecord();
                docTagRecord.setDocId(docId);
                docTagRecord.setTagId(params.getId());
                return docTagRecord;
            }).collect(Collectors.toList());
            docTagRecordService.saveBatch(records);
        }
        if (Objects.nonNull(params.getTaskIds()) && !params.getTaskIds().isEmpty()) {
            List<DocTagRecord> records = params.getTaskIds().stream().map(taskId -> {
                DocTagRecord docTagRecord = new DocTagRecord();
                docTagRecord.setTaskId(taskId);
                docTagRecord.setTagId(params.getId());
                return docTagRecord;
            }).collect(Collectors.toList());
            docTagRecordService.saveBatch(records);
        }
    }

    @Override
    public void deleteDocTag(DocTagDTO params) {
        docTagRecordMapper.delete(Wrappers.lambdaQuery(DocTagRecord.class)
                .in(DocTagRecord::getTagId, params.getIds())
                .nested(i -> i
                        .eq(Objects.nonNull(params.getTaskId()), DocTagRecord::getTaskId, params.getTaskId())
                        .or()
                        .eq(Objects.nonNull(params.getDocId()), DocTagRecord::getDocId, params.getDocId())));
    }

}
