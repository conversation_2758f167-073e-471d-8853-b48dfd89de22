package com.chaty.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.entity.ModelSetting;
import com.chaty.entity.TestSetImageEntity;
import com.chaty.entity.TestSetImageResultEntity;
import com.chaty.enums.TestSetResultStatus;
import com.chaty.mapper.ModelRequestMapper;
import com.chaty.mapper.TestSetImageMapper;
import com.chaty.mapper.TestSetImageResultMapper;
import com.chaty.service.TestSetImageResultService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
public class TestSetImageResultServiceImpl
        extends ServiceImpl<TestSetImageResultMapper, TestSetImageResultEntity>
        implements TestSetImageResultService {

    @Resource
    private TestSetImageResultMapper resultMapper;
    @Resource
    private TestSetImageMapper imageMapper;
    @Resource
    private ModelRequestMapper modelSettingMapper;

    @Override
    public IPage<TestSetImageResultEntity> page(Integer pageNumber, Integer pageSize,
                                                Long testSetId, Long imageId,
                                                String questionType, TestSetResultStatus status,
                                                Boolean success) {
        Page<TestSetImageResultEntity> page = new Page<>(
                pageNumber == null ? 1 : pageNumber,
                pageSize == null ? 10 : pageSize
        );
        LambdaQueryWrapper<TestSetImageResultEntity> qw = new LambdaQueryWrapper<>();
        if (testSetId != null) qw.eq(TestSetImageResultEntity::getTestSetId, testSetId);
        if (imageId != null) qw.eq(TestSetImageResultEntity::getImageId, imageId);
        if (questionType != null && !questionType.isEmpty())
            qw.eq(TestSetImageResultEntity::getQuestionType, questionType);
        if (status != null) qw.eq(TestSetImageResultEntity::getStatus, status);
        if (success != null) qw.eq(TestSetImageResultEntity::getSuccess, success);
        qw.orderByDesc(TestSetImageResultEntity::getCreateTime);
        return this.page(page, qw);
    }

    @Override
    public Long add(TestSetImageResultEntity param) {
        // 计数与状态兜底
        if (param.getCorrectCount() == null) param.setCorrectCount(0);
        if (param.getTotalCount() == null) param.setTotalCount(0);
        if (param.getSuccess() == null) param.setSuccess(Boolean.FALSE);
        if (param.getStatus() == null) param.setStatus(TestSetResultStatus.PROCESSING);

        // ===== 自动补齐 图片信息快照 =====
        if (param.getImageId() != null) {
            TestSetImageEntity img = imageMapper.selectById(param.getImageId());
            if (img != null) {
                if (param.getTestSetId() == null) param.setTestSetId(img.getTestSetId());
                if (param.getImgUrl() == null) param.setImgUrl(img.getImgUrl());
                if (param.getRightAnswer() == null) param.setRightAnswer(img.getRightAnswer());
                if (param.getQuestionDetail() == null) param.setQuestionDetail(img.getQuestionDetail());
                if (param.getQuestionType() == null) param.setQuestionType(img.getQuestionType());
                if (param.getUseQuestionDetail() == null) param.setUseQuestionDetail(
                        img.getUseQuestionDetail() == null ? Boolean.TRUE : img.getUseQuestionDetail()
                );
                if (param.getFromRecordId() == null) param.setFromRecordId(img.getFromRecordId());
                if (param.getFromRecordAreaIdx() == null) param.setFromRecordAreaIdx(img.getFromRecordAreaIdx());
            }
        }

        // ===== 自动补齐 模型参数快照 =====
        if (param.getModelSettingId() != null &&
                (param.getMsName() == null && param.getMsModelValue() == null)) {
            ModelSetting ms = modelSettingMapper.selectById(param.getModelSettingId());
            if (ms != null) {
                param.setMsName(ms.getName());
                param.setMsModelValue(ms.getModelValue());
                param.setMsJsonobject(ms.getJsonobject());
                param.setMsJsonschema(ms.getJsonschema());
                param.setMsEnableNormalQsTwoRequest(ms.getEnableNormalQsTwoRequest());
                param.setMsContent(ms.getContent());
                param.setMsRemark(ms.getRemark());
                param.setMsWeight(ms.getWeight());
                param.setMsPrompt(ms.getPrompt());
                param.setMsIsSecondRoundUseImage(ms.getIsSecondRoundUseImage());
                param.setMsIsSecondRoundJsonComparison(ms.getIsSecondRoundJsonComparison());
                param.setMsEnableImageEnhancement(ms.getEnableImageEnhancement());
                param.setMsFirstRoundPromptType(ms.getFirstRoundPromptType());
                param.setMsSecondRoundPromptType(ms.getSecondRoundPromptType());
                param.setMsSingleRoundPromptType(ms.getSingleRoundPromptType());
                param.setMsDisabled(ms.getDisabled());
                param.setMsQuestionType(ms.getQuestionType());
                // 转成 Date（快照）
                if (ms.getCreateTime() != null)
                    param.setMsCreateTime(java.sql.Timestamp.valueOf(ms.getCreateTime()));
                if (ms.getUpdateTime() != null)
                    param.setMsUpdateTime(java.sql.Timestamp.valueOf(ms.getUpdateTime()));
            }
        }

        this.save(param);
        return param.getId();
    }

    @Override
    public Long updateOne(TestSetImageResultEntity param) {
        // 一般不覆盖历史快照；按需仅更新结果字段/状态/日志ID/报告等
        this.updateById(param);
        return param.getId();
    }

    @Override
    public void deleteById(Long id) {
        if (id != null) this.removeById(id);
    }
}
