package com.chaty.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.entity.FtpFiles;
import com.chaty.dto.FtpFilesRemarkDTO;
import com.chaty.entity.FtpMessage;
import com.chaty.exception.BaseException;
import com.chaty.mapper.FtpFilesMapper;
import com.chaty.mapper.FtpMessageMapper;
import com.chaty.service.FtpFilesService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.io.File;

@Slf4j
@Service
public class FtpFilesServiceImpl extends ServiceImpl<FtpFilesMapper, FtpFiles> implements FtpFilesService {

    @Autowired
    private FtpMessageMapper ftpMessageMapper;

    @Override
    @Transactional
    public FtpFilesRemarkDTO addRemark(FtpFilesRemarkDTO dto) {
        if (StrUtil.isBlank(dto.getPath()) || StrUtil.isBlank(dto.getType()) || StrUtil.isBlank(dto.getFilename())) {
            throw new BaseException("path, type, and filename are required for adding");
        }

        FtpFiles ftpFile = new FtpFiles();
        BeanUtils.copyProperties(dto, ftpFile);

        if (Objects.nonNull(ftpFile.getId())) {
            ftpFile.setId(null);
        }
        save(ftpFile);

        updateFtpMessageCompletionStatus(ftpFile.getPath(), ftpFile.getFilename(), dto.getFileId(), dto.getConfigPackageId());

        FtpFilesRemarkDTO result = new FtpFilesRemarkDTO();
        BeanUtils.copyProperties(ftpFile, result);
        return result;
    }

    @Override
    @Transactional
    public FtpFilesRemarkDTO updateRemark(FtpFilesRemarkDTO dto) {
        if (dto.getId() == null) {
            throw new BaseException("id is required for updating");
        }

        FtpFiles ftpFile = getById(dto.getId());
        if (ftpFile == null) {
            throw new BaseException("FTP file not found");
        }

        BeanUtils.copyProperties(dto, ftpFile, "id", "createTime", "updateTime");

        updateById(ftpFile);

        updateFtpMessageCompletionStatus(ftpFile.getPath(), ftpFile.getFilename(), dto.getFileId(), dto.getConfigPackageId());

        FtpFilesRemarkDTO result = new FtpFilesRemarkDTO();
        BeanUtils.copyProperties(ftpFile, result);
        return result;
    }

    private void updateFtpMessageCompletionStatus(String ftpFilesPath, String ftpFilesFilename, String fileId, String configPackageId) {
        if (StrUtil.isNotBlank(fileId)) {
            LambdaUpdateWrapper<FtpMessage> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(FtpMessage::getFileCompleted, true)
                    .eq(FtpMessage::getFilePath, ftpFilesPath + "/" + ftpFilesFilename);

            int rowsAffected = ftpMessageMapper.update(null, updateWrapper);
            log.info("Updated FtpMessage file_completed for path: {} and filename: {}. Rows affected: {}", ftpFilesPath, ftpFilesFilename, rowsAffected);
            if (rowsAffected == 0) {
                log.warn("No FtpMessage records found matching path: {} and filename: {} for file_completed update.", ftpFilesPath, ftpFilesFilename);
            }
        }

        if (StrUtil.isNotBlank(configPackageId)) {
            LambdaUpdateWrapper<FtpMessage> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(FtpMessage::getConfigPackageCompleted, true)
                    .eq(FtpMessage::getFilePath, ftpFilesPath + "/" + ftpFilesFilename);

            int rowsAffected = ftpMessageMapper.update(null, updateWrapper);
            log.info("Updated FtpMessage config_package_completed for path: {} and filename: {}. Rows affected: {}", ftpFilesPath, ftpFilesFilename, rowsAffected);
            if (rowsAffected == 0) {
                log.warn("No FtpMessage records found matching path: {} and filename: {} for config_package_completed update.", ftpFilesPath, ftpFilesFilename);
            }
        }
    }
} 