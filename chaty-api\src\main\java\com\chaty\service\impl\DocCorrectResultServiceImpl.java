package com.chaty.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.dto.DocCorrectConfigDTO;
import com.chaty.dto.DocCorrectRecordDTO;
import com.chaty.dto.DocCorrectResultDTO;
import com.chaty.dto.DocCorrectTaskDTO;
import com.chaty.entity.DocCorrectConfig;
import com.chaty.entity.DocCorrectRecord;
import com.chaty.entity.DocCorrectResult;
import com.chaty.entity.DocCorrectTask;
import com.chaty.entity.User;
import com.chaty.enums.UserRoleConsts;
import com.chaty.exception.BaseException;
import com.chaty.mapper.DocCorrectConfigMapper;
import com.chaty.mapper.DocCorrectRecordMapper;
import com.chaty.mapper.DocCorrectResultMapper;
import com.chaty.mapper.DocCorrectTaskMapper;
import com.chaty.security.AuthUtil;
import com.chaty.service.DocCorrectRecordService;
import com.chaty.service.DocCorrectResultService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import lombok.Synchronized;

@Service
public class DocCorrectResultServiceImpl extends ServiceImpl<DocCorrectResultMapper, DocCorrectResult>
        implements DocCorrectResultService {

    @Resource
    private DocCorrectResultMapper docCorrectResultMapper;
    @Resource
    private DocCorrectTaskMapper docCorrectTaskMapper;
    @Resource
    private DocCorrectConfigMapper docCorrectConfigMapper;
    @Resource
    private DocCorrectRecordMapper docCorrectRecordMapper;
    @Resource
    private DocCorrectRecordService docCorrectRecordService;

    @Override
    public void saveByRecord(DocCorrectRecordDTO record) {
        DocCorrectConfigDTO config = record.getConfig();
        JSONArray areas = config.getAreasObj();
        DocCorrectTaskDTO task = record.getTask();
        saveByAreas(areas, Collections.singletonList(record), task, config);
        // 计算题目正确率
        docCorrectResultMapper.setCorrectRateByTaskId(task.getId());
    }

    @Override
    public void saveByTask(String taskId) {
        DocCorrectTask task = docCorrectTaskMapper.selectById(taskId);
        if (Objects.isNull(task)) {
            throw new BaseException("未查询到任务信息");
        }
        DocCorrectTaskDTO taskDTO = BeanUtil.copyProperties(task, DocCorrectTaskDTO.class);
        DocCorrectConfig config = docCorrectConfigMapper.selectById(task.getConfigId());
        if (Objects.isNull(config)) {
            throw new BaseException("未查询到配置信息");
        }
        DocCorrectConfigDTO configDTO = BeanUtil.toBean(config, DocCorrectConfigDTO.class);
        List<DocCorrectRecord> records = docCorrectRecordService.selectByTaskId(taskId, null);
        List<DocCorrectRecordDTO> recordDTOs = BeanUtil.copyToList(records, DocCorrectRecordDTO.class);
        saveByAreas(configDTO.getAreasObj(), recordDTOs, taskDTO, configDTO);
        // 计算题目正确率
        docCorrectResultMapper.setCorrectRateByTaskId(task.getId());
    }

    @Override
    public void saveByRecordId(String recordId) {
        DocCorrectRecord record = docCorrectRecordMapper.selectById(recordId);
        if (Objects.isNull(record)) {
            throw new BaseException("未查询到记录信息");
        }
        DocCorrectRecordDTO recordDTO = BeanUtil.copyProperties(record, DocCorrectRecordDTO.class);
        DocCorrectConfig config = docCorrectConfigMapper.selectById(record.getConfigId());
        if (Objects.isNull(config)) {
            throw new BaseException("未查询到配置信息");
        }
        DocCorrectConfigDTO configDTO = BeanUtil.toBean(config, DocCorrectConfigDTO.class);
        DocCorrectTask task = docCorrectTaskMapper.selectById(record.getTaskId());
        if (Objects.isNull(task)) {
            throw new BaseException("未查询到任务信息");
        }
        DocCorrectTaskDTO taskDTO = BeanUtil.copyProperties(task, DocCorrectTaskDTO.class);
        saveByAreas(configDTO.getAreasObj(), Collections.singletonList(recordDTO), taskDTO, configDTO);
    }

    public void saveByAreas(JSONArray areaObj, List<DocCorrectRecordDTO> records, DocCorrectTaskDTO task, DocCorrectConfigDTO config) {
        records.forEach(record -> {
            List<DocCorrectResult> results = new ArrayList<>();
            JSONArray reviewed = record.getReviewedObj();
            for (int areaIdx = 0; areaIdx < areaObj.size(); areaIdx++) {
                JSONObject area = areaObj.getJSONObject(areaIdx);
                JSONArray questions = Optional.ofNullable(area.getJSONArray("questions")).orElse(new JSONArray());
                Integer isScorePoint = area.getInt("isScorePoint", 0);
                JSONObject  reviewArea = Optional.ofNullable(reviewed.getJSONObject(areaIdx)).orElse(new JSONObject());
                String areaImg = reviewArea.getStr("areaImg");
                String reviewStatus = reviewArea.getStr("status");
                JSONArray reviewedQss = Optional.ofNullable(reviewArea.getJSONArray("reviewed")).orElse(new JSONArray());
                for (int qsIdx = 0; qsIdx < questions.size(); qsIdx ++) {
                    JSONObject qs = questions.getJSONObject(qsIdx);
                    JSONObject reviewedQs = Optional.ofNullable(reviewedQss.getJSONObject(qsIdx)).orElse(new JSONObject());
                    DocCorrectResult result = new DocCorrectResult();
                    result.setConfigId(config.getId());
                    result.setConfigName(config.getName());
                    result.setAreaIdx(areaIdx);
                    result.setQsIdx(qsIdx);
                    result.setTaskId(task.getId());
                    result.setTaskName(task.getName());
                    result.setRecordId(record.getId());
                    result.setRecordName(record.getDocname());
                    result.setAreaUrl(areaImg);
                    result.setQuestion(qs.getStr("question"));
                    result.setAnswer(qs.getStr("answer"));
                    result.setQsInfo(qs.getStr("qsInfo"));
                    BigDecimal score = qs.getBigDecimal("score", BigDecimal.ZERO);
                    result.setScore(score);
                    result.setStuAnswer(reviewedQs.getStr("studentAnswer"));
                    Boolean isCorrect = "Y".equals(Optional.ofNullable(reviewedQs.getStr("isCorrect")).orElse("Y"));
                    result.setIsCorrect(isCorrect ? 1 : 0);
                    result.setReview("1".equals(reviewStatus) ? reviewedQs.getStr("review") : reviewArea.getStr("error"));
                    if (isScorePoint.equals(2)) {
                        result.setScored(reviewedQs.getBigDecimal("scored", BigDecimal.ZERO));
                    } else {
                        result.setScored(isCorrect ? score : BigDecimal.ZERO);
                    }
                    results.add(result);
                }
            }
            // 删除之前的记录
            docCorrectResultMapper.delete(Wrappers.lambdaQuery(DocCorrectResult.class)
                .eq(DocCorrectResult::getRecordId, record.getId()));
            // 保存
            saveBatch(results);
        });
    }

    @Override
    public IPage<DocCorrectResultDTO> page(DocCorrectResultDTO param) {
        User user = AuthUtil.getLoginUser();
        return docCorrectResultMapper.selectDTOPage(param.getPage().page(), Wrappers.query()
                .eq("dcr.deleted", 0)
                .eq(Objects.nonNull(param.getIsCorrect()), "dcr.is_correct", param.getIsCorrect())
                .eq(Objects.nonNull(param.getRecordId()), "dcr.record_id", param.getRecordId())
                .eq(Objects.nonNull(param.getTaskId()), "dcr.task_id", param.getTaskId())
                .like(StrUtil.isNotBlank(param.getRecordName()), "dcr.record_name", param.getRecordName())
                .like(StrUtil.isNotBlank(param.getTaskName()), "dcr.task_name", param.getTaskName())
                .like(StrUtil.isNotBlank(param.getIdentify()), "dcrr.identify", param.getIdentify())
                .exists(CollUtil.isNotEmpty(param.getTagIds()), "select dtr.id from doc_tag_record dtr where dtr.deleted = 0 and dtr.doc_id = dcr.record_id and dtr.tag_id in ('" + CollUtil.join(param.getTagIds(), "','") + "')")
                .exists(!UserRoleConsts.ADMIN.equals(user.getRole()), "select ud.id from user_doc ud where ud.deleted = 0 and ud.user_id = {0} and ud.doc_id = dcr.record_id", user.getId())
                .orderByAsc(param.getCorrectRateOrder() == 1, "dcr.correct_rate")
                .orderByDesc(param.getCorrectRateOrder() == 2, "dcr.correct_rate")
                .orderByDesc("dcrr.create_time")
                .orderByAsc("dcr.area_idx", "dcr.qs_idx"));
    }

}
