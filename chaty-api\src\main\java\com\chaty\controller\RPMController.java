package com.chaty.controller;

import com.chaty.common.BaseResponse;
import com.chaty.task.correct.CorrectCacheService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/rpm")
public class RPMController {
    @Resource
    private CorrectCacheService correctCacheService;

    @GetMapping("/")
    public BaseResponse<?> get(@RequestParam(required = false, defaultValue = "") List<String> ids) {
        if (Objects.isNull(ids) || ids.isEmpty()) {
            return  BaseResponse.ok(correctCacheService.getCurrentRpmForAllTasks());
        } else {
            Long sum = 0L;
            for (String id : ids) {
                sum += correctCacheService.getCurrentRpmForTask(id);
            }
            return BaseResponse.ok(sum);
        }
    }
}
