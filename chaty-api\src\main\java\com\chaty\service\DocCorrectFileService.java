package com.chaty.service;

import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.dto.DocCorrectFileDTO;
import com.chaty.dto.DocCorrectTaskDTO;
import com.chaty.dto.OneClickRetestDTO;
import com.chaty.entity.DocCorrectFile;
import com.chaty.entity.admin.ClassStatisticDataReq;

import java.util.List;

public interface DocCorrectFileService extends IService<DocCorrectFile> {

    void add(DocCorrectFileDTO param);

    void delete(String id);

    void update(DocCorrectFileDTO param);

    DocCorrectFileDTO getById(String id);

    IPage<DocCorrectFileDTO> page(DocCorrectFileDTO param);

    DocCorrectFileDTO createFile(DocCorrectFileDTO param, Boolean isForApiUse);

    void doCorrect(DocCorrectFileDTO params);

    JSONArray getClassStatisticData(ClassStatisticDataReq req);

    JSONArray getClassStatisticDataWithoutRangesOnlyFirstSheet(ClassStatisticDataReq req);

    JSONArray processClassSheet(ClassStatisticDataReq req, JSONArray originalData);

    List<DocCorrectFile> getDocCorrectFileListByConfigPackageId(String configPackageId);

    String generateClassStatisticExcel(JSONArray sheetData);

    String generateClassStatisticExcel4(JSONArray sheetData);

    String generateClassStatisticExcel5(JSONArray sheetData);

    String generateClassStatisticExcel5(JSONArray sheetData, ClassStatisticDataReq req);

    JSONArray getClassStatisticDataWithoutRanges(ClassStatisticDataReq req);

    String generateClassStatisticExcel6(JSONArray sheetData, ClassStatisticDataReq req);

    JSONArray getClassStatisticDataForStyle6(ClassStatisticDataReq req);

    List<DocCorrectTaskDTO> oneClickRetest(OneClickRetestDTO req);
}
