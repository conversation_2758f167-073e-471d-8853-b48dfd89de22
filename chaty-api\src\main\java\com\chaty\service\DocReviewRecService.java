package com.chaty.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.entity.DocReviewRec;

public interface DocReviewRecService {
    
    List<DocReviewRec> list(DocReviewRec param);

    void insertOne(DocReviewRec entity);

    void updateById(DocReviewRec entity);

    void deleteById(String id);

    DocReviewRec selectById(String id);

    void batchAdd(DocReviewRec params);

    IPage<DocReviewRec> findPage(DocReviewRec params);

}
