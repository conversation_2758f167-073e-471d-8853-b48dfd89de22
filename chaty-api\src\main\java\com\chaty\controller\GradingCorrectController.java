package com.chaty.controller;

import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.StrUtil;
import com.chaty.entity.GradingCorrect;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.common.BaseResponse;
import com.chaty.dto.GradingCorrectDTO;
import com.chaty.service.GradingCorrectService;

@RequestMapping("/api/grading/correct")
@RestController
public class GradingCorrectController {

    @Resource
    private GradingCorrectService gradingCorrectService;

    @PostMapping("/page")
    public BaseResponse<?> getPage(@RequestBody GradingCorrectDTO param) {
        IPage<GradingCorrectDTO> res = gradingCorrectService.getPage(param);
        return BaseResponse.ok(res);
    }

    @PostMapping("/grade")
    public BaseResponse<?> doGrade(@RequestBody GradingCorrectDTO param) {
        gradingCorrectService.doGrade(param);
        return BaseResponse.ok("评分成功");
    }

    @GetMapping("/delete")
    public BaseResponse<?> delete(@RequestParam String id) {
        gradingCorrectService.removeById(id);
        return BaseResponse.ok("删除成功");
    }

    @GetMapping("/dwonload")
    public BaseResponse<?> download(@RequestParam String id, HttpServletResponse response) {
        Map<String, Object> res = gradingCorrectService.getFile(id);
        return BaseResponse.ok(res);
    }

    @PostMapping("/update")
    public BaseResponse<?> update(@RequestBody GradingCorrect param) {
        if (StrUtil.isBlank(param.getId())) {
            return BaseResponse.error("id不能为空");
        }
        gradingCorrectService.updateById(param);
        return BaseResponse.ok(param);
    }
}
