package com.chaty.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;

import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chaty.dto.UserDTO;
import com.chaty.entity.User;
import com.chaty.enums.UserRoleConsts;
import com.chaty.exception.BaseException;
import com.chaty.mapper.UserMapper;
import com.chaty.security.BaseUserDetails;
import com.chaty.service.SchoolUserService;
import com.chaty.service.UserService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;

@Service
public class UserServiceImpl implements UserService, UserDetailsService {

    @Resource
    private UserMapper userMapper;
    @Resource
    private PasswordEncoder passwordEncoder;
    @Resource
    private SchoolUserService schoolUserService;

    @Override
    public void delete(String id) {
        userMapper.deleteById(id);
    }

    @Override
    public User findById(String id) {
        return userMapper.selectById(id);
    }

    @Transactional
    @Override
    public void insert(UserDTO user) {
        User existed = findByUsername(user.getUsername());
        if (Objects.nonNull(existed)) {
            throw new BaseException("用户名已存在");
        }

        user.setId(IdUtil.fastSimpleUUID());
        user.setStatus(1);
        user.setRole(Optional.ofNullable(user.getRole()).orElse(UserRoleConsts.STUDENT));
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        user.setDefaultSchool(user.getSchoolId());
        userMapper.insert(user);

        if (Objects.nonNull(user.getSchoolId())) {
            schoolUserService.addSchoolUser(user.getSchoolId(), user.getId());
        }
    }

    @Override
    public List<User> list(User params) {
        return userMapper.list(params);
    }

    @Override
    public void updateById(User user) {
        if (StringUtils.isEmpty(user.getId())) {
            user.setPassword(passwordEncoder.encode(user.getPassword()));
        }
        userMapper.updateById(user);
    }

    @Override
    public void updateByNicknameAndStudentId(User user) {
        userMapper.updateByNicknameAndStudentId(user.getNickname(), user.getStudentId(), user.getId());
    }

    @Override
    public User findByUsername(String username) {
        return userMapper.selectByUsername(username);
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        User user = userMapper.selectByUsername(username);
        return Optional.ofNullable(user)
                .map(u -> new BaseUserDetails(u))
                .orElseThrow(() -> new UsernameNotFoundException("用户名或密码错误!"));
    }

    @Override
    public List<UserDTO> userCount() {
        return userMapper.userCount();
    }

    @Override
    public List<User> selectByClassId(String classId) {
        return userMapper.selectByClassId(classId);
    }

    @Override
    public IPage<UserDTO> findPage(UserDTO params) {
        Wrapper<User> wrapper = Wrappers.lambdaQuery(User.class)
                .eq(User::getDeleted, Boolean.FALSE)
                .like(StringUtils.hasText(params.getUsername()), User::getUsername, params.getUsername());
        return userMapper.selectPage(params.getPage().page(User.class), wrapper)
                .convert(user -> {
                    UserDTO userDTO = BeanUtil.copyProperties(user, UserDTO.class);
                    userDTO.setPassword(null);
                    return userDTO;
                });
    }

}
