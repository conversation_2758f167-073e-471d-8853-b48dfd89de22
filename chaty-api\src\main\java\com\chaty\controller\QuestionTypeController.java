package com.chaty.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chaty.common.BaseResponse;
import com.chaty.dto.QuestionTypeDTO;
import com.chaty.entity.QuestionType;
import com.chaty.service.cache.ModelSettingService;
import com.chaty.service.cache.QuestionTypeService;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/api/questionType")
public class QuestionTypeController {

    @Resource
    private QuestionTypeService questionTypeService;

    @Resource
    private ModelSettingService modelSettingService;

    // ===== Create =====
    @PostMapping("/add")
    public BaseResponse<?> add(@RequestBody QuestionType body) {
        try {
            return BaseResponse.ok(questionTypeService.add(body));
        } catch (IllegalArgumentException e) {
            return BaseResponse.error(e.getMessage());
        } catch (Exception e) {
            return BaseResponse.error("新增失败");
        }
    }

    // ===== Read - detail by id =====
    @GetMapping("/detail")
    public BaseResponse<?> detail(@RequestParam Integer id) {
        if (id == null) return BaseResponse.error("id 不能为空");
        QuestionType one = questionTypeService.detail(id);
        if (one == null) return BaseResponse.error("记录不存在");
        return BaseResponse.ok(one);
    }

    // ===== ✅ 新增：detail by name（优先 Redis）=====
    @GetMapping("/byName")
    public BaseResponse<?> byName(@RequestParam String name) {
        if (name == null || name.trim().isEmpty()) {
            return BaseResponse.error("name 不能为空");
        }
        QuestionType one = questionTypeService.getByName(name);
        if (one == null) return BaseResponse.error("记录不存在");
        return BaseResponse.ok(one);
    }

    // ===== Read - list （按名称模糊 + 分页，优先 Redis）=====
    @PostMapping("/list")
    public BaseResponse<?> list(
            @RequestParam(required = false) String name,
            @RequestParam(defaultValue = "1") long pageNo,
            @RequestParam(defaultValue = "10") long pageSize
    ) {
        IPage<QuestionType> page = questionTypeService.list(name, pageNo, pageSize);

        IPage<QuestionTypeDTO> res = page.convert(item -> {
            QuestionTypeDTO dto = new QuestionTypeDTO();
            BeanUtils.copyProperties(item, dto);
            dto.setModelSetting(modelSettingService.getByIdCache(item.getDefaultModelRequestId()));
            return dto;
        });
        return BaseResponse.ok(res);
    }

    // ===== Update =====
    @PostMapping("/update")
    public BaseResponse<?> update(@RequestBody QuestionType body) {
        if (body == null || body.getId() == null) {
            return BaseResponse.error("id 不能为空");
        }
        try {
            return BaseResponse.ok(questionTypeService.update(body));
        } catch (IllegalArgumentException e) {
            return BaseResponse.error(e.getMessage());
        } catch (Exception e) {
            return BaseResponse.error("更新失败");
        }
    }

    // ===== Delete（单条）=====
    @GetMapping("/delete")
    public BaseResponse<?> delete(@RequestParam Integer id) {
        if (id == null) return BaseResponse.error("id 不能为空");
        boolean ok = questionTypeService.delete(id);
        return ok ? BaseResponse.ok(true) : BaseResponse.error("删除失败或记录不存在");
    }

    // ===== Delete（批量）=====
    @PostMapping("/deleteBatch")
    public BaseResponse<?> deleteBatch(@RequestBody List<Integer> ids) {
        if (ids == null || ids.isEmpty()) return BaseResponse.error("ids 不能为空");
        int rows = questionTypeService.deleteBatch(ids);
        return rows > 0 ? BaseResponse.ok(true) : BaseResponse.error("批量删除失败");
    }
}
