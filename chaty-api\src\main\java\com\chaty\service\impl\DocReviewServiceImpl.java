package com.chaty.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiFunction;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.chaty.entity.GptAskLogEntity;
import com.chaty.enums.GptAskLogType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.core.task.TaskExecutor;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import com.chaty.cache.DocReviewCache;
import com.chaty.cache.DocReviewCacheService;
import com.chaty.dto.ChatCompletionDTO;
import com.chaty.dto.DocReviewDTO;
import com.chaty.dto.DocReviewDTO.AreaDTO;
import com.chaty.dto.DocReviewDTO.QuestionDTO;
import com.chaty.entity.DocReview;
import com.chaty.enums.AIModelConsts;
import com.chaty.enums.CompletionEnums;
import com.chaty.exception.BaseException;
import com.chaty.exception.RetryException;
import com.chaty.mapper.DocReviewMapper;
import com.chaty.service.BasicAiService;
import com.chaty.service.DocReviewService;
import com.chaty.service.OCRService;
import com.chaty.service.PDFService;
import com.chaty.util.LatexUtil;
import com.chaty.util.PDFUtil;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class DocReviewServiceImpl implements DocReviewService {

    @Value("${file.local.path}")
    public String path;
    @Value("${file.local.ctxpath}")
    public String ctxPath;
    @Value("${server.url}")
    public String serverUrl;

    @Resource
    private TaskExecutor taskExecutor;
    @Resource
    private DocReviewMapper docReviewMapper;
    @Resource
    private Map<String, OCRService> ocrServices;
    @Resource
    private Set<BasicAiService> basicAiServices;
    @Resource
    private PDFService pdfService;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private DocReviewCacheService docReviewCacheService;

    @Override
    public DocReview add(DocReview param) {
        param.setId(IdUtil.fastSimpleUUID());
        docReviewMapper.insertOne(param);
        return param;
    }

    @Override
    public void updateById(DocReview param) {
        docReviewMapper.updateById(param);
    }

    @Override
    public List<DocReview> list(DocReview param) {
        return docReviewMapper.list(param);
    }

    @Override
    public void deleteById(String id) {
        docReviewMapper.deleteById(id);
    }

    @Override
    public Map<String, ?> review(DocReviewDTO params) {
        List<String> ids = params.getIds();
        log.info("DocReview ids: {}", params.getIds());
        String batchId = IdUtil.fastSimpleUUID();
        DocReview update = new DocReview();
        update.setBatchId(batchId);
        update.setIncluedIds(ids);
        docReviewMapper.updateById(update);
        // 执行
        params.setBatchId(batchId);
        for (String id : ids) {
            // 添加缓存
            docReviewCacheService.put(id, DocReviewCache.builder()
                    .docReviewId(id)
                    .questionNum(params.getQuestions().size())
                    .reviewedNum(0)
                    .build());
            taskExecutor.execute(() -> {
                doReview(params, id);
            });
        }
        return Collections.singletonMap("batchId", batchId);
    }

    public void doReview(DocReviewDTO reviewContext, String id) {
        log.info("DocReview execute task id: {}", id);
        try {
            updateStatus(id, 2); // 更新状态为进行中

            DocReview update = new DocReview();
            update.setId(id);

            // 获取文档
            DocReview docReview = docReviewMapper.selectById(id);
            // 获取文档地址
            String docPath = docReview.getFileurl().substring(ctxPath.length() + 1);
            // 试卷标识
            if (Objects.nonNull(reviewContext.getDocIdArea())) {
                update.setIdentify(ocrArea(reviewContext.getDocIdArea(), docPath,
                        reviewContext.getPrimaryOcrService(), reviewContext.getSecondaryOcrService()));
            }
            // 问题识别处理
            List<JSONObject> reviewedRes = reviewContext.getQuestions().stream().map(question -> {
                JSONObject reviewed;
                if (AIModelConsts.visionModels.contains(reviewContext.getModel())) {
                    reviewed = visionAIReview(reviewContext, docPath, question);
                } else {
                    reviewed = ocrAIReview(reviewContext, docPath, question);
                }
                reviewed.set("answerArea", question.getAnswerArea());
                reviewed.set("reviewArea", question.getReviewArea());
                reviewed.set("checkArea", question.getCheckArea());
                reviewed.set("defaultReview", question.getDefaultReview());
                reviewed.set("isDefaultReview", question.getIsDefaultReview());
                reviewed.set("score", question.getScore());

                docReviewCacheService.nextQuestion(id); // 更新缓存

                return reviewed;
            }).collect(Collectors.toList());
            update.setReviewed(JSONUtil.toJsonStr(reviewedRes));
            // 合并生成结果文档
            Map<String, Object> reviewDocParams = MapUtil.builder(new HashMap<String, Object>())
                    .put("reviewContext", reviewContext)
                    .put("reviewedGroup", Collections.singletonList(reviewedRes))
                    .build();
            Map<String, Object> reviewedFile = pdfService.docReviewPDF(reviewDocParams);
            update.setReviewDoc(MapUtil.getStr(reviewedFile, "fileUrl"));

            // 更新状态为完成
            update.setStatus(3);
            docReviewMapper.updateById(update);
        } catch (Exception e) {
            log.error("DocReview execute task error", e);
            updateStatus(id, 4, e.getMessage()); // 更新状态为失败
            // 清除缓存
            docReviewCacheService.remove(id);
        }
    }

    /**
     * 拷贝文档
     * 
     * @param docReview
     * @return
     */
    private String copyDoc(DocReview docReview) {
        String filename = docReview.getFileurl().substring(ctxPath.length() + 1);
        String newFilename = IdUtil.fastSimpleUUID() + ".pdf";
        FileUtil.copyFile(String.format("%s/%s", path, filename), String.format("%s/%s", path, newFilename));
        return newFilename;
    }

    /**
     * PDF 截取并OCR识别
     * 
     * @return
     */
    private String ocrArea(AreaDTO area, String doc, String serviceName, String secondaryService) {
        String copyedDocPath = String.format("%s/%s", path, doc);
        String imgName = IdUtil.fastSimpleUUID() + ".jpg";
        String answerImg = String.format("%s/%s", path, imgName);
        PDFUtil.extractImageFromPDF(copyedDocPath, answerImg, area.getX(), area.getY(),
                area.getWidth(), area.getHeight());
        ThreadUtil.sleep(500);
        try {
            return ocrServices.get(serviceName).ocrForText(String.format("%s%s/%s", serverUrl, ctxPath, imgName));
            // return
            // ocrServices.get(serviceName).ocrForText("https://mathpix-ocr-examples.s3.amazonaws.com/cases_hw.jpg");
        } catch (Exception e) {
            // 调用腾讯的...
            log.error("ocrArea error: {}", e.getMessage());
            return ocrServices.get(secondaryService).ocrForText(String.format("%s%s/%s", serverUrl, ctxPath, imgName));
        }
    }

    // AI 评价
    @Retryable(value = RetryException.class)
    public void aiReview(DocReviewDTO reviewContext, QuestionDTO question, String answer, JSONObject res) {
        try {
            // 请求参数
            ChatCompletionDTO completion = CompletionEnums.AI_REVIEW.getCompletion(reviewContext.getModel(),
                    question.getQuestion(), question.getCorrectAnswer(), answer);
            GptAskLogEntity gptAskLogEntity = new GptAskLogEntity();
            gptAskLogEntity.setAimodel(reviewContext.getModel());
            gptAskLogEntity.setType(GptAskLogType.AiReview);
            Map<String, Object> cresp = BasicAiService.findSupport(basicAiServices, reviewContext.getModel())
                    .chatForCompletion(completion, gptAskLogEntity);
            // TOOD 异常处理
            JSONObject jsonResp = JSONUtil.parseObj(cresp);
            if (CompletionEnums.AI_REVIEW.getFuncName()
                    .equals(jsonResp.getByPath("'$function_call'.name", String.class))) {
                String reviewed = jsonResp.getByPath("'$function_call'.arguments", String.class);
                reviewed = reviewed.replaceAll("(?<!\\\\)\\\\(?!\\\\)", "\\\\\\\\")
                        .replaceAll("\\\\\n", "\\\\n");
                log.info("parsed reviewed: {}", reviewed);
                JSONObject reviewedObj = JSONUtil.parseObj(reviewed);
                if (Objects.nonNull(reviewedObj.get("review"))) {
                    reviewedObj.set("review", LatexUtil.escapeLatex(reviewedObj.getStr("review")));
                }
                res.set("review", reviewedObj);
            } else {
                res.set("status", false);
                res.set("error", jsonResp.get("$response"));
            }
        } catch (Exception e) {
            throw new RetryException("ai评价失败!", e);
        }
    }

    @Retryable(value = RetryException.class)
    public void aiCompletionReview(DocReviewDTO reviewContext, QuestionDTO question, String answer, JSONObject res) {
        try {
            // 请求参数
            ChatCompletionDTO completion = CompletionEnums.COMPLETION_DOCREVIEW.getCompletion(reviewContext.getModel(),
                    question.getCorrectAnswer(), question.getQuestion(), answer);
            GptAskLogEntity gptAskLogEntity = new GptAskLogEntity();
            gptAskLogEntity.setAimodel(reviewContext.getModel());
            gptAskLogEntity.setType(GptAskLogType.AiReview);
            Map<String, Object> cresp = BasicAiService.findSupport(basicAiServices, reviewContext.getModel())
                    .chatForCompletion(completion, gptAskLogEntity);
            parseCompleteResp(res, cresp);
        } catch (Exception e) {
            throw new RetryException("ai评价失败!", e);
        }
    }

    public void updateStatus(String id, int status) {
        updateStatus(id, status, null);
    }

    public void updateStatus(String id, int status, String error) {
        DocReview update = new DocReview();
        update.setId(id);
        update.setStatus(status);
        update.setErrorText(error);
        docReviewMapper.updateById(update);
    }

    @Override
    public Map<String, Object> ocrCropArea(String docurl, AreaDTO area, String service) {
        String docPath = docurl.substring(ctxPath.length() + 1);
        service = Optional.ofNullable(service).orElse("mathPixOCRService");
        String text = ocrArea(area, docPath, service, "tencentOCRService");
        return Collections.singletonMap("text", text);
    }

    @Override
    public Map<String, Object> batchReviewDoc(DocReviewDTO params) {
        List<String> ids = params.getIds();
        if (Objects.isNull(ids) || ids.isEmpty()) {
            throw new BaseException("请勿提交空的试卷");
        }
        DocReview search = new DocReview();
        search.setIncluedIds(ids);
        Map<String, JSONArray> mapById = new HashMap<>();
        Map<String, DocReview> mapByDoc = new HashMap<>();
        docReviewMapper.list(search).stream()
                .forEach(doc -> {
                    mapById.put(doc.getId(), JSONUtil.parseArray(doc.getReviewed()));
                    mapByDoc.put(doc.getId(), doc);
                });
        List<JSONArray> reviewedGroup = new ArrayList<>();
        List<DocReview> reviewedDoc = new ArrayList<>();
        ids.forEach(id -> {
            reviewedGroup.add(mapById.get(id));
            reviewedDoc.add(mapByDoc.get(id));
        });

        Map<String, Object> reviewDocParams = MapUtil.builder(new HashMap<String, Object>())
                .put("reviewContext", params)
                .put("reviewedGroup", reviewedGroup)
                .put("reviewedDoc", reviewedDoc)
                .build();
        return pdfService.docReviewPDF(reviewDocParams);
    }

    @Override
    public Map<String, Object> reviewStatsDoc(DocReviewDTO params) {
        DocReview query = new DocReview();
        query.setIncluedIds(params.getIds());
        List<DocReview> docReviews = docReviewMapper.list(query);

        List<JSONObject> reviewedRes = new ArrayList<JSONObject>();
        docReviews.forEach(docReview -> {
            JSONArray reviewed = JSONUtil.parseArray(docReview.getReviewed());
            for (int i = 0; i < params.getQuestions().size(); i++) {
                JSONObject questionRes = reviewed.getJSONObject(i);
                JSONObject questionData = null;
                if (reviewedRes.size() == i) {
                    questionData = new JSONObject();
                    reviewedRes.add(questionData);
                } else {
                    questionData = reviewedRes.get(i);
                }
                if (Objects.nonNull(questionRes)) {
                    boolean isTrue = questionRes.getByPath("review.isTrue", Boolean.class);
                    questionData.compute("count", addBiFunc());
                    if (isTrue) {
                        questionData.compute("correct", addBiFunc());
                    } else {
                        questionData.compute("incorrect", addBiFunc());
                    }
                }
            }
        });

        Map<String, Object> reviewDocParams = MapUtil.builder(new HashMap<String, Object>())
                .put("reviewContext", params)
                .put("reviewedRes", reviewedRes)
                .build();
        return pdfService.reviewStatsPDF(reviewDocParams);
    }

    private BiFunction<String, Object, Object> addBiFunc() {
        return (k, v) -> {
            if (Objects.isNull(v)) {
                return 1;
            } else {
                return (Integer) v + 1;
            }
        };
    }

    JSONObject ocrAIReview(DocReviewDTO reviewContext, String docPath, QuestionDTO question) {
        // PDF 截取答案并OCR识别
        String ocrAnswer = ocrArea(question.getAnswerArea(), docPath,
                reviewContext.getPrimaryOcrService(), reviewContext.getSecondaryOcrService());
        log.info("ocrAnswer: {}", ocrAnswer);
        // 识别结果与正确答案比对
        JSONObject reviewed = new JSONObject();
        reviewed.set("status", true);
        DocReviewServiceImpl docReviewService = applicationContext.getBean(DocReviewServiceImpl.class);
        try {
            if (AIModelConsts.aiReviewModels.contains(reviewContext.getModel())) {
                docReviewService.aiReview(reviewContext, question, ocrAnswer, reviewed);
            } else {
                docReviewService.aiCompletionReview(reviewContext, question, ocrAnswer, reviewed);
            }

        } catch (Exception e) {
            reviewed.set("status", false);
            reviewed.set("error", "AI评价异常");
            log.error("AI评价异常!", e);
        }
        return reviewed;
    }

    JSONObject visionAIReview(DocReviewDTO reviewContext, String docPath, QuestionDTO question) {
        AreaDTO area = question.getAnswerArea();
        // 图片截取
        String copyedDocPath = String.format("%s/%s", path, docPath);
        String imgName = IdUtil.fastSimpleUUID() + ".jpg";
        String answerImg = String.format("%s/%s", path, imgName);
        PDFUtil.extractImageFromPDF(copyedDocPath, answerImg, area.getX(), area.getY(),
                area.getWidth(), area.getHeight());
        String imgUrl = String.format("%s%s/%s", serverUrl, ctxPath, imgName);
        // String imgUrl = "https://mathpix-ocr-examples.s3.amazonaws.com/cases_hw.jpg";
        ThreadUtil.sleep(500);

        // 识别结果与正确答案比对
        JSONObject reviewed = new JSONObject();
        reviewed.set("status", true);
        DocReviewServiceImpl docReviewService = applicationContext.getBean(DocReviewServiceImpl.class);
        try {
            docReviewService.visionReview(reviewContext, question, imgUrl, reviewed);
        } catch (Exception e) {
            reviewed.set("status", false);
            reviewed.set("error", e.getMessage());
            log.error("AI评价异常!", e);
        }
        return reviewed;
    }

    @Retryable(value = RetryException.class, maxAttempts = 3)
    public void visionReview(DocReviewDTO reviewContext, QuestionDTO question, String imgUrl, JSONObject res) {
        try {
            // 请求参数
            ChatCompletionDTO completion = CompletionEnums.VISION_DOVREVIEW.getVisionCompletion(
                    reviewContext.getModel(), imgUrl,
                    question.getCorrectAnswer(), question.getQuestion());
            GptAskLogEntity gptAskLogEntity = new GptAskLogEntity();
            gptAskLogEntity.setAimodel(reviewContext.getModel());
            gptAskLogEntity.setType(GptAskLogType.AiReview);
            Map<String, Object> cresp = BasicAiService.findSupport(basicAiServices, reviewContext.getModel())
                    .chatForCompletion(completion, gptAskLogEntity);
            parseCompleteResp(res, cresp);
        } catch (Exception e) {
            throw new RetryException("ai评价失败!", e);
        }
    }

    private void parseCompleteResp(JSONObject res, Map<String, Object> resp) {
        // TOOD 异常处理
        JSONObject jsonResp = JSONUtil.parseObj(resp);
        String response = jsonResp.get("$response", String.class);
        log.info("completion response: {}", response);

        // 结果解析
        String regex = "(?<=(### 是否正确|### 描述学生答案)\\n)([\\s\\S]*?)(?=(\\n\\n|$))";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(response);
        List<String> matchGroups = new ArrayList<String>();
        while (matcher.find()) {
            matchGroups.add(matcher.group());
        }
        if (matchGroups.size() == 2) {
            JSONObject reviewObj = new JSONObject();
            reviewObj.set("isTrue", Objects.equals(matchGroups.get(0), "1") ? true : false);
            reviewObj.set("review", LatexUtil.escapeLatex(matchGroups.get(1)));
            res.set("review", reviewObj);
        } else {
            log.error("completion response error: {}", response);
            throw new RetryException(response);
        }
    }

    @Override
    public List<DocReview> addBatch(List<DocReview> params) {
        params.forEach(param -> {
            param.setId(IdUtil.fastSimpleUUID());
        });
        docReviewMapper.insertBatch(params);
        return params;
    }

}
