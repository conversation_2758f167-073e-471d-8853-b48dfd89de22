package com.chaty.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chaty.entity.DocReviewRec;
import com.chaty.mapper.DocReviewRecMapper;
import com.chaty.service.DocReviewRecService;

import cn.hutool.core.util.IdUtil;

@Service
public class DocReviewRecServiceImpl implements DocReviewRecService {

    @Resource
    private DocReviewRecMapper docReviewRecMapper;

    @Override
    public List<DocReviewRec> list(DocReviewRec param) {
        return docReviewRecMapper.list(param);
    }

    @Override
    public void insertOne(DocReviewRec entity) {
        docReviewRecMapper.insertOne(entity);
    }

    @Override
    public void updateById(DocReviewRec entity) {
        docReviewRecMapper.updateById(entity);
    }

    @Override
    public void deleteById(String id) {
        docReviewRecMapper.deleteById(id);
    }

    @Override
    public DocReviewRec selectById(String id) {
        return docReviewRecMapper.selectById(id);
    }

    @Transactional
    @Override
    public void batchAdd(DocReviewRec params) {
        params.getReviewIds().forEach(reviewId -> {
            DocReviewRec entity = new DocReviewRec();
            entity.setDocReviewId(reviewId);
            entity.setLibraryId(params.getLibraryId());
            entity.setId(IdUtil.fastSimpleUUID());
            docReviewRecMapper.insertOne(entity);
        });
    }

    @Override
    public IPage<DocReviewRec> findPage(DocReviewRec params) {
        return docReviewRecMapper.selectPage(new Page<>(params.getPageNumber(), params.getPageSize()), params);
    }
    
}
