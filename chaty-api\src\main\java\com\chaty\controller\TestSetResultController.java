package com.chaty.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.common.BaseResponse;
import com.chaty.entity.TestSetResultEntity;
import com.chaty.enums.TestSetResultStatus;
import com.chaty.service.TestSetResultService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/** 测试集批改结果 CRUD（仅DB，不走Redis） */
@RestController
@RequestMapping("/api/testsetResult")
public class TestSetResultController {

    @Resource
    private TestSetResultService service;

    /** 分页（支持 testSetId / status 过滤） */
    @GetMapping("/page")
    public BaseResponse<IPage<TestSetResultEntity>> page(
            @RequestParam(required = false) Integer pageNumber,
            @RequestParam(required = false) Integer pageSize,
            @RequestParam(required = false) Long testSetId,
            @RequestParam(required = false) TestSetResultStatus status
    ) {
        return BaseResponse.ok(service.page(pageNumber, pageSize, testSetId, status));
    }

    /** 详情 */
    @GetMapping("/{id}")
    public BaseResponse<TestSetResultEntity> get(@PathVariable Long id) {
        return BaseResponse.ok(service.getById(id));
    }

    /** 新增 */
    @PostMapping("/add")
    public BaseResponse<Long> add(@RequestBody TestSetResultEntity param) {
        return BaseResponse.ok(service.add(param));
    }

    /** 更新 */
    @PostMapping("/update")
    public BaseResponse<Long> update(@RequestBody TestSetResultEntity param) {
        return BaseResponse.ok(service.updateOne(param));
    }

    /** 删除 */
    @DeleteMapping("/{id}")
    public BaseResponse<?> delete(@PathVariable Long id) {
        service.deleteById(id);
        return BaseResponse.ok("删除成功");
    }
}
