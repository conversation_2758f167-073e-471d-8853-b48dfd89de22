package com.chaty.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.dto.DocCorrectFileDTO;
import com.chaty.entity.DocFileConfig;
import com.chaty.mapper.DocFileConfigMapper;
import com.chaty.service.DocFileConfigService;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import java.util.Objects;
import java.util.UUID;

@Service
public class DocFileConfigServiceImpl extends ServiceImpl<DocFileConfigMapper, DocFileConfig> implements DocFileConfigService {

    @Resource
    private DocFileConfigMapper docFileConfigMapper;

    @Override
    public DocFileConfig getByCode(String code) {
        Wrapper<DocFileConfig> wrapper = Wrappers.lambdaQuery(DocFileConfig.class).eq(DocFileConfig::getCode, code);
        return docFileConfigMapper.selectOne(wrapper);
    }

    @Override
    public void saveByFile(DocCorrectFileDTO param) {
        String code = param.getCode();
        if (StrUtil.isBlank(code)) {
            code = UUID.randomUUID().toString();
        }
        DocFileConfig config = getByCode(code);
        if (Objects.nonNull(config)) {
            config.setConfig(JSONUtil.toJsonStr(param.getPages()));
            updateById(config);
        } else {
            DocFileConfig add = new DocFileConfig();
            add.setCode(code);
            add.setConfig(JSONUtil.toJsonStr(param.getPages()));
            save(add);
        }
    }
    
}
