package com.chaty.Handler;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

public class Hutool<PERSON>sonTypeHandler extends AbstractJsonTypeHandler<JSONObject> {

    @Override
    protected JSONObject parse(String json) {
        if (json != null) {
            return new JSONObject(json);
        }
        return null;
    }

    @Override
    protected String toJson(JSONObject obj) {
        if (obj != null) {
            return obj.toString();
        }
        return null;
    }
}

