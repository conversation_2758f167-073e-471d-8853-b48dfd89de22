package com.chaty.enums;

public interface GptAskLogType {

    String normalArea = "普通区域";

    String cardArea = "答题卡区域";

    String essayArea = "写作题";

    String scorePoint = "分数识别";

    String completion = "后端的问答接口";

    String AiReview = "ai评价";

    String studentNumer = "学号识别";

    String studentName = "姓名识别";

    String extraQs = "问题提取";

    String essayAnalyticalReport = "作文分析报告";

    String correctOrgQs = "correctOrgQs";

    String standardVolumeMatching = "标准卷匹配";

    String normalQsFirstQuest = "普通区域-第一次请求";

    String normalQsSecondQuest = "普通区域-第二次请求";
}
