package com.chaty.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("question_type")
public class QuestionType {

    @TableId(type = IdType.AUTO)
    private Integer id;                 // 主键ID

    private String name;                // 题型名称（唯一）

    private String description;         // 描述

    @TableField("default_model_request_id")
    private Integer defaultModelRequestId; // 默认绑定的模型仓库ID(model_request.id)

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("create_time")
    private LocalDateTime createTime;   // 创建时间（DB自动维护）

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("update_time")
    private LocalDateTime updateTime;   // 修改时间（DB自动维护）
}
