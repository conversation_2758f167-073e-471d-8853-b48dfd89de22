package com.chaty.service.cache;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.entity.QuestionType;
import com.chaty.mapper.QuestionTypeMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct; // ★ 新增
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * QuestionType 缓存策略（适用于题型这样的小表）：
 * - Redis String:  key = QT:ALL      ，value = JSON[List<QuestionType>]（按 update_time desc, id desc 排序）
 * - Redis Hash  :  key = QT:IDMAP    ，field = id (String)         ，value = JSON[QuestionType]
 * - Redis Hash  :  key = QT:NAMEMAP  ，field = lower(trim(name))   ，value = JSON[QuestionType]   // ⭐ 新增 name 索引
 *
 * 读：detail 优先查 IDMAP；list 优先查 ALL；getByName 优先查 NAMEMAP
 * 写：add/update/delete/deleteBatch 后统一 refreshAllCache()，保证三份缓存一致
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QuestionTypeServiceImpl
        extends ServiceImpl<QuestionTypeMapper, QuestionType>
        implements QuestionTypeService {

    private static final String KEY_ALL     = "QT:ALL";
    private static final String KEY_IDMAP   = "QT:IDMAP";
    private static final String KEY_NAMEMAP = "QT:NAMEMAP";

    private final QuestionTypeMapper questionTypeMapper;
    private final StringRedisTemplate stringRedisTemplate;
    @Resource
    private ObjectMapper objectMapper;

    // ★ 启动完成后预热缓存（全量刷入 Redis）
    @PostConstruct
    public void initCache() {
        try {
            refreshAllCache();
            log.info("[QuestionType] Cache warmed on startup.");
        } catch (Exception e) {
            log.warn("[QuestionType] Cache warm-up failed: {}", e.getMessage());
        }
    }

    // ==================== Public APIs ====================

    @Override
    public QuestionType add(QuestionType body) {
        if (body == null || StrUtil.isBlank(body.getName())) {
            throw new IllegalArgumentException("name 不能为空");
        }
        long cnt = questionTypeMapper.selectCount(
                new LambdaQueryWrapper<QuestionType>()
                        .eq(QuestionType::getName, body.getName())
        );
        if (cnt > 0) {
            throw new IllegalArgumentException("题型名称已存在");
        }
        body.setId(null);
        questionTypeMapper.insert(body);
        refreshAllCache();
        return questionTypeMapper.selectById(body.getId());
    }

    @Override
    public QuestionType update(QuestionType body) {
        if (body == null || body.getId() == null) {
            throw new IllegalArgumentException("id 不能为空");
        }
        QuestionType db = questionTypeMapper.selectById(body.getId());
        if (db == null) {
            throw new IllegalArgumentException("记录不存在");
        }
        if (StrUtil.isNotBlank(body.getName())) {
            long cnt = questionTypeMapper.selectCount(
                    new LambdaQueryWrapper<QuestionType>()
                            .eq(QuestionType::getName, body.getName())
                            .ne(QuestionType::getId, body.getId())
            );
            if (cnt > 0) {
                throw new IllegalArgumentException("题型名称已存在");
            }
        }
        questionTypeMapper.updateById(body);
        refreshAllCache();
        return questionTypeMapper.selectById(body.getId());
    }

    @Override
    public boolean delete(Integer id) {
        if (id == null) return false;
        int rows = questionTypeMapper.deleteById(id);
        refreshAllCache();
        return rows == 1;
    }

    @Override
    public int deleteBatch(List<Integer> ids) {
        if (CollUtil.isEmpty(ids)) return 0;
        int rows = questionTypeMapper.deleteBatchIds(ids);
        refreshAllCache();
        return rows;
    }

    @Override
    public QuestionType detail(Integer id) {
        if (id == null) return null;
        QuestionType fromCache = getFromIdHash(id);
        if (fromCache != null) return fromCache;

        QuestionType db = questionTypeMapper.selectById(id);
        if (db != null) {
            putIdHash(db);
            putNameHash(db);
        }
        return db;
    }

    @Override
    public Page<QuestionType> list(String name, long pageNo, long pageSize) {
        List<QuestionType> all = getAllFromCache();
        if (all == null) {
            refreshAllCache();
            all = getAllFromCache();
        }
        if (all == null) all = Collections.emptyList();

        String like = StrUtil.trimToEmpty(name);
        List<QuestionType> filtered = StrUtil.isBlank(like)
                ? all
                : all.stream()
                .filter(q -> StrUtil.containsIgnoreCase(q.getName(), like))
                .collect(Collectors.toList());

        long total = filtered.size();
        long current = Math.max(1, pageNo);
        long size = Math.max(1, pageSize);
        long fromIdx = (current - 1) * size;
        long toIdx = Math.min(fromIdx + size, total);
        List<QuestionType> pageRecords = fromIdx >= total
                ? Collections.emptyList()
                : filtered.subList((int) fromIdx, (int) toIdx);

        Page<QuestionType> page = new Page<>(current, size, total);
        page.setRecords(pageRecords);
        return page;
    }

    @Override
    public List<QuestionType> all() {
        List<QuestionType> all = getAllFromCache();
        if (all == null) {
            refreshAllCache();
            all = getAllFromCache();
        }
        return all == null ? Collections.emptyList() : all;
    }

    @Override
    public void refreshAllCache() {
        LambdaQueryWrapper<QuestionType> qw = new LambdaQueryWrapper<>();
        qw.orderByDesc(QuestionType::getUpdateTime)
                .orderByDesc(QuestionType::getId);
        List<QuestionType> list = questionTypeMapper.selectList(qw);
        writeAllCache(list);
    }

    // ===== 新增：name 查询（优先 Redis） =====
    @Override
    public QuestionType getByName(String name) {
        if (StrUtil.isBlank(name)) return null;
        String key = nameKey(name);
        // 1) 先读 name 索引
        QuestionType cached = getFromNameHash(key);
        if (cached != null) return cached;

        // 2) DB 精确查（等值），命中则写入 IDMAP 与 NAMEMAP
        QuestionType db = questionTypeMapper.selectOne(
                new LambdaQueryWrapper<QuestionType>()
                        .eq(QuestionType::getName, name)
        );
        if (db != null) {
            putIdHash(db);
            putNameHash(db);
        }
        return db;
    }

    // ==================== Redis helpers ====================

    private String nameKey(String name) {
        return StrUtil.trimToEmpty(name).toLowerCase();
    }

    private List<QuestionType> getAllFromCache() {
        try {
            String json = stringRedisTemplate.opsForValue().get(KEY_ALL);
            if (StrUtil.isBlank(json)) return null;
            return objectMapper.readValue(json, new TypeReference<List<QuestionType>>() {});
        } catch (Exception e) {
            log.warn("Redis 读取 {} 异常：{}", KEY_ALL, e.getMessage());
            return null;
        }
    }

    private void writeAllCache(List<QuestionType> list) {
        try {
            String json = objectMapper.writeValueAsString(list);
            stringRedisTemplate.opsForValue().set(KEY_ALL, json);

            // 重建 IDMAP & NAMEMAP
            if (CollUtil.isNotEmpty(list)) {
                Map<String, String> idMap = new HashMap<>(list.size() * 2);
                Map<String, String> nameMap = new HashMap<>(list.size() * 2);
                for (QuestionType qt : list) {
                    idMap.put(String.valueOf(qt.getId()), objectToJson(qt));
                    String nk = nameKey(qt.getName());
                    if (StrUtil.isNotBlank(nk)) {
                        nameMap.put(nk, objectToJson(qt));
                    }
                }
                stringRedisTemplate.delete(Arrays.asList(KEY_IDMAP, KEY_NAMEMAP)); // 全量重建
                if (!idMap.isEmpty()) {
                    stringRedisTemplate.opsForHash().putAll(KEY_IDMAP, idMap);
                }
                if (!nameMap.isEmpty()) {
                    stringRedisTemplate.opsForHash().putAll(KEY_NAMEMAP, nameMap);
                }
            } else {
                stringRedisTemplate.delete(Arrays.asList(KEY_IDMAP, KEY_NAMEMAP));
            }
        } catch (DataAccessException e) {
            log.error("Redis 写入缓存失败：{}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("序列化写缓存失败：{}", e.getMessage(), e);
        }
    }

    private QuestionType getFromIdHash(Integer id) {
        try {
            Object v = stringRedisTemplate.opsForHash().get(KEY_IDMAP, String.valueOf(id));
            if (v == null) return null;
            return objectMapper.readValue(String.valueOf(v), QuestionType.class);
        } catch (Exception e) {
            log.warn("Redis 读取 {}#{} 异常：{}", KEY_IDMAP, id, e.getMessage());
            return null;
        }
    }

    private QuestionType getFromNameHash(String lowerName) {
        try {
            Object v = stringRedisTemplate.opsForHash().get(KEY_NAMEMAP, lowerName);
            if (v == null) return null;
            return objectMapper.readValue(String.valueOf(v), QuestionType.class);
        } catch (Exception e) {
            log.warn("Redis 读取 {}#{} 异常：{}", KEY_NAMEMAP, lowerName, e.getMessage());
            return null;
        }
    }

    private void putIdHash(QuestionType qt) {
        try {
            stringRedisTemplate.opsForHash().put(
                    KEY_IDMAP, String.valueOf(qt.getId()), objectToJson(qt));
        } catch (Exception e) {
            log.warn("Redis 写入 {}#{} 失败：{}", KEY_IDMAP, qt.getId(), e.getMessage());
        }
    }

    private void putNameHash(QuestionType qt) {
        try {
            String nk = nameKey(qt.getName());
            if (StrUtil.isNotBlank(nk)) {
                stringRedisTemplate.opsForHash().put(
                        KEY_NAMEMAP, nk, objectToJson(qt));
            }
        } catch (Exception e) {
            log.warn("Redis 写入 {} 失败：{}", KEY_NAMEMAP, e.getMessage());
        }
    }

    private String objectToJson(Object o) {
        try {
            return objectMapper.writeValueAsString(o);
        } catch (Exception e) {
            return "{}";
        }
    }
}
