package com.chaty.completion;

import java.util.List;
import java.util.Objects;

import com.chaty.dto.ChatCompletionDTO;
import com.chaty.dto.DocCorrectRecordDTO;
import com.chaty.dto.MessageDTO;
import com.chaty.dto.PaperTopicDTO;
import com.chaty.enums.AIModelConsts;
import com.chaty.form.ExtraQsForm;
import com.chaty.util.FileUtil;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import static com.chaty.util.MyStringUtils.replaceChineseChar2EnglishCharInPrompt;

public interface CompletionCreator {

    boolean isSupported(String aimodel, Boolean jsonSchema);

    ChatCompletionDTO createDocAreaCompletion(DocCorrectRecordDTO record, JSONObject areaObj, JSONObject areaRes);

    ChatCompletionDTO createDocAreaNormalQsFirstRequest(DocCorrectRecordDTO record, JSONObject areaObj, JSONObject areaRes);

    ChatCompletionDTO createDocAreaNormalQsSecondRequest(DocCorrectRecordDTO record, JSONObject areaObj, JSONObject areaRes, JSONArray resolvedRes);

    ChatCompletionDTO createDocAreaResponseFormatCompletion(DocCorrectRecordDTO record, JSONObject areaObj, JSONObject areaRes, String ocrContent);

    ChatCompletionDTO createWriteQsCompletion1Request(DocCorrectRecordDTO record, JSONObject areaObj, JSONObject areaRes, Integer allScore, String ocrContent, Integer modelRequestId);

    ChatCompletionDTO createWriteQsCompletion2Request(DocCorrectRecordDTO record, JSONObject areaObj, String essay, String gradeName, String title, String apiCorrectResult, Integer allScore, Boolean isEnglishEssay, Integer modelRequestId);

    ChatCompletionDTO createScoreCompletion(DocCorrectRecordDTO record, JSONObject areaObj, JSONObject areaRes, String ocrContent);

    ChatCompletionDTO createEssayAnalyticalReportCompletion(List<DocCorrectRecordDTO> records, DocCorrectRecordDTO param, String scoreSituation, String gradeName);

    ChatCompletionDTO createExtractStudentName(String aimodel, String areaImgUrl, String ocrContent, Integer modelRequestId);

    ChatCompletionDTO createExtractStudentNumber(String aimodel, String areaImgUrl, String ocrContent, Integer modelRequestId);

    ChatCompletionDTO createExtractPaperTopic(String aimodel, List<String> imgUrls, List<PaperTopicDTO> topics);

    default MessageDTO createMessage(String role, String content) {
        content = replaceChineseChar2EnglishCharInPrompt(content);
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setRole(role);
        messageDTO.setContent(content);
        return messageDTO;
    }

    default void setSystemMessage(List<MessageDTO> messages, String aimodel, String content) {
        content = replaceChineseChar2EnglishCharInPrompt(content);
        if (Objects.equals(AIModelConsts.GPT_4_V, aimodel)) {
            MessageDTO messageDTO = createMessage("system", content);
            messages.add(messageDTO);
        } else {
            messages.add(createMessage("user", content));
            if (aimodel.contains("claude")) {
                // Claude
                messages.add(createMessage("assistant", "好的, 我会按照您的要求批改试卷。"));
            }
        }
    }


    default MessageDTO createImgMessage(String aimodel, String role, String content, boolean isUrl, String... img) {
        return createImgMessage(aimodel, role, content, isUrl, null, img);
    }

    default MessageDTO createImgMessage(String aimodel, String role, String content, boolean isUrl, JSONObject areaObj, String... img) {
        content = replaceChineseChar2EnglishCharInPrompt(content);
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setRole(role);

        JSONArray contentArr = new JSONArray();
        if (img != null && img.length > 0) {
            for (String imgStr : img) {
                JSONObject imgObj = new JSONObject();
                String base64 = imgStr;
                if (isUrl) {
                    // 传递区域信息进行图像处理
                    base64 = "data:image/jpeg;base64," + FileUtil.INSTANCE.url2Base64(imgStr, areaObj);
                }
                if (AIModelConsts.openaiVisionModels.contains(aimodel)) {
                    imgObj.set("type", "image_url");
                    imgObj.set("image_url",
                            JSONUtil.createObj().set("url", base64).set("detail", "high"));
                } else if (AIModelConsts.creatorImageDouBaoModels.contains(aimodel)) {
                    imgObj.set("type", "image_url");
                    JSONObject imageUrl = new JSONObject();
                    imageUrl.set("url", base64);
                    imageUrl.set("detail", "high");
                    imgObj.set("image_url", imageUrl);
                } else {
                    imgObj.set("type", "img");
                    JSONObject sourceObj = new JSONObject();
                    sourceObj.set("type", "base64");
                    sourceObj.set("media_type", "image/jpeg");
                    sourceObj.set("data", base64);
                    imgObj.set("source", sourceObj);
                }
                contentArr.add(imgObj);
            }
        }
        if (StrUtil.isNotBlank(content)) {
            JSONObject contentObj = new JSONObject();
            contentObj.set("type", "text");
            contentObj.set("text", content);
            contentArr.add(contentObj);
        }
        messageDTO.setContent(contentArr);
        return messageDTO;
    }

    ChatCompletionDTO createRxtraQsCompletion(ExtraQsForm params);


}