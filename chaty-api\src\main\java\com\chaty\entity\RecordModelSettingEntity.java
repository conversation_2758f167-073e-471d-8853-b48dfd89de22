package com.chaty.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 模型记录配置表对应实体类
 */
@Data
@TableName("record_model_setting")
public class RecordModelSettingEntity {

    /** 主键，自增 */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 文件 ID */
    private String fileId;

    /** 配置包 ID */
    private String configPackageId;

    private Long testSetId;

    /** 类型：file 或 config 或 testSet（数据库为 ENUM） */
    private String type;

    /** 模型配置 ID */
    private Integer modelSettingId;

    /** 题型/问题类型 */
    private String questionType;

    /** 创建时间，插入时填充 */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新时间，插入和更新时填充 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
