package com.chaty.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("ftp_message_user_title")
public class FtpMessageUserTitle {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String userId;

    private Integer titleId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
