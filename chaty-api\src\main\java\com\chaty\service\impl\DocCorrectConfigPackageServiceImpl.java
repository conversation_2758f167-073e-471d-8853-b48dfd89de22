package com.chaty.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.dto.DocCorrectConfigPackageDTO;
import com.chaty.entity.DocCorrectConfigPackage;
import com.chaty.mapper.DocCorrectConfigPackageMapper;
import com.chaty.security.AuthUtil;
import com.chaty.service.DocCorrectConfigPackageService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

@Service
public class DocCorrectConfigPackageServiceImpl extends ServiceImpl<DocCorrectConfigPackageMapper, DocCorrectConfigPackage>
            implements DocCorrectConfigPackageService {

    @Resource
    private DocCorrectConfigPackageMapper docCorrectConfigPackageMapper;

    @Override
    public String add(DocCorrectConfigPackageDTO param) {
        // 重名检测,如果重复,后面加时间标识以区分
        if (StrUtil.isNotBlank(param.getName())) {
            try {
                LambdaQueryWrapper<DocCorrectConfigPackage> queryWrapper = Wrappers.lambdaQuery();
                queryWrapper.eq(DocCorrectConfigPackage::getName, param.getName());
                List<DocCorrectConfigPackage> list = docCorrectConfigPackageMapper.selectList(queryWrapper);
                if (!list.isEmpty()) {
                    // 加一个年月日时分秒的时间
                    String timestamp = LocalDateTime
                            .now()
                            .format(DateTimeFormatter.ofPattern("yyyyMMddHH:mm:ss"));
                    // 用下划线分隔，拼接到原名称后
                    param.setName(param.getName() + "-" + timestamp);
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }

        }
        docCorrectConfigPackageMapper.insert(param);
        return param.getId();
    }

    @Override
    public void delete(String id) {
        docCorrectConfigPackageMapper.deleteById(id);
    }

    @Override
    public IPage<DocCorrectConfigPackage> page(DocCorrectConfigPackageDTO param) {
        LambdaQueryWrapper<DocCorrectConfigPackage> queryWrapper = Wrappers.<DocCorrectConfigPackage>lambdaQuery()
                .like(StrUtil.isNotBlank(param.getConfig()), DocCorrectConfigPackage::getConfig, param.getConfig())
                .like(StrUtil.isNotBlank(param.getName()), DocCorrectConfigPackage::getName, param.getName())
//                .eq(DocCorrectConfigPackage::getCreator, AuthUtil.getLoginUser().getId())
                .orderByDesc(DocCorrectConfigPackage::getUpdateTime);
        return docCorrectConfigPackageMapper.selectPage(param.getPage().page(), queryWrapper);
    }

    @Override
    public List<DocCorrectConfigPackage> getDocCorrectConfigPackageByConfigId(String configId) {
        LambdaQueryWrapper<DocCorrectConfigPackage> queryWrapper = Wrappers.<DocCorrectConfigPackage>lambdaQuery()
                .like(StrUtil.isNotBlank(configId), DocCorrectConfigPackage::getConfig, configId)
//                .eq(DocCorrectConfigPackage::getCreator, AuthUtil.getLoginUser().getId())
                .orderByDesc(DocCorrectConfigPackage::getUpdateTime);
        return docCorrectConfigPackageMapper.selectList(queryWrapper);
    }

    @Transactional
    @Override
    public String update(DocCorrectConfigPackageDTO param) {
        DocCorrectConfigPackage existed = null;
        if (StrUtil.isNotBlank(param.getId())) {
            existed = docCorrectConfigPackageMapper.selectById(param.getId());
        } else if (StrUtil.isNotBlank(param.getName())) {
            existed =  getByName(param.getName());
        }

        if (Objects.nonNull(existed)) {
            param.setId(existed.getId());
            docCorrectConfigPackageMapper.updateById(param);
        } else {
            param.setId(null);
            add(param);
        }
        return param.getId();
    }

    @Override
    public DocCorrectConfigPackage getById(String id) {
        return docCorrectConfigPackageMapper.selectById(id);
    }

    public DocCorrectConfigPackage getByName(String name) {
        return docCorrectConfigPackageMapper.selectOne(Wrappers.<DocCorrectConfigPackage>lambdaQuery()
                .eq(DocCorrectConfigPackage::getDeleted, 0)
                .eq(DocCorrectConfigPackage::getName, name)
                .orderByDesc(DocCorrectConfigPackage::getCreateTime)
                .last("limit 1"));
    }

}
