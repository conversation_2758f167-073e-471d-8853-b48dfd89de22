package com.chaty.service.impl;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;

import com.chaty.entity.GptAskLogEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import com.chaty.api.qianwen.QianwenApi;
import com.chaty.dto.ChatCompletionDTO;
import com.chaty.service.BasicAiService;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import static com.chaty.util.ModelRequestUtil.mergeModelRequest;

@Slf4j
@Service
public class QianwenAiServiceImpl implements BasicAiService {

    @Resource
    private QianwenApi qianwenApi;
    @Resource
    private WebClient qianwenWebClient;
    @Resource
    private GptAskLogServiceImpl gptAskLogService;

    private Map<String, Object> models = MapUtil
            .builder(new HashMap<String, Object>())
            .put("qwen-turbo", "qwen-turbo")
            .put("qwen-plus", "qwen-plus")
            .put("qwen-max", "qwen-max")
            .build();

    @Override
    public Map<String, Object> chatForCompletion(ChatCompletionDTO param, GptAskLogEntity gptAskLogEntity) {
        Map<String, Object> apiParam = convert2CompletionParam(param);
        // 记录问答开始时间
        gptAskLogEntity.setStartTime(System.currentTimeMillis());
        Map<String, Object> resp = qianwenApi.chatCompletionsV1(apiParam);

        resp.put("$response", JSONUtil.getByPath(JSONUtil.parseObj(resp), "output.text"));
        gptAskLogService.add(BasicAiService.getGptAskLogEntityInAsk(apiParam, resp, gptAskLogEntity));
        return resp;
    }

    @Override
    public Map<String, Object> getFinalCompletion(ChatCompletionDTO param) {
        return convert2CompletionParam(param);
    }

    @Override
    public Flux<String> streamCompletetion(ChatCompletionDTO param) {
        Map<String, Object> apiParam = convert2CompletionParam(param);
        Map<String, Object> parameters = (Map<String, Object>) apiParam.get("parameters");
        if (Objects.isNull(parameters)) {
            parameters = new HashMap<String, Object>();
            apiParam.put("parameters", parameters);
        }
        parameters.put("incremental_output", true);

        return qianwenWebClient.post()
                .uri("/api/v1/services/aigc/text-generation/generation")
                .header("X-DashScope-SSE", "enable")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(apiParam)
                .retrieve()
                .onStatus(HttpStatus::is4xxClientError, clientResponse -> {
                    clientResponse.bodyToMono(String.class).subscribe(body -> {
                        log.error("Error body: {}", body);
                    });
                    log.error("request qianwen ai chat completion failed, {}", clientResponse);
                    return Mono.error(new RuntimeException("request qianwen ai chat completion failed"));
                })
                .bodyToFlux(String.class)
                .map(s -> {
                    if (JSONUtil.isTypeJSONObject(s)) {
                        JSONObject parsed = JSONUtil.parseObj(s);
                        String content = parsed.getByPath("output.text", String.class);
                        String finishReason = parsed.getByPath("output.finish_reason", String.class);
                        if (Objects.equals(finishReason, "stop")) {
                            parsed.set("$end", true);
                        }
                        parsed.set("$content", content);
                        return JSONUtil.toJsonStr(parsed);
                    } else {
                        throw new RuntimeException("unexpected response");
                    }
                });
    }

    private Map<String, Object> convert2CompletionParam(ChatCompletionDTO param) {
        Map<String, Object> apiParam = new HashMap<>();
        apiParam.put("model", models.get(param.getModel()));

        Map<String, Object> inputMap = new HashMap<>();
        inputMap.put("messages", param.getMessages());
        apiParam.put("input", inputMap);

        Map<String, Object> parametersMap = new HashMap<>();
        Optional.ofNullable(param.getMaxTokens())
                .ifPresent(maxTokens -> parametersMap.put("max_tokens", maxTokens));
        Optional.ofNullable(param.getTemperature())
                .ifPresent(temperature -> parametersMap.put("temperature", temperature));
        Optional.ofNullable(param.getTopp())
                .ifPresent(topP -> parametersMap.put("top_p", topP));
        Optional.ofNullable(param.getTopk())
                .ifPresent(topK -> parametersMap.put("top_k", topK));
        Optional.ofNullable(param.getEnableSearch())
                .ifPresent(enableSearch -> parametersMap.put("enable_search", enableSearch));
        apiParam.put("parameters", parametersMap);


        if (Objects.nonNull(param.getModelRequestObj())) {
            apiParam = mergeModelRequest(apiParam, param);
        }
        return apiParam;
    }

    @Override
    public Boolean isSupport(String model) {
        return models.containsKey(model);
    }

}
