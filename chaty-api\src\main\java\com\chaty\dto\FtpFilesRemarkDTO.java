package com.chaty.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class FtpFilesRemarkDTO extends BaseDTO {
    
    private Integer id;
    
    private String filename;

    private String path;
    
    private String modified;
    
    private Integer size;
    
    private String remark;
    
    private String fileId;
    
    private String configPackageId;
    
    private String type;
    
    private String name;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 