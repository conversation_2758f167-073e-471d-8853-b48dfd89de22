package com.chaty.service;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

import org.springframework.web.multipart.MultipartFile;

import cn.hutool.json.JSONObject;

public interface FileService {

    Map<String, Object> saveFile(MultipartFile file);

    String saveFile(byte[] fileData, String filename);

    Map<String, Object> doc2img(MultipartFile file, String filePath, boolean checkPageNum);

    List<List<Map<String, Object>>> uploadMulitePdf(MultipartFile file, JSONObject config);

    List<List<Map<String, Object>>> uploadInputStreamPdf(InputStream inputStream, JSONObject config);

    Map<String, Object> pdf2Img(String filename, Integer pageNum);

    List<List<Map<String, Object>>> uploadMulitePdf(JSONObject configObj);

    Map<String, Object> saveRemoteFile(String url, String type);

    String img2Pdf(MultipartFile file) throws IOException;
    List<List<Map<String, Object>>> imgs2Pdf(List<MultipartFile> files, String config) throws IOException;
}
