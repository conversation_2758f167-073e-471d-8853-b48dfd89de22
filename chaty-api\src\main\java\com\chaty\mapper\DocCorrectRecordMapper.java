package com.chaty.mapper;

import java.sql.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.chaty.dto.DocCorrectRecordDTO;
import com.chaty.entity.DocCorrectRecord;

@Mapper
public interface DocCorrectRecordMapper extends BaseMapper<DocCorrectRecord> {

    @Select("select task_id as taskId, count(1) as recordCount, sum(CASE WHEN status IN (4,5) THEN 1 ELSE 0 END) as finishedCount from doc_correct_record ${ew.customSqlSegment}")
    List<Map<String, Object>> countByStatus(@Param(Constants.WRAPPER) Wrapper<DocCorrectRecord> wrapper);

    @Select("select dcr.*, dct.name as task_name from doc_correct_record dcr left join doc_correct_task dct on dcr.task_id = dct.id left join user_doc ud on ud.doc_id = dcr.id ${ew.customSqlSegment}")
    IPage<DocCorrectRecordDTO> pageByUser(IPage<?> page, @Param(Constants.WRAPPER) Wrapper<DocCorrectRecord> wrapper);

    @Select("select dcr.task_id as taskId, count(1) as recordCount from doc_correct_record dcr where dcr.deleted = 0 and dcr.task_id in ('${taskIds}') group by dcr.task_id")
    List<Map<String, Object>> countByTaskIds(@Param("taskIds") String taskIds);

    @Select("SELECT COUNT(1) AS recordCount " +
            "FROM doc_correct_record dcr " +
            "WHERE dcr.deleted = 0 " +
            "AND dcr.user_id = #{userId} " +  // 通过 user_id 过滤该用户的记录
            "AND dcr.create_time = #{todayDate} " +  // 只查询今天的记录
            "GROUP BY dcr.task_id")
    Integer countTodayRecordsByUser(@Param("userId") String userId, @Param("todayDate") Date todayDate);
}
