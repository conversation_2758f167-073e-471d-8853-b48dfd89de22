package com.chaty.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("model_setting")
public class ModelSetting {
    @TableId(type = IdType.AUTO)
    private Integer id; // 主键ID，自增

    private String name; // 模型请求名称
    private String modelValue; // 模型值（模型标识符）

    private Boolean jsonobject; // 是否返回JSON对象
    private Boolean jsonschema; // 是否返回JSON Schema
    private Boolean enableNormalQsTwoRequest; // 是否启用普通问题二次请求

    private String content; // 请求内容
    private String remark; // 备注
    private Integer weight; // 权重，用于排序

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime; // 创建时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime; // 更新时间

    private String prompt; // 提示词内容

    /** ================= 新增字段 ================= **/

    private Boolean isSecondRoundUseImage;
    // 第二轮请求是否使用图片，默认 false

    private Boolean isSecondRoundJsonComparison;
    // 第二轮是否使用 JSON 比对（true: 使用大模型比对），默认 false

    private Boolean enableImageEnhancement;
    // 图像增强，默认 true

    private String firstRoundPromptType;
    // 第一轮提示词类型：system、user、mixed

    private String secondRoundPromptType;
    // 第二轮提示词类型：system、user、mixed（仅当 isSecondRoundJsonComparison=false 时有效）

    // ===== 新增字段 =====
    private String singleRoundPromptType;
    // 一轮问询提示词类型：system / user / mixed
    // 仅当 enableNormalQsTwoRequest = false（关闭普通题目两轮询问）时使用
    // 仅当 enableNormalQsTwoRequest = false 时使用

    // ===== 新增：是否禁用 =====
    private Boolean disabled;              // 是否禁用（默认 false）

    private String questionType;
}
