package com.chaty.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.dto.DocCorrectRecordDTO;
import com.chaty.dto.DocCorrectTaskDTO;
import com.chaty.dto.UserDocDTO;
import com.chaty.entity.UserDoc;

public interface UserDocService extends IService<UserDoc> {

    public void assignedDocs(UserDocDTO params);

    public IPage<DocCorrectTaskDTO> getUserTask(DocCorrectTaskDTO param);

    public IPage<DocCorrectRecordDTO> getUserDoc(DocCorrectRecordDTO param);

    public List<String> getTaskIds(String userId);

    public List<String> getDocIds(String userId, String taskId);

    public void deleteUserDocs(UserDocDTO params);

    /**
     * 根据用户名自动分配试卷
     */
    public void autoAssign(UserDocDTO params);

}
