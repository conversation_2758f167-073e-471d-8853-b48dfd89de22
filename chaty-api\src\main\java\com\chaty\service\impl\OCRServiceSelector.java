package com.chaty.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import cn.hutool.core.util.StrUtil;
import com.chaty.enums.DefaultCorrectConfigsConsts;
import com.chaty.enums.GetOcrContentType;
import com.chaty.service.OCRService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class OCRServiceSelector {

    @Autowired
    private Map<String, OCRService> ocrServices;

    @Value("${ocr.enableOcr}")
    private Boolean enableOcr;
    @Value("${ocr.enableOcrForIdentifyOrStudentNumber}")
    private Boolean enableOcrForIdentifyOrStudentNumber;
    @Value("${ocr.enableOcrForCorrect}")
    private Boolean enableOcrForCorrect;
    @Value("${ocr.enableOcrForEssay}")
    private Boolean enableOcrForEssay;
    @Value("${ocr.enableOcrForScore}")
    private Boolean enableOcrForScore;
    @Value("${ocr.enableOcrForEXTRAQS}")
    private Boolean enableOcrForEXTRAQS;
    @Value("${ocr.defaultCorrectService}")
    private String defaultCorrectService;

    /**
     * 根据服务名称动态选择 OCR 服务
     *
     * @param serviceName 服务名称，如 "tencentOCRService"
     * @return 选择的 OCRService 实现
     */
    public OCRService getOCRService(String serviceName) {
        return Optional.ofNullable(serviceName)
                .map(ocrServices::get)
                .orElse(ocrServices.get(defaultCorrectService));
    }

    public List<OCRService> getOCRServiceChain(String service, String type) {
        if (!enableOcr) {
            return new ArrayList<>();
        }
        if (type.equals(GetOcrContentType.CORRECT) && !enableOcrForCorrect) {
            return new ArrayList<>();
        }
        if (type.equals(GetOcrContentType.IDENTIFY_OR_STUDENT_NUMBER) && !enableOcrForIdentifyOrStudentNumber) {
            return new ArrayList<>();
        }
        if (type.equals(GetOcrContentType.ESSAY) && !enableOcrForEssay) {
            return new ArrayList<>();
        }
        if (type.equals(GetOcrContentType.SCORE) && !enableOcrForScore) {
            return new ArrayList<>();
        }
        if (type.equals(GetOcrContentType.extraQs) && !enableOcrForEXTRAQS) {
            return new ArrayList<>();
        }
        List<OCRService> ocrServiceChain = new ArrayList<>();
        if (StrUtil.isBlank(service) || service.equals("default")) {
            service = defaultCorrectService;
        }
        // 优先调用service
        OCRService ocrServiceFirst = getOCRService(service);
        if (ocrServiceFirst != null) {
            ocrServiceChain.add(ocrServiceFirst);
        }

        for (String serviceName : DefaultCorrectConfigsConsts.availableOcrServices) {
            if (serviceName.equals(service)) {
                continue;
            }
            OCRService ocrService = getOCRService(serviceName);
            if (ocrService != null) {
                ocrServiceChain.add(ocrService);
            }
        }

        return ocrServiceChain;
    }
}

