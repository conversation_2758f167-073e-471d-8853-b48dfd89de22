package com.chaty.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.dto.ClassInfoDTO;
import com.chaty.dto.SchoolDTO;
import com.chaty.dto.SchoolUserDTO;
import com.chaty.entity.SchoolUser;
import com.chaty.mapper.SchoolUserMapper;
import com.chaty.service.SchoolUserService;

import cn.hutool.core.bean.BeanUtil;

@Service
public class SchoolUserServiceImpl extends ServiceImpl<SchoolUserMapper, SchoolUser> implements SchoolUserService {

    @Override
    public void add(SchoolUserDTO schoolUser) {
        SchoolUser add = BeanUtil.copyProperties(schoolUser, SchoolUser.class);
        SchoolUser existed = findBySchoolIdAndUserId(add.getSchoolId(), add.getUserId());
        if (existed != null) {
            throw new RuntimeException("学校用户已存在");
        }
        this.save(add);
    }

    public SchoolUser findBySchoolIdAndUserId(String schoolId, String userId) {
        return this.baseMapper.selectOne(Wrappers.query(SchoolUser.class)
                .eq("school_id", schoolId)
                .eq("user_id", userId)
                .eq("deleted", false), false);
    }

    @Override
    public void deleteById(String id) {
        this.update(Wrappers.lambdaUpdate(SchoolUser.class)
                .eq(SchoolUser::getId, id)
                .set(SchoolUser::getDeleted, true));
    }

    @Override
    public IPage<?> findPage(SchoolUserDTO params) {
        QueryWrapper<?> wrapper = Wrappers.query()
                .eq(StringUtils.hasText(params.getSchoolId()), "su.school_id", params.getSchoolId())
                .eq(StringUtils.hasText(params.getUserId()), "su.user_id", params.getUserId())
                .like(StringUtils.hasText(params.getUsername()), "u.username", params.getUsername())
                .eq("su.deleted", false)
                .orderByDesc("su.create_time");
        return this.baseMapper.findPage(params.getPage().page(), wrapper);
    }

    @Override
    public List<SchoolDTO> getSchoolList(String userId) {
        Wrapper<?> wrapper = Wrappers.query()
                .eq("su.deleted", false)
                .eq("s.deleted", false)
                .eq("su.user_id", userId)
                .orderByDesc("su.create_time");
        return this.baseMapper.getSchoolList(wrapper);
    }

    @Override
    public void addSchoolUser(String schoolId, String id) {
        SchoolUser add = new SchoolUser();
        add.setSchoolId(schoolId);
        add.setUserId(id);
        this.save(add);
    }

    @Override
    public List<ClassInfoDTO> selectClassByNickname(String nickname) {
        return this.baseMapper.selectClassByNickname(nickname);
    }

}
