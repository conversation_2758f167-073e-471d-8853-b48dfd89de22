\documentclass[a4paper]{article}
\usepackage[absolute, overlay]{textpos}
\usepackage{CJKutf8}
\usepackage{fancyhdr}
\usepackage{pifont}
\usepackage{color}
\usepackage{graphicx}
\usepackage{pdfpages}
\usepackage{pbox}
\pagestyle{fancy}
\fancyhf{}
\renewcommand{\headrulewidth}{0pt}

\setlength{\TPHorizModule}{1mm}
\setlength{\TPVertModule}{1mm}

\begin{document}

<#list revieweds as reviewed>
<#assign reviewedQuestions = questions[reviewed_index]>

<#list reviewedQuestions as question>

% 对错标志
\begin{textblock}{1000}(${question.points[0].x / 300 * 25.4}, ${question.points[0].y / 300 * 25.4})
\begin{minipage}{3cm}
<#if question.status == 2 || question.isTrue>
\textcolor{green}{\fontsize{${context.signSize}pt}{50pt}\selectfont \ding{51}}  % 设置勾的大小
<#else>
\textcolor{red}{\fontsize{${context.signSize}pt}{50pt}\selectfont \ding{55}}  % 设置勾的大小
</#if>
\end{minipage}
\end{textblock}

% 评价
<#if question.review?has_content>
\begin{textblock}{1000}(${2000 / 300 * 25.4}, ${question.points[0].y / 300 * 25.4})
\fbox{
\pbox[t]{50pt}{
\begin{CJK*}{UTF8}{gbsn}
<#if question.review?has_content && question.status == 1>
<#if question.isTrue>
{\fontsize{${context.fontSize}pt}{50pt}\selectfont ${question.review}}
<#else>
\textcolor{red}{\fontsize{${context.fontSize}pt}{50pt}\selectfont ${question.review}}
</#if>
</#if>
\end{CJK*}
}
}
\end{textblock}
</#if>

</#list>

<#if context.preview>

<#assign fileurlParts = reviewed.fileurl?split("/")>
\includepdf[pages=-, frame=true, scale=1, pagecommand={}]{${fileurlParts[2]}}

<#else>

\null
\clearpage

</#if>

</#list>

\end{document}