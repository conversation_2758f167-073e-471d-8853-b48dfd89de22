package com.chaty.service;

import com.chaty.entity.SchoolClass;

import java.util.List;

public interface SchoolClassService {

    List<SchoolClass> selectAll();

    Integer add(SchoolClass schoolClass);

    List<SchoolClass> addBatch(List<SchoolClass> schoolClass);

    boolean classUpdateById(SchoolClass schoolClass);

    Integer deleteById(String id);

    Integer deleteByBatch(List<String> ids);

    List<SchoolClass> selectBySchool(String schoolId);


}
