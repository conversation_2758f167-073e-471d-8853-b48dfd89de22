package com.chaty.controller;

import java.io.File;
import java.io.IOException;
import java.util.*;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.chaty.common.BaseResponse;
import com.chaty.service.FileService;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import static com.chaty.util.FileUtil.convertFileToInputStream;
import static com.chaty.util.MultiImageToPDF.generatePdfFromImages;

@RequestMapping("/api/file")
@RestController
public class FileController {

    @Resource
    private FileService fileService;

    @PostMapping("/upload")
    public BaseResponse<?> upload(@RequestParam MultipartFile file) {
        Map<String, Object> res = fileService.saveFile(file);
        return BaseResponse.ok("上传文件成功", res);
    }

    /**
     * 保存远程文件
     * @param url url地址
     * @param type 文件地址类型 目前只对接alist
     */
    @PostMapping("/upload/remote")
    public BaseResponse<?> saveRemoteFile(@RequestParam String url, @RequestParam(defaultValue = "alist") String type) {
        Map<String, Object> res = fileService.saveRemoteFile(url, type);
        return BaseResponse.ok("上传文件成功", res);
    }

    @PostMapping("/upload/doc2img")
    public BaseResponse<?> uploadDoc2Img(@RequestParam(required = false) MultipartFile file, 
            @RequestParam(required = false) String filePath, boolean checkpageNum) {
        Map<String, Object> res = fileService.doc2img(file, filePath, checkpageNum);
        return BaseResponse.ok("上传文件成功", res);
    }

    @PostMapping("/upload/img2pdf")
    public BaseResponse<?> img2pdf(@RequestParam(required = false) MultipartFile file) throws IOException {
        String outPdfPath = fileService.img2Pdf(file);
        return BaseResponse.ok("上传文件成功", outPdfPath);
    }

    @PostMapping("/upload/multiPdf")
    public BaseResponse<?> uploadMulitePdf(@RequestParam(required = false) MultipartFile file,
            @RequestParam(required = false) String config) {
        JSONObject configObj = null;
        if (Objects.nonNull(config)) {
            configObj = JSONUtil.parseObj(config);
        }
        List<List<Map<String, Object>>> uploadRes = null;
        // 
        if (Objects.nonNull(file)) {
            uploadRes = fileService.uploadMulitePdf(file, configObj);
        } else {
            // 本地路径加载文件
            Objects.requireNonNull(configObj, "文件路径不能为空");
            uploadRes = fileService.uploadMulitePdf(configObj); 
        }
        Object res = uploadRes;
        if (Objects.isNull(config)) {
            res = uploadRes.get(0);
        }
        return BaseResponse.ok("上传文件成功", res);
    }

    @GetMapping("/pdf2Img")
    public BaseResponse<?> pdf2Img(@RequestParam String filename, @RequestParam Integer pageNum) {
        Map<String, Object> res = fileService.pdf2Img(filename, pageNum);
        return BaseResponse.ok(res);
    }

    /**
     * 上传多张图片并转换为一个 PDF 文件
     *
     * @param files  上传的图片文件 (可选)
     * @param config  配置信息，支持 Base64 图片路径等 (可选)
     * @return  生成的 PDF 文件下载链接或本地路径
     */
    @PostMapping("/multiImageToPdf")
    public BaseResponse<?> uploadMultiImageToPdf(
            @RequestParam(required = false) List<MultipartFile> files,
            @RequestParam(required = false) String config) throws IOException {

        List<List<Map<String, Object>>> res = fileService.imgs2Pdf(files, config);
        return BaseResponse.ok("上传文件成功", res);
    }
}
