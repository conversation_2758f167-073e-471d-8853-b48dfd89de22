package com.chaty.service.cache;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chaty.dto.PromptDTO;
import com.chaty.entity.Prompt;
import com.chaty.mapper.PromptMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PromptCacheServiceImpl implements PromptCacheService {

    private final RedisTemplate<String, String> redis;
    private final PromptMapper promptMapper;
    private final ObjectMapper objectMapper = new ObjectMapper();

    private static final String PFX = "prompt:";
    private static final String H = PFX + "h:";               // Hash：prompt:h:{id}
    private static final String J = PFX + "j:";               // JSON：prompt:j:{id}
    private static final String ALL = PFX + "all";            // Set：全部 id
    private static final String ZUPD = PFX + "z:update";      // ZSet：按更新时间

    private static String IDX(String field, String val) { return PFX + "i:" + field + ":" + val; }
    private static String IDX_BOOL(String field, boolean b) { return PFX + "i:" + field + ":" + (b ? "1" : "0"); }

    /** englishName + modelRequestId 精确映射：prompt:x:enmr:{enLower}:{mrKey} -> id */
    private static String EN_MR(String enLower, String mrKey) { return PFX + "x:enmr:" + enLower + ":" + mrKey; }

    private static String norm(String s) { return Optional.ofNullable(s).map(v -> v.trim().toLowerCase()).orElse(""); }
    private static String mrKey(Integer mrId) { return mrId == null ? "null" : String.valueOf(mrId); }

    private static List<String> splitTags(String s) {
        if (!StringUtils.hasText(s)) return Collections.emptyList();
        String[] arr = s.split(",");
        List<String> r = new ArrayList<>();
        for (String a : arr) {
            String t = norm(a);
            if (StringUtils.hasText(t)) r.add(t);
        }
        return r;
    }

    @PostConstruct
    public void warmupOnStart() {
        try { rebuildAll(); } catch (Exception e) { log.warn("Prompt cache warmup failed: {}", e.getMessage()); }
    }

    @Override
    public void rebuildAll() {
        Set<String> oldKeys = redis.keys(PFX + "*");
        if (!CollectionUtils.isEmpty(oldKeys)) redis.delete(oldKeys);

        List<Prompt> list = promptMapper.selectList(null);
        if (CollectionUtils.isEmpty(list)) return;

        for (Prompt p : list) {
            try { doIndex(p, true); } catch (Exception e) { log.error("index error id={}", p.getId(), e); }
        }
        log.info("Prompt cache rebuilt: {}", list.size());
    }

    @Override
    public void index(Prompt prompt) {
        if (prompt == null || prompt.getId() == null) return;
        doIndex(prompt, false);
    }

    @Override
    public void remove(Long id) {
        if (id == null) return;
        String hk = H + id;
        Map<Object, Object> old = redis.opsForHash().entries(hk);
        if (!CollectionUtils.isEmpty(old)) removeFromIndexes(id, old);
        redis.opsForSet().remove(ALL, String.valueOf(id));
        redis.opsForZSet().remove(ZUPD, String.valueOf(id));
        redis.delete(Arrays.asList(hk, J + id));
    }

    private void doIndex(Prompt p, boolean skipRemoveOld) {
        Long id = p.getId();
        String sid = String.valueOf(id);
        String hk = H + sid;

        if (!skipRemoveOld) {
            Map<Object, Object> old = redis.opsForHash().entries(hk);
            if (!CollectionUtils.isEmpty(old)) removeFromIndexes(id, old);
        }

        Map<String, String> map = new LinkedHashMap<>();
        put(map, "id", sid);
        put(map, "englishName", p.getEnglishName());
        put(map, "description", p.getDescription());
        put(map, "name", p.getName());
        put(map, "isMajorType", boolStr(p.getIsMajorType()));
        put(map, "questionType", nvl(p.getQuestionType()));
        put(map, "category", nvl(p.getCategory()));
        put(map, "promptContent", nvl(p.getPromptContent()));
        put(map, "majorStageType", nvl(p.getMajorStageType()));
        put(map, "tags", nvl(p.getTags()));
        put(map, "role", nvl(p.getRole()));
        put(map, "hasImage", boolStr(p.getHasImage()));

        // ★ modelRequestId
        String mrStr = mrKey(p.getModelRequestId());
        put(map, "modelRequestId", mrStr);

        put(map, "createTime", String.valueOf(p.getCreateTime()));
        put(map, "updateTime", String.valueOf(p.getUpdateTime()));
        long ts = Optional.ofNullable(p.getUpdateTime())
                .map(t -> t.toInstant(ZoneOffset.UTC).toEpochMilli())
                .orElse(System.currentTimeMillis());
        put(map, "updateTimeTs", String.valueOf(ts));
        redis.opsForHash().putAll(hk, map);

        try { redis.opsForValue().set(J + sid, objectMapper.writeValueAsString(p), 1, TimeUnit.DAYS); } catch (Exception ignore) {}

        redis.opsForSet().add(ALL, sid);
        redis.opsForZSet().add(ZUPD, sid, ts);

        // 倒排索引
        if (p.getIsMajorType() != null) redis.opsForSet().add(IDX_BOOL("isMajorType", p.getIsMajorType()), sid);
        if (StringUtils.hasText(p.getCategory())) redis.opsForSet().add(IDX("category", norm(p.getCategory())), sid);
        if (StringUtils.hasText(p.getQuestionType())) redis.opsForSet().add(IDX("questionType", norm(p.getQuestionType())), sid);
        if (StringUtils.hasText(p.getMajorStageType())) redis.opsForSet().add(IDX("majorStageType", norm(p.getMajorStageType())), sid);
        if (StringUtils.hasText(p.getRole())) redis.opsForSet().add(IDX("role", norm(p.getRole())), sid);
        if (p.getHasImage() != null) redis.opsForSet().add(IDX_BOOL("hasImage", p.getHasImage()), sid);
        // ★ modelRequestId 维度（null/具体ID）
        redis.opsForSet().add(IDX("modelRequestId", mrStr), sid);
        for (String tag : splitTags(p.getTags())) redis.opsForSet().add(IDX("tag", tag), sid);

        // ★ englishName + modelRequestId 精确映射
        if (StringUtils.hasText(p.getEnglishName())) {
            String enLower = norm(p.getEnglishName());
            redis.opsForValue().set(EN_MR(enLower, mrStr), sid);
            // 仍保留按 englishName 的集合（用于模糊场景）
            redis.opsForSet().add(IDX("englishName", enLower), sid);
        }
    }

    private void removeFromIndexes(Long id, Map<Object, Object> old) {
        String sid = String.valueOf(id);
        String oldCat = norm(str(old.get("category")));
        if (!oldCat.isEmpty()) redis.opsForSet().remove(IDX("category", oldCat), sid);

        String oldQ = norm(str(old.get("questionType")));
        if (!oldQ.isEmpty()) redis.opsForSet().remove(IDX("questionType", oldQ), sid);

        String oldStage = norm(str(old.get("majorStageType")));
        if (!oldStage.isEmpty()) redis.opsForSet().remove(IDX("majorStageType", oldStage), sid);

        String oldRole = norm(str(old.get("role")));
        if (!oldRole.isEmpty()) redis.opsForSet().remove(IDX("role", oldRole), sid);

        String oldIsMajor = str(old.get("isMajorType"));
        if (!oldIsMajor.isEmpty()) {
            boolean b = "1".equals(oldIsMajor) || "true".equalsIgnoreCase(oldIsMajor);
            redis.opsForSet().remove(IDX_BOOL("isMajorType", b), sid);
        }

        String oldHasImage = str(old.get("hasImage"));
        if (!oldHasImage.isEmpty()) {
            boolean b = "1".equals(oldHasImage) || "true".equalsIgnoreCase(oldHasImage);
            redis.opsForSet().remove(IDX_BOOL("hasImage", b), sid);
        }

        String oldTags = str(old.get("tags"));
        for (String tag : oldTags.split(",")) {
            String t = norm(tag);
            if (!t.isEmpty()) redis.opsForSet().remove(IDX("tag", t), sid);
        }

        String oldMr = str(old.get("modelRequestId"));
        if (!oldMr.isEmpty()) redis.opsForSet().remove(IDX("modelRequestId", oldMr), sid);

        String oldEn = norm(str(old.get("englishName")));
        if (!oldEn.isEmpty()) {
            String mrStr = str(old.get("modelRequestId"));
            redis.delete(EN_MR(oldEn, mrStr.isEmpty() ? "null" : mrStr));
            redis.opsForSet().remove(IDX("englishName", oldEn), sid);
        }
    }

    private static void put(Map<String, String> m, String k, String v) { m.put(k, v == null ? "" : v); }
    private static String nvl(String s) { return s == null ? "" : s; }
    private static String boolStr(Boolean b) { return (b != null && b) ? "1" : "0"; }
    private static String str(Object o) { return o == null ? "" : String.valueOf(o); }

    @Override
    public List<Long> searchIds(PromptDTO param) {
        List<String> andKeys = new ArrayList<>();

        // ★ 默认只展示“默认提示词”：modelRequestId = null
        String mrStr = param.getModelRequestId() == null ? "null" : String.valueOf(param.getModelRequestId());
        andKeys.add(IDX("modelRequestId", mrStr));

        if (param.getIsMajorType() != null) andKeys.add(IDX_BOOL("isMajorType", param.getIsMajorType()));
        if (StringUtils.hasText(param.getCategory())) andKeys.add(IDX("category", norm(param.getCategory())));
        if (StringUtils.hasText(param.getQuestionType())) andKeys.add(IDX("questionType", norm(param.getQuestionType())));
        if (StringUtils.hasText(param.getMajorStageType())) andKeys.add(IDX("majorStageType", norm(param.getMajorStageType())));
        if (StringUtils.hasText(param.getRole())) andKeys.add(IDX("role", norm(param.getRole())));
        if (param.getHasImage() != null) andKeys.add(IDX_BOOL("hasImage", param.getHasImage()));
        if (StringUtils.hasText(param.getTags())) {
            for (String t : splitTags(param.getTags())) andKeys.add(IDX("tag", t));
        }

        Set<String> base;
        if (andKeys.size() == 1) base = redis.opsForSet().members(andKeys.get(0));
        else base = redis.opsForSet().intersect(andKeys);

        if (CollectionUtils.isEmpty(base)) return Collections.emptyList();

        String kw = param.getKeyword();
        List<String> candidate = new ArrayList<>(base);
        if (StringUtils.hasText(kw)) {
            final String lkw = kw.toLowerCase();
            candidate = candidate.stream().filter(sid -> {
                String hk = H + sid;
                String name = str(redis.opsForHash().get(hk, "name")).toLowerCase();
                String desc = str(redis.opsForHash().get(hk, "description")).toLowerCase();
                String en = str(redis.opsForHash().get(hk, "englishName")).toLowerCase();
                String content = str(redis.opsForHash().get(hk, "promptContent")).toLowerCase();
                return name.contains(lkw) || desc.contains(lkw) || en.contains(lkw) || content.contains(lkw);
            }).collect(Collectors.toList());
        }

        if (candidate.isEmpty()) return Collections.emptyList();

        Map<String, Double> scoreMap = new HashMap<>(candidate.size() * 2);
        for (String id : candidate) {
            Double score = redis.opsForZSet().score(ZUPD, id);
            if (score == null) {
                String ts = String.valueOf(redis.opsForHash().get(H + id, "updateTimeTs"));
                try { score = Double.valueOf(ts); } catch (Exception ignore) { score = 0D; }
            }
            scoreMap.put(id, score);
        }
        candidate.sort((a, b) -> Double.compare(scoreMap.getOrDefault(b, 0D), scoreMap.getOrDefault(a, 0D)));

        return candidate.stream().map(Long::valueOf).collect(Collectors.toList());
    }

    @Override
    public IPage<Prompt> page(PromptDTO param) {
        Page<Prompt> page = (param == null || param.getPage() == null)
                ? new Page<>(1, 10, true)
                : (Page<Prompt>) param.getPage().page(Prompt.class);

        List<Long> all = searchIds(param);
        long total = all.size();
        if (total == 0L) {
            page.setTotal(0);
            page.setRecords(Collections.emptyList());
            return page;
        }

        int fromIdx = (int) ((page.getCurrent() - 1) * page.getSize());
        int toIdx = Math.min(fromIdx + (int) page.getSize(), (int) total);
        if (fromIdx >= toIdx) {
            page.setTotal(total);
            page.setRecords(Collections.emptyList());
            return page;
        }

        List<Long> slice = all.subList(fromIdx, toIdx);
        List<Prompt> records = slice.stream().map(this::readPromptFromCache).filter(Objects::nonNull).collect(Collectors.toList());
        page.setTotal(total);
        page.setRecords(records);
        return page;
    }

    private Prompt readPromptFromCache(Long id) {
        Map<Object, Object> h = redis.opsForHash().entries(H + id);
        if (CollectionUtils.isEmpty(h)) return null;
        Prompt p = new Prompt();
        p.setId(id);
        p.setEnglishName(String.valueOf(h.getOrDefault("englishName","")));
        p.setDescription(String.valueOf(h.getOrDefault("description","")));
        p.setName(String.valueOf(h.getOrDefault("name","")));
        p.setIsMajorType("1".equals(String.valueOf(h.getOrDefault("isMajorType","0"))));
        p.setQuestionType(String.valueOf(h.getOrDefault("questionType","")));
        p.setCategory(String.valueOf(h.getOrDefault("category","")));
        p.setPromptContent(String.valueOf(h.getOrDefault("promptContent","")));
        p.setMajorStageType(String.valueOf(h.getOrDefault("majorStageType","")));
        p.setTags(String.valueOf(h.getOrDefault("tags","")));
        p.setRole(String.valueOf(h.getOrDefault("role","")));
        p.setHasImage("1".equals(String.valueOf(h.getOrDefault("hasImage","0"))));
        String mrStr = String.valueOf(h.getOrDefault("modelRequestId","null"));
        p.setModelRequestId("null".equals(mrStr) ? null : Integer.valueOf(mrStr));

        String cts = String.valueOf(h.getOrDefault("createTime",""));
        String uts = String.valueOf(h.getOrDefault("updateTime",""));
        try { if (StringUtils.hasText(cts) && !"null".equalsIgnoreCase(cts)) p.setCreateTime(LocalDateTime.parse(cts)); } catch (Exception ignore) {}
        try { if (StringUtils.hasText(uts) && !"null".equalsIgnoreCase(uts)) p.setUpdateTime(LocalDateTime.parse(uts)); } catch (Exception ignore) {}

        return p;
    }
}
