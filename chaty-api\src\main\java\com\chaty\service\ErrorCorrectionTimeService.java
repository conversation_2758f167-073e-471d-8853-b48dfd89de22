package com.chaty.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.entity.ErrorCorrectionTime;
import com.chaty.dto.ErrorCorrectionTimeDTO;

import java.util.List;
import java.util.Map;

public interface ErrorCorrectionTimeService extends IService<ErrorCorrectionTime> {
    
    ErrorCorrectionTimeDTO add(ErrorCorrectionTimeDTO dto);

    List<Map<String, Object>> getStatisticsByFileIds(List<String> fileIds);
} 