package com.chaty.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.common.BaseResponse;
import com.chaty.dto.TestSetDTO;
import com.chaty.entity.TestSetEntity;
import com.chaty.service.TestSetService;
import com.chaty.service.cache.RecordModelSettingService;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/** 测试集 CRUD（仅 DB，无 Redis） */
@RestController
@RequestMapping("/api/testset")
public class TestSetController {

    @Resource
    private TestSetService service;
    @Resource
    private RecordModelSettingService recordModelSettingService;

    /** 分页查询：支持 name / questionTypes 模糊过滤 */
    @GetMapping("/page")
    public BaseResponse<IPage<TestSetEntity>> page(
            @RequestParam(required = false) Integer pageNumber,
            @RequestParam(required = false) Integer pageSize,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String questionTypes
    ) {
        return BaseResponse.ok(service.page(pageNumber, pageSize, name, questionTypes));
    }

    /** 详情 */
    @GetMapping("/{id}")
    public BaseResponse<TestSetEntity> get(@PathVariable Long id) {
        return BaseResponse.ok(service.getById(id));
    }

    /** 新增 */
    @PostMapping("/add")
    public BaseResponse<?> add(@RequestBody TestSetDTO param) {
        TestSetEntity testSetEntity = new TestSetEntity();
        BeanUtils.copyProperties(param, testSetEntity);
        service.add(testSetEntity);

        if (Objects.nonNull(param.getRecordModelSettingEntityList())) {
            param.getRecordModelSettingEntityList().forEach(recordModelSettingEntity -> {
                recordModelSettingEntity.setTestSetId(testSetEntity.getId());
                recordModelSettingEntity.setType("testSet");
                recordModelSettingService.save(recordModelSettingEntity);
            });
        }
        return BaseResponse.ok(testSetEntity);
    }

    /** 更新 */
    @PostMapping("/update")
    public BaseResponse<Long> update(@RequestBody TestSetEntity param) {
        return BaseResponse.ok(service.updateOne(param));
    }

    /** 删除 */
    @DeleteMapping("/{id}")
    public BaseResponse<?> delete(@PathVariable Long id) {
        service.deleteById(id);
        return BaseResponse.ok("删除成功");
    }
}
