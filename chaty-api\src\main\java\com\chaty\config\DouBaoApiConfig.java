package com.chaty.config;

import com.chaty.api.douBao.DouBaoApi;
import com.chaty.api.openai.OpenaiApi;
import com.chaty.exception.BaseException;
import feign.*;
import feign.codec.ErrorDecoder;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import feign.slf4j.Slf4jLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.http.HttpHeaders;

import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
public class DouBaoApiConfig {
    @Value("${api.openai.url:https://api.openai.com/v1}")
    public String apiUrl;
    @Value("${api.doubao.url:https://ark.cn-beijing.volces.com}")
    private String url;
    @Value("${api.doubao.token}")
    private String token;

    @Bean
    public DouBaoApi douBaoAPI() {
        return Feign.builder()
                .options(new Request.Options(10, TimeUnit.SECONDS, 120, TimeUnit.SECONDS, true))
                .encoder(new JacksonEncoder())
                .decoder(new JacksonDecoder())
                .errorDecoder(errorDecoder())
                .logLevel(Logger.Level.FULL)
                .logger(new Slf4jLogger(DouBaoApi.class))
                .requestInterceptor(new RequestInterceptor() {
                    @Override
                    public void apply(RequestTemplate requestTemplate) {
                        requestTemplate.header(HttpHeaders.AUTHORIZATION, "Bearer " + token);
                    }
                })
                .target(DouBaoApi.class, url);
    }

    ErrorDecoder errorDecoder() {
        return (methodKey, response) -> {
            return new BaseException(String.format("豆包请求模型失败: %s", response.status()),
                    new BaseException(response.reason()));
        };
    }

    @Bean
    public WebClient douBaoWebClient() {
        return WebClient.builder().baseUrl(url)
                .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + token)
                .build();
    }
}
