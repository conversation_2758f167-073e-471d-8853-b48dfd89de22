package com.chaty.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.entity.ErrorCorrectionTime;
import com.chaty.dto.ErrorCorrectionTimeDTO;
import com.chaty.exception.BaseException;
import com.chaty.mapper.ErrorCorrectionTimeMapper;
import com.chaty.service.ErrorCorrectionTimeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.util.StrUtil;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ErrorCorrectionTimeServiceImpl extends ServiceImpl<ErrorCorrectionTimeMapper, ErrorCorrectionTime> implements ErrorCorrectionTimeService {

    // 定义时间间隔阈值（2分钟）
    private static final long TIME_INTERVAL_THRESHOLD_SECONDS = 120;

    @Override
    @Transactional
    public ErrorCorrectionTimeDTO add(ErrorCorrectionTimeDTO dto) {
        if (StrUtil.isBlank(dto.getFileId()) || StrUtil.isBlank(dto.getTaskId()) || StrUtil.isBlank(dto.getRecordId())) {
            throw new BaseException("fileId, taskId, and recordId are required for adding");
        }

        ErrorCorrectionTime errorCorrectionTime = new ErrorCorrectionTime();
        BeanUtils.copyProperties(dto, errorCorrectionTime);

        save(errorCorrectionTime);

        ErrorCorrectionTimeDTO result = new ErrorCorrectionTimeDTO();
        BeanUtils.copyProperties(errorCorrectionTime, result);
        return result;
    }

    @Override
    public List<Map<String, Object>> getStatisticsByFileIds(List<String> fileIds) {
        if (fileIds == null || fileIds.isEmpty()) {
            return Collections.emptyList();
        }

        List<Map<String, Object>> resultList = new ArrayList<>();
        
        // 对每个fileId分别处理
        for (String fileId : fileIds) {
            // 查询当前fileId的所有记录
            LambdaQueryWrapper<ErrorCorrectionTime> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ErrorCorrectionTime::getFileId, fileId);
            List<ErrorCorrectionTime> records = list(queryWrapper);

            // 按taskId分组
            Map<String, List<ErrorCorrectionTime>> taskGroups = records.stream()
                    .collect(Collectors.groupingBy(ErrorCorrectionTime::getTaskId));

            // 计算每个task的时间差
            Map<String, Long> times = new HashMap<>();
            for (Map.Entry<String, List<ErrorCorrectionTime>> entry : taskGroups.entrySet()) {
                List<ErrorCorrectionTime> taskRecords = entry.getValue();
                if (taskRecords.size() < 2) {
                    continue; // 跳过记录数少于2的任务
                }

                // 按createTime排序
                List<LocalDateTime> sortedTimes = taskRecords.stream()
                        .map(ErrorCorrectionTime::getCreateTime)
                        .sorted()
                        .collect(Collectors.toList());

                // 计算总时间，排除超过阈值的时间间隔
                long totalSeconds = 0;
                for (int i = 0; i < sortedTimes.size() - 1; i++) {
                    LocalDateTime currentTime = sortedTimes.get(i);
                    LocalDateTime nextTime = sortedTimes.get(i + 1);
                    long intervalSeconds = Duration.between(currentTime, nextTime).getSeconds();
                    
                    // 如果时间间隔小于阈值，计入总时间
                    if (intervalSeconds <= TIME_INTERVAL_THRESHOLD_SECONDS) {
                        totalSeconds += intervalSeconds;
                    }
                }

                if (totalSeconds > 0) {
                    times.put(entry.getKey(), totalSeconds);
                }
            }
            
            // 构建包含fileId的结果Map
            Map<String, Object> fileResult = new HashMap<>();
            fileResult.put("fileId", fileId);
            fileResult.put("times", times);
            resultList.add(fileResult);
        }

        return resultList;
    }
} 