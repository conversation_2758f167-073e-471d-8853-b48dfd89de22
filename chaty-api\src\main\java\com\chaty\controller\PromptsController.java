package com.chaty.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.common.BaseResponse;
import com.chaty.dto.AddQuestionTypeParam;
import com.chaty.dto.ModelSettingDTO;
import com.chaty.dto.UpdatePromptParam;
import com.chaty.entity.ModelSetting;
import com.chaty.entity.Prompt;
import com.chaty.security.AuthUtil;
import com.chaty.service.PromptService;
import com.chaty.service.PromptsRedisService;
import org.springframework.web.bind.annotation.*;
import com.chaty.entity.User;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import com.chaty.dto.PromptDTO;
import static com.chaty.security.TokenAuthConsts.ADMIN_ROLE_ID;

@RestController
@RequestMapping("/api/prompts")
public class PromptsController {

    @Resource
    private PromptService promptService;
    @Resource
    private PromptsRedisService promptsRedisService;

    /** 分页查询（支持 keyword 汇总搜索） */
    @PostMapping("/page")
    public BaseResponse<IPage<Prompt>> page(@RequestBody PromptDTO param) {
        return BaseResponse.ok(promptService.page(param));
    }

    /** 新增 */
    @PostMapping("/add")
    public BaseResponse<?> add(@RequestBody Prompt param) {
        promptService.add(param);
        return BaseResponse.ok("新增成功");
    }

    /** 更新 */
    @PostMapping("/update")
    public BaseResponse<?> update(@RequestBody Prompt param) {
        promptService.update(param);
        return BaseResponse.ok("更新成功");
    }

    /** 删除 */
    @PostMapping("/delete")
    public BaseResponse<?> delete(@RequestParam String id) {
        promptService.delete(id);
        return BaseResponse.ok("删除成功");
    }

    /** 删除 */
    @PostMapping("/deleteByModelSettingId")
    public BaseResponse<?> deleteByModelSettingId(@RequestParam Integer id) {
        if (id == null) {
            return BaseResponse.ok("删除成功");
        }
        PromptDTO promptDTO = new PromptDTO();
        promptDTO.setModelRequestId(id);
        IPage<Prompt> pageData = promptService.page(promptDTO);
        pageData.getRecords().forEach(prompt -> {
            promptService.delete(String.valueOf(prompt.getId()));
        });
        return BaseResponse.ok("删除成功");
    }

    /** 手动重建 Redis 缓存与索引 */
    @PostMapping("/rebuildCache")
    public BaseResponse<?> rebuildCache() {
        promptService.rebuildCache();
        return BaseResponse.ok("重建完成");
    }

    /**
     * 获取题型列表（可选过滤条件）。
     * 默认仅返回“默认提示词”（modelRequestId = null）的题型。
     *
     * GET /api/prompts/questionTypes
     * 可选参数：
     *  - modelRequestId
     *  - isMajorType
     *  - category
     *  - majorStageType
     *  - role
     *  - hasImage
     */
    @GetMapping("/questionTypes")
    public BaseResponse<List<String>> listQuestionTypes(
            @RequestParam(required = false) Integer modelRequestId,
            @RequestParam(required = false) Boolean isMajorType,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String majorStageType,
            @RequestParam(required = false) String role,
            @RequestParam(required = false) Boolean hasImage
    ) {
        List<String> types = promptsRedisService.listQuestionTypes(
                modelRequestId, isMajorType, category, majorStageType, role, hasImage
        );
        return BaseResponse.ok(types);
    }

    @PostMapping("/getPromptsFromModelSettingAuto")
    public BaseResponse<List<Prompt>> getPromptsFromModelSettingAuto(@RequestBody ModelSettingDTO modelSetting) {
        List<Prompt> res = new ArrayList<>();
        if (Boolean.TRUE.equals(modelSetting.getDisabled())) {
            return BaseResponse.ok(res);
        }

        if (StrUtil.isBlank(modelSetting.getQuestionType())) {
            return BaseResponse.ok(res);
        }
        PromptDTO promptDTO = new PromptDTO();
        if (Boolean.TRUE.equals(modelSetting.getEnableNormalQsTwoRequest())) {
            // 两轮问答

            // 第一轮
            if ("mixed".equals(modelSetting.getFirstRoundPromptType())) {
                promptDTO = new PromptDTO();
                promptDTO.setCategory("大题类型");
                promptDTO.setQuestionType(modelSetting.getQuestionType());
                promptDTO.setMajorStageType("第一轮");
                promptDTO.setRole("mix-user");
                res.addAll(promptService.page(promptDTO).getRecords());

                promptDTO = new PromptDTO();
                promptDTO.setCategory("大题类型");
                promptDTO.setQuestionType(modelSetting.getQuestionType());
                promptDTO.setMajorStageType("第一轮");
                promptDTO.setRole("mix-system");
                res.addAll(promptService.page(promptDTO).getRecords());
            } else {
                promptDTO = new PromptDTO();
                promptDTO.setCategory("大题类型");
                promptDTO.setQuestionType(modelSetting.getQuestionType());
                promptDTO.setMajorStageType("第一轮");
                promptDTO.setRole(modelSetting.getFirstRoundPromptType());
                res.addAll(promptService.page(promptDTO).getRecords());
            }

            // 第二轮
            if (!Boolean.TRUE.equals(modelSetting.getIsSecondRoundJsonComparison())) {
                if ("mixed".equals(modelSetting.getSecondRoundPromptType())) {
                    promptDTO = new PromptDTO();
                    promptDTO.setCategory("大题类型");
                    promptDTO.setQuestionType(modelSetting.getQuestionType());
                    promptDTO.setMajorStageType("第二轮");
                    promptDTO.setRole("mix-user");
                    promptDTO.setHasImage(modelSetting.getIsSecondRoundUseImage());
                    res.addAll(promptService.page(promptDTO).getRecords());

                    promptDTO = new PromptDTO();
                    promptDTO.setCategory("大题类型");
                    promptDTO.setQuestionType(modelSetting.getQuestionType());
                    promptDTO.setMajorStageType("第二轮");
                    promptDTO.setRole("mix-system");
                    promptDTO.setHasImage(modelSetting.getIsSecondRoundUseImage());
                    res.addAll(promptService.page(promptDTO).getRecords());
                } else {
                    promptDTO = new PromptDTO();
                    promptDTO.setCategory("大题类型");
                    promptDTO.setQuestionType(modelSetting.getQuestionType());
                    promptDTO.setMajorStageType("第二轮");
                    promptDTO.setRole(modelSetting.getSecondRoundPromptType());
                    promptDTO.setHasImage(modelSetting.getIsSecondRoundUseImage());
                    res.addAll(promptService.page(promptDTO).getRecords());
                }
            }

        } else {
            // 一轮问答
            if ("mixed".equals(modelSetting.getSingleRoundPromptType())) {
                promptDTO = new PromptDTO();
                promptDTO.setCategory("大题类型");
                promptDTO.setQuestionType(modelSetting.getQuestionType());
                promptDTO.setMajorStageType("整体一轮");
                promptDTO.setRole("mix-user");
                res.addAll(promptService.page(promptDTO).getRecords());

                promptDTO = new PromptDTO();
                promptDTO.setCategory("大题类型");
                promptDTO.setQuestionType(modelSetting.getQuestionType());
                promptDTO.setMajorStageType("整体一轮");
                promptDTO.setRole("mix-system");
                res.addAll(promptService.page(promptDTO).getRecords());
            } else {
                promptDTO = new PromptDTO();
                promptDTO.setCategory("大题类型");
                promptDTO.setQuestionType(modelSetting.getQuestionType());
                promptDTO.setMajorStageType("整体一轮");
                promptDTO.setRole(modelSetting.getSingleRoundPromptType());
                res.addAll(promptService.page(promptDTO).getRecords());
            }
        }
        return BaseResponse.ok(res);
    }
}
