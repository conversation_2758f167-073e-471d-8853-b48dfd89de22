package com.chaty.api.HuaShi;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.chaty.exception.RetryException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.List;

@Service
public class HuaShiApi {

    @Value("${api.huashi.key}")
    private String key;

    @Value("${api.huashi.url}")
    private String url;

    public static String readBase64FromFile(String filePath) {
        try {
            // 读取文件内容并返回为字符串
            byte[] bytes = Files.readAllBytes(Paths.get(filePath));
            return new String(bytes, StandardCharsets.UTF_8).trim(); // 读取的内容去除前后空白字符
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static void main(String[] args) {
        try {
            String base64Image = readBase64FromFile("C:\\Users\\<USER>\\Desktop\\base64.txt");
            // 1. 构造请求体
            JSONObject body = new JSONObject();
            body.put("key", "gso98MCdf6DhUsGKb39RNbAJeJt2+dRPoXqhixWn+gpzwzNmMMEgMcIUkeG9XlmcC+cLGQuDc4J9VWP7f0g/cUsLz5yDnBAWyg6jDN+X6lM+ICiuW3SKSCat5DoD/LW65EkqK17hrA=="); // 替换为你的用户密钥
            body.put("method", "POST");
            body.put("action", "beta/ocr");

            // 构造params参数
            JSONObject params = new JSONObject();
            JSONArray jsonArray = new JSONArray();
            jsonArray.add(base64Image);
            params.put("images", jsonArray); // 替换为实际的base64图片数据
            body.put("params", params.toJSONString());
            body.put("timestamp", System.currentTimeMillis() / 1000);

            // 2. 构造请求头
            JSONObject headers = new JSONObject();
//            headers.put("User-Agent", "your-user-agent");
//            headers.put("Host", "api.xhpolaris.com");
            headers.put("Content-Type", "application/json");
            headers.put("Accept-Encoding", "gzip, deflate, br");
            headers.put("Accept", "*/*");
//            headers.put("X-Xh-Env", "test");

            // 3. 生成签名
            String sign = body.toJSONString();
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(sign.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                hexString.append(String.format("%02x", b)); // 转换为十六进制字符串
            }
            String hashedSign = hexString.toString();
            headers.put("Signature", hashedSign);

            // 4. 发送请求
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost("https://api.xhpolaris.com/openapi/call/");
            httpPost.setEntity(new StringEntity(body.toJSONString(), StandardCharsets.UTF_8));

            // 设置请求头
            for (String key : headers.keySet()) {
                httpPost.addHeader(key, headers.getString(key));
            }

            // 执行请求
            CloseableHttpResponse response = httpClient.execute(httpPost);
            try {
                System.out.println("响应状态码: " + response.getStatusLine().getStatusCode());
                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    System.out.println("响应内容: " + EntityUtils.toString(entity));
                }
            } finally {
                response.close();
            }

        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Retryable(value = RetryException.class, backoff = @Backoff(delay = 1000))
    public cn.hutool.json.JSONObject request(String base64Image) {
        try {
            // 1. 构造请求体
            JSONObject body = new JSONObject();
            body.put("key", key);
            body.put("method", "POST");
            body.put("action", "beta/ocr");

            // 构造params参数
            JSONObject params = new JSONObject();
            JSONArray jsonArray = new JSONArray();
            jsonArray.add(base64Image);
            params.put("images", jsonArray); // 替换为实际的base64图片数据
            body.put("params", params.toJSONString());
            body.put("timestamp", System.currentTimeMillis() / 1000);

            // 2. 构造请求头
            JSONObject headers = new JSONObject();
            headers.put("Content-Type", "application/json");
            headers.put("Accept-Encoding", "gzip, deflate, br");
            headers.put("Accept", "*/*");

            // 3. 生成签名
            String sign = body.toJSONString();
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(sign.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                hexString.append(String.format("%02x", b)); // 转换为十六进制字符串
            }
            String hashedSign = hexString.toString();
            headers.put("Signature", hashedSign);

            // 4. 发送请求
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url);
            httpPost.setEntity(new StringEntity(body.toJSONString(), StandardCharsets.UTF_8));

            // 设置请求头
            for (String key : headers.keySet()) {
                httpPost.addHeader(key, headers.getString(key));
            }

            // 执行请求
            CloseableHttpResponse response = httpClient.execute(httpPost);
            try {
                System.out.println("响应状态码: " + response.getStatusLine().getStatusCode());
                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    cn.hutool.json.JSONObject responseObj = JSONUtil.parseObj(EntityUtils.toString(entity));
                    return responseObj.getJSONObject("result");
                }
            } finally {
                response.close();
            }

        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (RetryException | IOException e) {
            e.printStackTrace();
        }
        return null;
    }
}
