package com.chaty.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.volcengine.tos.TOSV2;
import com.volcengine.tos.TOSV2ClientBuilder;

@Configuration
public class VolcengineStorageConfig {

    @Value("${volcengine.tos.region}")
    private String region;

    @Value("${volcengine.tos.endpoint}")
    private String endpoint;

    @Value("${volcengine.tos.accessKey}")
    private String accessKey;

    @Value("${volcengine.tos.secretKey}")
    private String secretKey;

    @Bean
    public TOSV2 tosClient() {
        // 官方初始化方式：build(region, endpoint, accessKey, secretKey)
        System.out.println("Using TOS endpoint: " + endpoint + ", region: " + region + ", bucket: " + endpoint);

        return new TOSV2ClientBuilder().build(region, endpoint, accessKey, secretKey);
    }
}


