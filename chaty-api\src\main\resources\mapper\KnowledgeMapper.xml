<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chaty.mapper.KnowledgeMapper">

    <select id="list">
        select * from knowledge where deleted = 0 
        <if test="keyword != null and keyword != ''">
            and (name like concat('%', #{keyword}, '%') or content like concat('%', #{keyword}, '%'))
        </if>
    </select>

</mapper>