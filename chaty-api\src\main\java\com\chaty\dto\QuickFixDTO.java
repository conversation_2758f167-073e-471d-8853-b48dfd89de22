package com.chaty.dto;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

@Data
public class QuickFixDTO extends BaseDTO {

    private String id;
    /**
     * 模板任务ID
     */
    private String templateId;
    /**
     * 模板任务
     */
    private String templateName;
    /**
     * 纠错任务ID
     */
    private String fixedId;
    /**
     * 纠错任务
     */
    private String fixedName;
    /**
     * 总题目数量
     */
    private Integer totalNum;
    /**
     * 纠错数量
     */
    private Integer fixedNum;
    /**
     * 纠错题目统计数据
     */
    private String qsStats;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 纠错记录ID
     */
    private String fixRecordId;
    /**
     * 纠错结果
     */
    private String fixedReviewed;



    private Integer identifySumCount;

    private Integer identifyRightCount;

    private Integer identifyWrongCount;

    private Double identifyRatio;

    private Integer studentNumberSumCount;

    private Integer studentNumberRightCount;

    private Integer studentNumberWrongCount;

    private Double studentNumberRatio;

    private Integer failNum;

    private Integer correctNum;

    private String stats;

    private String remark;

    private String tenantId;
}