package com.chaty.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.entity.Contact;
import com.chaty.dto.ContactDTO;

public interface ContactService extends IService<Contact> {
    
    ContactDTO addContact(ContactDTO dto);
    
    void deleteContact(Integer id);
    
    ContactDTO updateContact(ContactDTO dto);
    
    IPage<ContactDTO> selectPage(ContactDTO param);
    
    /**
     * 根据ID获取Contact对象
     * @param id Contact ID
     * @return Contact对象
     */
    Contact getContactById(Integer id);
} 