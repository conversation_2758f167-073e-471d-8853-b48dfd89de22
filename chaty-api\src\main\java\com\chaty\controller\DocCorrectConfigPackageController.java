package com.chaty.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chaty.common.BaseResponse;
import com.chaty.dto.DocCorrectConfigDTO;
import com.chaty.dto.DocCorrectConfigPackageDTO;
import com.chaty.dto.PageDTO;
import com.chaty.dto.ScoreTypesDTO;
import com.chaty.entity.DocCorrectConfig;
import com.chaty.entity.DocCorrectConfigPackage;
import com.chaty.entity.DocCorrectFile;
import com.chaty.exception.BaseException;
import com.chaty.mapper.DocCorrectConfigPackageMapper;
import com.chaty.service.DocCorrectConfigPackageService;
import com.chaty.service.DocCorrectConfigService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.chaty.util.ScoreUtils.mergeScoreTypesFromDTO;

@RequestMapping("/api/docCorrectConfigPackage")
@RestController
public class DocCorrectConfigPackageController {

    @Resource
    private DocCorrectConfigPackageService docCorrectConfigPackageService;
    @Resource
    private DocCorrectConfigPackageMapper docCorrectConfigPackageMapper;
    @Resource
    private DocCorrectConfigService docCorrectConfigService;

    @PostMapping("/page")
    public BaseResponse<?> page(@RequestBody DocCorrectConfigPackageDTO param) {
        IPage<DocCorrectConfigPackage> res = docCorrectConfigPackageService.page(param);
        return BaseResponse.ok("查询成功", res);
    }

    @PostMapping("/add")
    public BaseResponse<?> add(@RequestBody DocCorrectConfigPackageDTO param) {
        String id = docCorrectConfigPackageService.add(param);
        return BaseResponse.ok("添加成功", MapUtil.of("id", id));
    }

    @PostMapping("/update")
    public BaseResponse<?> update(@RequestBody DocCorrectConfigPackageDTO param) {
        String id = docCorrectConfigPackageService.update(param);
        return BaseResponse.ok("修改成功", MapUtil.of("id", id));
    }

    @PostMapping("/delete")
    public BaseResponse<?> delete(String id) {
        docCorrectConfigPackageService.delete(id);
        return BaseResponse.ok("删除成功");
    }

    @PostMapping("/deleteConfig")
    @Transactional
    public BaseResponse<?> delete(@RequestParam("configID") String configId, @RequestParam("packageId") String packageId) {

        DocCorrectConfigPackage docCorrectConfigPackage = docCorrectConfigPackageService.getById(packageId);
        if (StrUtil.isNotBlank(docCorrectConfigPackage.getConfig())) {
            JSONArray jsonArray = JSONUtil.parseArray(docCorrectConfigPackage.getConfig());
            if (jsonArray.contains(configId)) {
                jsonArray.remove(configId);
                docCorrectConfigService.delete(configId);

                // 删除
                DocCorrectConfigPackageDTO docCorrectConfigPackageDTO = new DocCorrectConfigPackageDTO();
                docCorrectConfigPackageDTO.setId(packageId);
                docCorrectConfigPackageDTO.setConfig(jsonArray.toString());
                docCorrectConfigPackageDTO.setName(docCorrectConfigPackage.getName());
                docCorrectConfigPackageService.update(docCorrectConfigPackageDTO);
            } else {
                throw new BaseException("删除失败，配置包中不存在该配置");
            }
        } else {
            throw new BaseException("删除失败，配置包中不存在配置");
        }
        return BaseResponse.ok("删除成功");
    }

    @GetMapping("/get")
    public BaseResponse<?> getById(String id) {
        DocCorrectConfigPackage res = docCorrectConfigPackageService.getById(id);
        return BaseResponse.ok(res);
    }

    /**
     * getScoreTypeByFileId不是configPackageId
     * @param id
     * @return
     */
    @GetMapping("/getScoreTypeByFileId")
    public BaseResponse<?> getScoreTypeByFileId(String id) {
        List<DocCorrectConfigPackage> docCorrectConfigPackages = docCorrectConfigPackageService.getDocCorrectConfigPackageByConfigId(id);
        if (docCorrectConfigPackages.isEmpty() || docCorrectConfigPackages.size() != 1) {
            DocCorrectConfig docCorrectConfig = docCorrectConfigService.getById(id);
            DocCorrectConfigDTO docCorrectConfigDTO = BeanUtil.copyProperties(docCorrectConfig, DocCorrectConfigDTO.class);
            List<DocCorrectConfigDTO> docCorrectConfigDTOS = new ArrayList<>();
            docCorrectConfigDTOS.add(docCorrectConfigDTO);
            return BaseResponse.ok(mergeScoreTypesFromDTO(docCorrectConfigDTOS));
        } else {
            DocCorrectConfigPackage docCorrectConfigPackage = docCorrectConfigPackages.get(0);
            JSONArray jsonArray = JSONUtil.parseArray(docCorrectConfigPackage.getConfig());
            List<DocCorrectConfigDTO> docCorrectConfigDTOS = new ArrayList<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                DocCorrectConfig docCorrectConfig = docCorrectConfigService.getById(jsonArray.getStr(i));
                DocCorrectConfigDTO docCorrectConfigDTO = BeanUtil.copyProperties(docCorrectConfig, DocCorrectConfigDTO.class);
                docCorrectConfigDTOS.add(docCorrectConfigDTO);
            }
            return BaseResponse.ok(mergeScoreTypesFromDTO(docCorrectConfigDTOS));
        }
    }

    @GetMapping("/checkName")
    public BaseResponse<?> checkName(@RequestParam String name) {
        QueryWrapper<DocCorrectConfigPackage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", name);
        List<DocCorrectConfigPackage> docCorrectFiles = docCorrectConfigPackageMapper.selectList(queryWrapper);
        if (!docCorrectFiles.isEmpty()) {
            return BaseResponse.error("文件名已存在");
        } else{
            return BaseResponse.ok("文件名可用");
        }
    }

    @GetMapping("/getDeep")
    public BaseResponse<?> getDeep(String id) {
        Map<String,Object> res = new HashMap<>();
        if (StrUtil.isBlank(id)) {
            return BaseResponse.error("id为空");
        }
        DocCorrectConfigPackage docCorrectConfigPackage = docCorrectConfigPackageMapper.selectById(id);
        String configIds = docCorrectConfigPackage.getConfig();
        if (StrUtil.isNotBlank(configIds)) {
            JSONArray ids = JSONUtil.parseArray(configIds);
            List<DocCorrectConfig> docCorrectConfigs = new ArrayList<>();
            for (Object configId : ids) {
                docCorrectConfigs.add(docCorrectConfigService.getById((String) configId));
            }
            res.put("configs", docCorrectConfigs);
        }
        res.put("configPackage", docCorrectConfigPackage);
        return BaseResponse.ok(res);
    }

    @GetMapping("/getAllScoreTypeScore")
    public BaseResponse<?> getAllScoreTypeScore(String id) {
        ScoreTypesDTO res = new ScoreTypesDTO();
        if (StrUtil.isBlank(id)) {
            return BaseResponse.error("id为空");
        }
        DocCorrectConfigPackage docCorrectConfigPackage = docCorrectConfigPackageMapper.selectById(id);
        if (docCorrectConfigPackage == null) {
            List<DocCorrectConfigPackage> docCorrectConfigPackages = docCorrectConfigPackageService.getDocCorrectConfigPackageByConfigId(id);
            if (docCorrectConfigPackages.isEmpty()) {
                return BaseResponse.error("未找到配置套卷！");
            } else if(docCorrectConfigPackages.size() != 1) {
                return BaseResponse.error("找到多个配置套卷-无法确定");
            } else {
                docCorrectConfigPackage = docCorrectConfigPackages.get(0);
            }

        }
        String configIds = docCorrectConfigPackage.getConfig();
        if (StrUtil.isNotBlank(configIds)) {
            JSONArray ids = JSONUtil.parseArray(configIds);
            if (ids.isEmpty()) {
                return BaseResponse.error("该试卷下无标准卷");
            }
            for (Object configId : ids) {
                DocCorrectConfig docCorrectConfig = docCorrectConfigService.getById((String) configId);
                DocCorrectConfigDTO docCorrectConfigDTO = BeanUtil.copyProperties(docCorrectConfig, DocCorrectConfigDTO.class);
                res.merge(docCorrectConfigDTO.getScoreType());
            }
        } else {
            return BaseResponse.error("该试卷下无标准卷");
        }
        return BaseResponse.ok(res);
    }

    @PostMapping("/onSyncToOtherConfigs/{id}")
    public BaseResponse<?> onSyncToOtherConfigs(@PathVariable("id") String id, @RequestBody JSONObject form) {
        ScoreTypesDTO res = new ScoreTypesDTO();
        if (StrUtil.isBlank(id)) {
            return BaseResponse.error("id为空");
        }
        DocCorrectConfigPackage docCorrectConfigPackage = docCorrectConfigPackageMapper.selectById(id);
        if (docCorrectConfigPackage == null) {
            List<DocCorrectConfigPackage> docCorrectConfigPackages = docCorrectConfigPackageService.getDocCorrectConfigPackageByConfigId(id);
            if (docCorrectConfigPackages.isEmpty()) {
                return BaseResponse.error("未找到配置套卷！");
            } else if(docCorrectConfigPackages.size() != 1) {
                return BaseResponse.error("找到多个配置套卷-无法确定");
            } else {
                docCorrectConfigPackage = docCorrectConfigPackages.get(0);
            }
        }
        String configIds = docCorrectConfigPackage.getConfig();
        if (StrUtil.isNotBlank(configIds)) {
            JSONArray ids = JSONUtil.parseArray(configIds);
            if (ids.isEmpty()) {
                return BaseResponse.error("该试卷下无标准卷");
            }
            for (Object configId : ids) {
                DocCorrectConfig docCorrectConfig = docCorrectConfigService.getById((String) configId);
                String configStr = docCorrectConfig.getConfig();
                if (StrUtil.isBlank(configStr)) {
                    return BaseResponse.error("configObj为空！");
                }
                JSONObject configObj = JSONUtil.parseObj(configStr);

                if (form.containsKey("fontSize")) {
                    configObj.set("fontSize", form.get("fontSize"));
                }
                if (form.containsKey("flagSize")) {
                    configObj.set("flagSize", form.get("flagSize"));
                }
                if (form.containsKey("flagColor")) {
                    configObj.set("flagColor", form.get("flagColor"));
                }
                if (form.containsKey("errorFlagColor")) {
                    configObj.set("errorFlagColor", form.get("errorFlagColor"));
                }
                if (form.containsKey("scoreColor")) {
                    configObj.set("scoreColor", form.get("scoreColor"));
                }
                if (form.containsKey("scoreFontSize")) {
                    configObj.set("scoreFontSize", form.get("scoreFontSize"));
                }
                if (form.containsKey("errorFlagSize")) {
                    configObj.set("errorFlagSize", form.get("errorFlagSize"));
                }
                if (form.containsKey("errorFlag")) {
                    configObj.set("errorFlag", form.get("errorFlag"));
                }
                if (form.containsKey("correctFlag")) {
                    configObj.set("correctFlag", form.get("correctFlag"));
                }

                DocCorrectConfig needUpdate = new DocCorrectConfig();
                needUpdate.setConfig(JSONUtil.toJsonStr(configObj));
                needUpdate.setId((String) configId);
                docCorrectConfigService.updateById(needUpdate);
            }
        } else {
            return BaseResponse.error("该试卷下无标准卷");
        }
        return BaseResponse.ok(res);
    }

    /**
     * 查询 30 分钟内最新的一条配置包记录
     * <p>
     * 1. 以 update_time（或 create_time，视实际表字段而定）>= 当前时间 - 30 分钟 作为筛选条件；
     * 2. 按 update_time 倒序，LIMIT 1；
     * 3. 若无符合条件的记录，则返回 null；若多条，则返回最新的那一条。
     */
    @GetMapping("/latestWithin30Min")
    public BaseResponse<DocCorrectConfigPackage> getLatestWithin30Min() {
        LocalDateTime threshold = LocalDateTime.now().minusMinutes(30);

        QueryWrapper<DocCorrectConfigPackage> query = new QueryWrapper<>();
        query.ge("update_time", threshold)
                .orderByDesc("update_time")
                .last("LIMIT 1");

        DocCorrectConfigPackage latest = docCorrectConfigPackageMapper.selectOne(query);

        return BaseResponse.ok(latest);
    }
}
