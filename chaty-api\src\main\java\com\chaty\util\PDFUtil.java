package com.chaty.util;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import javax.imageio.ImageIO;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.apache.pdfbox.rendering.PDFRenderer;

import com.chaty.exception.BaseException;

import cn.hutool.core.io.IoUtil;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PDFUtil {

    private static int DPI = 300;

    public static void convert2Img(String pdfPath, String imgPath) {
        convert2Img(pdfPath, imgPath, null);
    }

    public static JSONObject convert2ImgWithImgInfo(String pdfPath, String imgPath) {
        return convert2ImgWithImgInfo(pdfPath, imgPath, null);
    }

    /**
     * Converts a PDF to an image.
     *
     * @param pdfPath the path to the PDF
     * @param imgPath the path to save the image
     */
    public static void convert2Img(String pdfPath, String imgPath, Integer pageIndex) {
        try {
            PDDocument document = PDDocument.load(new File(pdfPath));
            PDFRenderer pdfRenderer = new PDFRenderer(document);
            BufferedImage bim = pdfRenderer.renderImageWithDPI(Optional.ofNullable(pageIndex).orElse(0), DPI);
            ImageIO.write(bim, "JPEG", new File(imgPath));
            if (Objects.nonNull(pageIndex)) {
                // 将指定页数的PDF保存
                PDPage page = document.getPage(pageIndex);
                // 创建新的 PDF 文档
                PDDocument destinationDocument = new PDDocument();
                destinationDocument.addPage(page);
                destinationDocument.save(imgPath.replace(".jpg", ".pdf"));
                destinationDocument.close();
            }
            document.close();
        } catch (IOException e) {
            throw new BaseException("文档转图片失败!", e);
        }
    }

    /**
     * 返回图片的长宽
     *
     * @param pdfPath
     * @param imgPath
     * @param pageIndex
     * @return
     */
    public static JSONObject convert2ImgWithImgInfo(String pdfPath, String imgPath, Integer pageIndex) {
        try {
            PDDocument document = PDDocument.load(new File(pdfPath));
            PDFRenderer pdfRenderer = new PDFRenderer(document);
            BufferedImage bim = pdfRenderer.renderImageWithDPI(Optional.ofNullable(pageIndex).orElse(0), DPI);
            ImageIO.write(bim, "JPEG", new File(imgPath));

            if (Objects.nonNull(pageIndex)) {
                PDPage page = document.getPage(pageIndex);
                PDDocument destinationDocument = new PDDocument();
                destinationDocument.addPage(page);
                destinationDocument.save(imgPath.replace(".jpg", ".pdf"));
                destinationDocument.close();
            }

            int widthPx = bim.getWidth();
            int heightPx = bim.getHeight();

            // 转换为毫米（单位换算）
            double widthMM = widthPx * 25.4 / DPI;
            double heightMM = heightPx * 25.4 / DPI;

            // 若为横向页面，交换长宽后再判断
            double w = widthMM;
            double h = heightMM;
            if (widthMM > heightMM) {
                double temp = w;
                w = h;
                h = temp;
            }

            // 判断 docType
            String docType;
            if ((w >= 840 && h >= 1180) || (w >= 1180 && h >= 840)) {
                docType = "A0";
            } else if ((w >= 590 && h >= 840) || (w >= 840 && h >= 590)) {
                docType = "A1";
            } else if ((w >= 420 && h >= 590) || (w >= 590 && h >= 420)) {
                docType = "A2";
            } else if ((w >= 360 && h >= 515) || (w >= 515 && h >= 360)) {
                docType = "4K";
            } else if ((w >= 267 && h >= 390) || (w >= 390 && h >= 267)) {
                docType = "A3";
            } else if ((w >= 247 && h >= 370) || (w >= 370 && h >= 247)) {
                docType = "8K";
            } else if ((w >= 190 && h >= 277) || (w >= 277 && h >= 190)) {
                docType = "A4";
            } else if ((w >= 128 && h >= 190) || (w >= 190 && h >= 128)) {
                docType = "A5";
            } else {
                docType = "Smaller";
            }




            JSONObject obj = new JSONObject();
            obj.set("width", String.format("%.2f", widthMM));
            obj.set("height", String.format("%.2f", heightMM));
            obj.set("x", 0);
            obj.set("y", 0);
            obj.set("docType", docType);

            document.close();
            return obj;
        } catch (IOException e) {
            throw new BaseException("文档转图片失败!", e);
        }
    }


    /**
     * 截取PDF文档的部分区域转为图片
     */
    public static void addTextToPDF(String filePath, String text, int x, int y, int width, int height) {
        try {
            PDDocument document = PDDocument.load(new File(filePath));
            PDPage page = document.getPage(0);
            PDPageContentStream contentStream = new PDPageContentStream(document, page,
                    PDPageContentStream.AppendMode.APPEND, true, true);

            // 获取page的宽度和高度
            float pageWidth = page.getMediaBox().getWidth();
            float pageHeight = page.getMediaBox().getHeight();
            log.info("width: {}, height: {}", pageWidth, pageHeight);

            int fontSize = 12;
            PDType0Font font = PDType0Font.load(document, PDFUtil.class.getResourceAsStream("/font/STFANGSO.TTF"));
            String[] words = text.split("");
            contentStream.beginText();
            contentStream.setFont(font, fontSize);
            contentStream.newLineAtOffset(pixel2point(x) + 10, pageHeight - pixel2point(y) - 15);
            float currentWidth = 0;
            int line = 0;
            for (String word : words) {
                if ("\\n".equals(word) || "\n".equals(words)) {
                    currentWidth = 0;
                    line++;
                    contentStream.newLineAtOffset(0, -15);
                    continue;
                }
                float wordWidth = fontSize * font.getStringWidth(word) / 1000;
                if (currentWidth + wordWidth < pixel2point(width - 20)) {
                    contentStream.showText(word);
                    currentWidth += wordWidth;
                } else {
                    contentStream.newLineAtOffset(0, -15);
                    contentStream.showText(word);
                    currentWidth = wordWidth;
                    line++;
                }
            }
            contentStream.endText();

            contentStream.setStrokingColor(Color.BLACK);
            contentStream.setLineWidth(1f);
            int recHeight = (line + 2) * 15;
            contentStream.addRect(pixel2point(x), pageHeight - pixel2point(y) - recHeight, pixel2point(width) + 20,
                    recHeight);
            contentStream.stroke();

            contentStream.close();
            document.save(filePath);
            document.close();
        } catch (IOException e) {
            throw new BaseException("文档添加文本失败", e);

        }
    }

    // private int addParagraph(PDPageContentStream contentStream, PDDocument
    // document, String text, int x, int y,
    // int width, int height) throws IOException {
    // String[] words = text.split("");
    // int line = 0;
    // int fontSize = 12;
    // PDType0Font font = PDType0Font.load(document,
    // PDFUtil.class.getResourceAsStream("/LXGWWenKaiScreenR.ttf"));

    // contentStream.beginText();
    // contentStream.setFont(font, fontSize);
    // contentStream.setLeading(TEXT_LEADING);

    // contentStream.newLineAtOffset(x + 10, y - 10);
    // float currentWidth = 0;
    // for (String word : words) {
    // if ("\n".equals(word)) {
    // currentWidth = 0;
    // line++;
    // contentStream.newLineAtOffset(0, -TEXT_LEADING);
    // }
    // float wordWidth = fontSize * font.getStringWidth(word) / 1000;
    // if (currentWidth + wordWidth < (width - 20)) {
    // contentStream.showText(word + "");
    // currentWidth += wordWidth;
    // } else {
    // contentStream.newLineAtOffset(0, -TEXT_LEADING);
    // contentStream.showText(word + " ");
    // currentWidth = wordWidth;
    // }
    // }

    // }

    public synchronized static void extractImageFromPDF(String filePath, String imagePath, int x, int y, int width,
                                                        int height, List<JSONObject> markAreas) {
        extractImageFromPDF(filePath, imagePath, x, y, width, height, markAreas, 0);
    }

    /**
     * 截取指定PDF的区域保存为图片
     */
    public synchronized static void extractImageFromPDF(String filePath, String imagePath, int x, int y, int width,
                                                        int height, List<JSONObject> markAreas, Integer rightRotation) {
        try {
            PDDocument document = PDDocument.load(new File(filePath));
            PDFRenderer renderer = new PDFRenderer(document);
            BufferedImage image = renderer.renderImageWithDPI(0, DPI); // Render the first page with 300 DPI
            extractImageFromImage(image, imagePath, x, y, width, height, markAreas, rightRotation);
            document.close();
        } catch (Exception e) {
            throw new BaseException("文档截取图片失败!", e);
        }
    }

    /**
     * 获取文档图片
     */
    public static BufferedImage getDocumentImage(String filePath) {
        PDDocument document = null;
        try {
            document = PDDocument.load(new File(filePath));
            PDFRenderer renderer = new PDFRenderer(document);
            return renderer.renderImageWithDPI(0, DPI); // Render the first page with 300 DPI
        } catch (Exception e) {
            throw new BaseException("文档截取图片失败!", e);
        } finally {
            IoUtil.close(document);
        }
    }

    public static void extractImageFromImage(BufferedImage image, String imagePath, int x, int y, int width,
                                             int height, List<JSONObject> markAreas) {
        extractImageFromImage(image, imagePath, x, y, width, height, markAreas, 0);
    }

    /**
     * 截取图片
     */
    public static void extractImageFromImage(BufferedImage image, String imagePath, int x, int y, int width,
                                             int height, List<JSONObject> markAreas, int rotationAngle) {
        try {
            int imageWidth = image.getWidth();
            x = Math.max(0, x);
            y = Math.max(0, y);
            if (x + width > imageWidth) {
                log.warn("图片截取超出文档, x: {}, width: {}, imageWidth: {}", x, width, imageWidth);
                width = imageWidth - x;
            }
            int imageHeight = image.getHeight();
            if (y + height > imageHeight) {
                log.warn("图片截取超出文档, y: {}, height: {}, imageHeight: {}", y, height, imageHeight);
                height = imageHeight - y;
            }

            BufferedImage subImage = image.getSubimage(x, y, width, height); // Extract the area

            // 添加区域标记
            if (Objects.nonNull(markAreas) && markAreas.size() > 0) {
                drawRectWithImage(subImage, markAreas);
            }

            int normalizedAngle = ((rotationAngle % 360) + 360) % 360;
            BufferedImage rotatedImage = subImage;
            if (normalizedAngle != 0) {
                if (normalizedAngle % 90 != 0) {
                    throw new IllegalArgumentException("旋转角度必须为90的倍数！");
                }
                // 根据旋转角度进行处理
                if (normalizedAngle == 90) {
                    rotatedImage = new BufferedImage(subImage.getHeight(), subImage.getWidth(), subImage.getType());
                    Graphics2D g2d = rotatedImage.createGraphics();
                    // 先平移，再旋转90度顺时针
                    g2d.translate(subImage.getHeight(), 0);
                    g2d.rotate(Math.toRadians(90));
                    g2d.drawImage(subImage, 0, 0, null);
                    g2d.dispose();
                } else if (normalizedAngle == 180) {
                    rotatedImage = new BufferedImage(subImage.getWidth(), subImage.getHeight(), subImage.getType());
                    Graphics2D g2d = rotatedImage.createGraphics();
                    // 平移到右下角后旋转180度
                    g2d.translate(subImage.getWidth(), subImage.getHeight());
                    g2d.rotate(Math.toRadians(180));
                    g2d.drawImage(subImage, 0, 0, null);
                    g2d.dispose();
                } else if (normalizedAngle == 270) {
                    rotatedImage = new BufferedImage(subImage.getHeight(), subImage.getWidth(), subImage.getType());
                    Graphics2D g2d = rotatedImage.createGraphics();
                    // 平移到下边后旋转270度（或逆时针90度）
                    g2d.translate(0, subImage.getWidth());
                    g2d.rotate(Math.toRadians(270));
                    g2d.drawImage(subImage, 0, 0, null);
                    g2d.dispose();
                }
            }

            ImageIO.write(rotatedImage, "JPG", new File(imagePath)); // Save the image to a file
        } catch (Exception e) {
            throw new BaseException("文档截取图片失败!", e);
        }
    }

    public static void extractImageFromPDF(String filePath, String imagePath, int x, int y, int width,
                                           int height) {
        extractImageFromPDF(filePath, imagePath, x, y, width, height, null);
    }

    public static void drawRectWithImage(BufferedImage image, List<JSONObject> markAreas) {
        // 获取图片的 Graphics 对象
        Graphics g = image.getGraphics();

        // 设置颜色
        g.setColor(Color.GREEN);

        // 绘制矩形（x, y, width, height）
        markAreas.forEach(area -> {
            JSONObject markArea = (JSONObject) area;
            int x = markArea.getInt("x");
            int y = markArea.getInt("y");
            int width = markArea.getInt("width");
            int height = markArea.getInt("height");
            g.drawRect(x, y, width, height);
        });

        // 释放 Graphics 对象
        g.dispose();
    }

    private static float pixel2point(int pixel) {
        return pixel * 72f / DPI;
    }

    /**
     * 获取 PDF 页数
     *
     * @param filePath
     * @return
     */
    public static int getPageNum(String filePath) {
        try {
            PDDocument document = PDDocument.load(new File(filePath));
            return document.getNumberOfPages();
        } catch (Exception e) {
            throw new BaseException("获取文档页数失败!", e);
        }
    }

    /**
     * 获取文档信息
     */
    public static JSONObject getPDFInfo(String filePath) {
        File file = new File(filePath);
        // 如果文件不存在，则返回默认参数
        if (!file.exists()) {
            return new JSONObject()
                    .set("width", 210)
                    .set("height", 297);
        }

        PDDocument document = null;
        try {
            document = PDDocument.load(file);
            PDPage page = document.getPage(0);
            PDRectangle mediaBox = page.getCropBox();
            int rotation = page.getRotation();
            // 高度和宽度转换为毫米
            float width = mediaBox.getWidth() * 25.4f / 72;
            float height = mediaBox.getHeight() * 25.4f / 72;

            // 考虑页面的旋转
            if (rotation == 90 || rotation == 270) {
                // 交换宽度和高度
                float temp = width;
                width = height;
                height = temp;
            }

            return new JSONObject()
                    .set("width", width)
                    .set("height", height);
        } catch (Exception e) {
            // 如果处理过程中出现异常，也返回默认参数
            return new JSONObject()
                    .set("width", 210)
                    .set("height", 297);
        } finally {
            if (document != null) {
                try {
                    document.close();
                } catch (IOException ioe) {
                    // 可以在这里记录日志，但通常不影响默认参数的返回
                }
            }
        }
    }


}
