package com.chaty.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.entity.TestSetImageResultEntity;
import com.chaty.enums.TestSetResultStatus;

public interface TestSetImageResultService extends IService<TestSetImageResultEntity> {

    IPage<TestSetImageResultEntity> page(Integer pageNumber, Integer pageSize,
                                         Long testSetId, Long imageId,
                                         String questionType, TestSetResultStatus status,
                                         Boolean success);

    Long add(TestSetImageResultEntity param);     // 写库并自动补快照
    Long updateOne(TestSetImageResultEntity param);
    void deleteById(Long id);
}
