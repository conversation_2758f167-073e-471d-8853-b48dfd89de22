package com.chaty.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.dto.DocCorrectConfigPackageDTO;
import com.chaty.entity.DocCorrectConfigPackage;

import java.util.List;

public interface DocCorrectConfigPackageService extends IService<DocCorrectConfigPackage> {
    IPage<DocCorrectConfigPackage> page(DocCorrectConfigPackageDTO param);

    List<DocCorrectConfigPackage> getDocCorrectConfigPackageByConfigId(String configId);

    String add(DocCorrectConfigPackageDTO param);

    String update(DocCorrectConfigPackageDTO param);

    void delete(String id);

    DocCorrectConfigPackage getById(String id);
}
