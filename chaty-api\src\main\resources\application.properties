spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8

spring.session.store-type=redis
spring.session.timeout=3d
spring.session.redis.namespace=myapp:sessions

# mathpix api
api.mathpix.app-id=zhejianguniversity_536b41_49f14d
api.mathpix.app-key=9f036bf314c1a04f9f54667dba482ef3c35f18770fbd8b2aa516f5d77c53f9bd

#
api.baiduai.clientId=R0GbYUyG8eyjpwmCnHukl3eF
api.baiduai.clientSecret=yLsDhaqB7Cp83d5OWF0s7qtFiacNRfah

# aws
api.aws.ak=5f08d3af531411eeb4eb0242ac120004
api.aws.sk=0LRim57ZFt%CIUA%8kN0pNUj/w-n47Is
api.aws.endpoint=cpm-bee-230916092609TMPI
api.aws.host=http://saas-1222385307.us-west-2.elb.amazonaws.com

essayScoring.url=http://127.0.0.1:8002

spring.profiles.active=tfh

grammarly.api.url=https://api.grammarly.com
grammarly.api.key=<your_api_key>

api.tencentcloud.ecc.secretId=AKIDKXWHo23pNhnfJitpWzDHCvc7OQEoAJG1
api.tencentcloud.ecc.secretKey=c0FJ9l9RWN2YrwmOluoUwA7yd6LL7eZu
api.tencentcloud.ecc.endpoint=ecc.tencentcloudapi.com

api.huashi.key=gso98MCdf6DhUsGKb39RNbAJeJt2+dRPoXqhixWn+gpzwzNmMMEgMcIUkeG9XlmcC+cLGQuDc4J9VWP7f0g/cUsLz5yDnBAWyg6jDN+X6lM+ICiuW3SKSCat5DoD/LW65EkqK17hrA==
api.huashi.url=https://api.xhpolaris.com/openapi/call/

api.aliyun.accessKeyId=LTAI5tFqxHUvkZ8jKuLyNqD1
api.aliyun.accessKeySecret=******************************

ocr.enableOcr=false
ocr.enableOcrForIdentifyOrStudentNumber=true
ocr.enableOcrForCorrect=false
ocr.enableOcrForEssay=true
ocr.enableOcrForScore=true
ocr.enableOcrForEXTRAQS=true
ocr.defaultCorrectService=aliOcrService

api.doubao.url=https://ark.cn-beijing.volces.com
api.doubao.token=ce5ff0da-49db-4dac-b54e-98c6922af6e8

correct.openai.logprobs=false

essay.secret=pDil8MDtPpGy323Xo9s
essay.apiUserId=2


mybatis-plus.global-config.db-config.logic-delete-field=deleted
mybatis-plus.global-config.db-config.logic-not-delete-value=0
mybatis-plus.global-config.db-config.logic-delete-value=1

postgres.datasource.url=**************************************************************************************
postgres.datasource.username=postgres
postgres.datasource.password=123456
postgres.datasource.driver-class-name=org.postgresql.Driver

volcengine.tos.region=cn-beijing
volcengine.tos.endpoint=https://tos-cn-beijing.volces.com
volcengine.tos.accessKey=AKLTZTIyNTg3ZjRiMTU0NGI1YWI4NjhkZmQ1Y2E1ZGIwMTE
volcengine.tos.secretKey=T0RWaE5XRmxOelEwTmpBeE5EYzVNemt4TW1VNE1UZzRZV1k1TkdJMk16Yw==
volcengine.tos.bucket=embedding

vikingdb.host=api-vikingdb.volces.com
vikingdb.region=cn-beijing
vikingdb.ak=AKLTZTIyNTg3ZjRiMTU0NGI1YWI4NjhkZmQ1Y2E1ZGIwMTE
vikingdb.sk=T0RWaE5XRmxOelEwTmpBeE5EYzVNemt4TW1VNE1UZzRZV1k1TkdJMk16Yw==
vikingdb.collection=firstPage
vikingdb.indexName=embedding
vikingdb.score=0.8