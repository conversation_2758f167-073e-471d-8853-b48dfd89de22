package com.chaty.service.impl;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import cn.hutool.json.JSONUtil;
import com.chaty.common.BaseResponse;
import org.apache.pdfbox.cos.COSStream;
import org.apache.pdfbox.multipdf.LayerUtility;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.PDPageTree;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.graphics.form.PDFormXObject;
import org.apache.pdfbox.util.Matrix;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import com.chaty.exception.BaseException;
import com.chaty.service.FileService;
import com.chaty.util.PDFUtil;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;

import static com.chaty.util.FileUtil.convertFileToInputStream;
import static com.chaty.util.MultiImageToPDF.generatePdfFromImages;

@Slf4j
@Service
public class LocalFileServiceImpl implements FileService {

    @Value("${file.local.path}")
    public String path;
    @Value("${file.local.ctxpath}")
    public String ctxPath;

    @Override
    public Map<String, Object> saveFile(MultipartFile file) {
        String filename = save2Local(file, true);
        return Collections.singletonMap("url", String.format("%s/%s", ctxPath, filename));
    }

    @Override
    public String saveFile(byte[] fileData, String filename) {
        FileUtil.writeBytes(fileData, localFilePath(filename));
        return String.format("%s/%s", ctxPath, filename);
    }


    /**
     * 保存文件到本地
     *
     * @param file             上传的 MultipartFile
     * @param needJudgeMaxSize 是否需要判断最大尺寸
     * @return 存储后的文件名
     */
    public String save2Local(MultipartFile file, Boolean needJudgeMaxSize) {
        String ext = FileUtil.extName(file.getOriginalFilename());
        String name = IdUtil.fastSimpleUUID() + "." + ext;
        File destFile = new File(localFilePath(name));

        try {
            if ("pdf".equalsIgnoreCase(ext) && Boolean.TRUE.equals(needJudgeMaxSize)) {
                try (PDDocument srcDoc = PDDocument.load(file.getInputStream())) {
                    // 1. 取第一页物理宽、高（pt）
                    PDPage first = srcDoc.getPage(0);
                    PDRectangle mb = first.getMediaBox();
                    float srcW = mb.getWidth(), srcH = mb.getHeight();
                    float srcLong  = Math.max(srcW, srcH);
                    float srcShort = Math.min(srcW, srcH);

                    // 2. 定义 A3 规格（297×420 mm → pt）及 1.2 倍阈值
                    final float MM_TO_PT = 72f / 25.4f;
                    PDRectangle a3 = new PDRectangle(
                            297f * MM_TO_PT,
                            420f * MM_TO_PT
                    );
                    float a3Long      = Math.max(a3.getWidth(),  a3.getHeight());
                    float a3Short     = Math.min(a3.getWidth(),  a3.getHeight());
                    float thresholdLong  = a3Long  * 1.2f;
                    float thresholdShort = a3Short * 1.2f;

                    // 3. 当页面长短边同时超过阈值时，等比例缩放到 A3
                    if (srcLong > thresholdLong && srcShort > thresholdShort) {
                        scalePdfToSize(srcDoc, destFile, a3);
                    } else {
                        srcDoc.save(destFile);
                    }
                }
            } else {
                // 非 PDF 或无需判断：直接保存
                FileUtil.writeFromStream(file.getInputStream(), destFile);
            }
            return name;

        } catch (IORuntimeException | IOException e) {
            throw new BaseException("文件保存失败", e);
        }
    }

    /**
     * 将 srcDoc 每页等比例缩放到 targetA1Size 并保持原页方向，内容自动居中
     */
    private void scalePdfToSize(PDDocument srcDoc, File destFile, PDRectangle a1Size) throws IOException {
        try (PDDocument dstDoc = new PDDocument()) {
            LayerUtility layerUtil = new LayerUtility(dstDoc);

            int pageCount = srcDoc.getNumberOfPages();
            for (int i = 0; i < pageCount; i++) {
                PDPage srcPage = srcDoc.getPage(i);
                PDRectangle srcBox = srcPage.getMediaBox();
                float srcW = srcBox.getWidth();
                float srcH = srcBox.getHeight();

                // 判断页是横向还是纵向
                boolean isLandscape = srcW > srcH;

                // 构造目标页面，保持 A1 纵向或横向
                PDRectangle targetBox = isLandscape
                        ? new PDRectangle(a1Size.getHeight(), a1Size.getWidth())
                        : a1Size;
                float tgtW = targetBox.getWidth();
                float tgtH = targetBox.getHeight();

                // 等比例缩放因子
                float scale = Math.min(tgtW / srcW, tgtH / srcH);

                // 导入整页为 FormXObject
                PDFormXObject form = layerUtil.importPageAsForm(srcDoc, i);

                // 新建一张目标大小的空白页
                PDPage newPage = new PDPage(targetBox);
                dstDoc.addPage(newPage);

                // 在内容流中：先平移(dx, dy)→再缩放(scale)→再绘制
                try (PDPageContentStream cs = new PDPageContentStream(dstDoc, newPage)) {
                    // 计算绘制后居中所需的偏移
                    float dx = (tgtW - srcW * scale) / 2f;
                    float dy = (tgtH - srcH * scale) / 2f;

                    cs.transform(Matrix.getTranslateInstance(dx, dy));
                    cs.transform(Matrix.getScaleInstance(scale, scale));
                    cs.drawForm(form);
                }
            }

            dstDoc.save(destFile);
        }
    }


    public String localFilePath(String filename) {
        return String.format("%s/%s", path, filename);
    }

    /**
     * 保存远程文件
     */
    @Override
    public Map<String, Object> saveRemoteFile(String url, String type) {
        log.info("保存远程文件 url:{} type:{}", url, type);
        String extName = FileUtil.extName(URLUtil.url(url).getPath());
        String filename = String.format("%s.%s", IdUtil.fastSimpleUUID(), extName);
        String localFilePath = localFilePath(filename);
        // 下载文件到本地
        HttpUtil.downloadFile(url, localFilePath);
        // 
        return Collections.singletonMap("url", String.format("%s/%s", ctxPath, filename));
    }

    @Override
    public Map<String, Object> doc2img(MultipartFile file, String filePath, boolean checkPageNum) {
        String filename = null;
        if (Objects.nonNull(file)) {
            filename = save2Local(file, true);
        } else {
            filename = com.chaty.util.FileUtil.INSTANCE.ctxPath2Filename(filePath);
        }
        Objects.requireNonNull(filename, "文件解析失败");
        String docPath = localFilePath(filename);
        if (checkPageNum) {
            int pageNum = PDFUtil.getPageNum(docPath);
            if (pageNum >= 1) {
                return MapUtil.builder(new HashMap<String, Object>())
                            .put("code", 2)
                            .put("pageNum", pageNum)
                            .put("filename", filename)
                            .build();
            }
        }
        String imgName = String.format("%s.%s", FileUtil.mainName(filename), "jpg");
        PDFUtil.convert2Img(docPath, String.format("%s/%s", path, imgName));
        return MapUtil.builder(new HashMap<String, Object>())
                .put("code", 1)
                .put("docpath", String.format("%s/%s", ctxPath, filename))
                .put("url", String.format("%s/%s", ctxPath, imgName))
                .build();
    }

    @Override
    public List<List<Map<String, Object>>> uploadMulitePdf(MultipartFile file, JSONObject config) {
        try {
            return multiPdfParse(file.getInputStream(), config);
        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            throw new BaseException("PDF文档上传失败", e);
        }
    }

    @Override
    public List<List<Map<String, Object>>> uploadInputStreamPdf(InputStream inputStream, JSONObject config) {
        try {
            return multiPdfParse(inputStream, config);
        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            throw new BaseException("PDF文档上传失败", e);
        }
    }

    @Override
    public List<List<Map<String, Object>>> uploadMulitePdf(JSONObject configObj) {
        String file = configObj.getStr("file");
        if (!StringUtils.hasText(file)) {
            throw new BaseException("文件路径不能为空");
        }
        return multiPdfParse(FileUtil.getInputStream(com.chaty.util.FileUtil.INSTANCE.ctxUrl2Path(file)), configObj);
    }

    /**
     * 解析多个PDF文件
     */
    public List<List<Map<String, Object>>> multiPdfParse(InputStream in, JSONObject config) {
        int pageNum = Optional.ofNullable(config)
                .map(c -> c.getInt("pageNum", 1)).orElse(1);
        JSONArray pagesConfig = Optional.ofNullable(config)
                .map(c -> c.getJSONArray("pages")).orElse(new JSONArray());
        List<List<Map<String, Object>>> res = new ArrayList<>();
        try {
            PDDocument document = PDDocument.load(in);
            PDPageTree pages = document.getPages();
            for (int i = 0; i < pages.getCount(); i++) {
                int curPage = i % pageNum;
                JSONObject pageConf = pagesConfig.getJSONObject(curPage);
                boolean isParse = Optional.ofNullable(pageConf).map(c -> c.getBool("isParse", true)).orElse(true);
                boolean isRotation = Optional.ofNullable(pageConf).map(c -> c.getBool("isRotation", false))
                        .orElse(false);

                List<Map<String, Object>> pageRes;
                if (res.size() <= curPage) {
                    pageRes = new ArrayList<>();
                    res.add(pageRes);
                } else {
                    pageRes = res.get(curPage);
                }

                if (!isParse) {
                    continue;
                }

                // 创建新的PDF文档
                PDDocument newDocument = new PDDocument();
                PDPage page = pages.get(i);
                if (isRotation) {
                    page.setRotation(180);
                }
                newDocument.addPage(page);

                // 保存新的PDF文件
                String filename = IdUtil.fastSimpleUUID() + ".pdf";
                newDocument.save(localFilePath(filename));
                newDocument.close();

                pageRes.add(Collections.singletonMap("url", String.format("%s/%s", ctxPath, filename)));
            }
            document.close();
            return res;   
        } catch (Exception e) {
            throw new BaseException("PDF文档解析失败", e);
        } finally {
            IoUtil.close(in);
        }
    }

    @Override
    public Map<String, Object> pdf2Img(String filename, Integer pageNum) {
        String docPath = localFilePath(filename);
        String target = IdUtil.fastSimpleUUID() + ".jpg";
        JSONObject imageInfo = PDFUtil.convert2ImgWithImgInfo(docPath, String.format("%s/%s", path, target), pageNum - 1);
        return MapUtil.builder(new HashMap<String, Object>())
                .put("code", 1)
                .put("docpath", String.format("%s/%s", ctxPath, target.replace(".jpg", ".pdf")))
                .put("url", String.format("%s/%s", ctxPath, target))
                .put("imageInfo", imageInfo)
                .build();
    }

    @Override
    public String img2Pdf(MultipartFile file) throws IOException {
        List<byte[]> imageBytesList = new ArrayList<>();
        imageBytesList.add(file.getBytes());
        String fileName = generatePdfFromImages(imageBytesList, path);
        String outPdfPath = ctxPath + "/" + fileName;
        return outPdfPath;
    }

    @Override
    public List<List<Map<String, Object>>> imgs2Pdf(List<MultipartFile> files, String config) throws IOException {
        JSONObject configObj = null;
        if (Objects.nonNull(config)) {
            configObj = JSONUtil.parseObj(config);
        }

        List<byte[]> imageBytesList = new ArrayList<>();

        try {
            // 处理 MultipartFile 图片
            if (Objects.nonNull(files) && !files.isEmpty()) {
                for (MultipartFile file : files) {
                    imageBytesList.add(file.getBytes());
                }
            }
            // 处理 Base64 图片
            else if (Objects.nonNull(configObj)) {
                List<String> base64Images = configObj.getBeanList("base64Images", String.class);
                for (String base64Image : base64Images) {
                    imageBytesList.add(Base64.getDecoder().decode(base64Image));
                }
            } else {
                throw new IllegalArgumentException("图片文件或 Base64 不能为空");
            }

            // 生成 PDF
            String pdfName = generatePdfFromImages(imageBytesList, path);
            String outputPdfPath = path + File.separator + pdfName;
            List<List<Map<String, Object>>> uploadRes = uploadInputStreamPdf(convertFileToInputStream(outputPdfPath), configObj);
            return uploadRes;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
