package com.chaty.dto;

import java.math.BigDecimal;
import java.util.Objects;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.Data;

@Data
public class CorrectAreaQsDTO {

    /**
     * 试题批改结果 Y-正确
     */
    private String isCorrect;
    /**
     * 学生答案
     */
    private String studentAnswer;
    /**
     * 批改评价
     */
    private String review;
    /**
     * 分数
     */
    private String scored;
    /**
     * 是否修改
     */
    private Integer hasChange;

    /**
     * 是否是手写数字识别 2的话就是
     */
    private Integer isScorePoint;

    /**
     * 原始分数
     */
    private String scoredOriginal;

    /**
     * 原始批改结果
     */
    private String isCorrectOriginal;

    /**
     * 原始JSON
     */
    private JSONObject rawObj;

    public Boolean hasChangeVal() {
        return Objects.equals(hasChange, 1);
    }

    public Boolean getCorrect() {
        return "Y".equals(isCorrect);
    }

    public Boolean getCorrectOriginal() {
        return "Y".equals(isCorrectOriginal);
    }

    public Boolean haveCorrectOriginal() {
        return Objects.nonNull(isCorrectOriginal) && StrUtil.isNotBlank(isCorrectOriginal);
    }

    public BigDecimal getScoredVal() {
        return NumberUtil.toBigDecimal(scored);
    }

    public BigDecimal getScoredOriginalVal() {
        return NumberUtil.toBigDecimal(scoredOriginal);
    }

    /**
     * 修改批改结果-手写分数题目
     */
    public void changeScorePointCorrectVal(BigDecimal newScore) {
        if (Objects.isNull(this.isCorrectOriginal)) {
            // 首次纠错 要保存原始答案和分数，方便判断hasChange
            this.isCorrectOriginal = this.isCorrect;
            this.scoredOriginal = this.scored;
        }
        this.hasChange = this.getScoredOriginalVal().compareTo(newScore) == 0 ? 0 : 1;
        this.scored = String.valueOf(newScore);
    }

    /**
     * 修改批改结果-普通题目
     */
    public void changeNormalCorrectVal() {
        if (Objects.isNull(this.isCorrectOriginal)) {
            // 首次纠错 要保存原始答案和分数，方便判断hasChange
            this.isCorrectOriginal = this.isCorrect;
        }
        this.isCorrect = this.isCorrect.equals("Y") ? "N" : "Y";
        this.hasChange = this.isCorrect.equals(this.isCorrectOriginal) ? 0 : 1;
    }

    /**
     * 合并返回JSON结果
     */
    public JSONObject toJSONObj() {
        // 合并当前对象和原始JSON
        JSONObject obj = JSONUtil.parseObj(this);
        obj.remove("rawObj");
        rawObj.putAll(obj);
        return rawObj;
    }

    /**
     * 创建对象
     */
    public static CorrectAreaQsDTO create(JSONObject obj) {
        CorrectAreaQsDTO dto = JSONUtil.toBean(obj, CorrectAreaQsDTO.class);
        dto.setRawObj(obj);
        return dto;
    }

}
