package com.chaty.service.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.dto.AdminRoleDTO;
import com.chaty.entity.admin.AdminRole;

public interface AdminRoleService extends IService<AdminRole> {

    IPage<?> getPage(AdminRoleDTO params);

    void add(AdminRoleDTO params);

    void update(AdminRoleDTO params);

    void delete(String id);
    
}
