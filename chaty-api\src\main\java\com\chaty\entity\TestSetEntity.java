package com.chaty.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/** 测试集实体（无 Redis 参与） */
@Data
@TableName("test_set")
public class TestSetEntity {

    /** 主键，自增 */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 测试集名称 */
    private String name;

    /** 描述 */
    private String description;

    /** 包含题型（英文逗号分隔字符串） */
    @TableField("question_types")
    private String questionTypes;

    /** 创建时间，插入时填充（若项目已有通用填充器） */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新时间，插入和更新时填充（若项目已有通用填充器） */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
