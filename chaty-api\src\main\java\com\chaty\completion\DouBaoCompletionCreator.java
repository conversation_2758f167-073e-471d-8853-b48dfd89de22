package com.chaty.completion;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.chaty.dto.ChatCompletionDTO;
import com.chaty.dto.DocCorrectRecordDTO;
import com.chaty.dto.MessageDTO;
import com.chaty.dto.PaperTopicDTO;
import com.chaty.entity.ModelSetting;
import com.chaty.enums.AIModelConsts;
import com.chaty.form.ExtraQsForm;
import com.chaty.mapper.ReviewMapper;
import com.chaty.service.PromptsRedisService;
import com.chaty.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.chaty.enums.AIModelMaxTokenConsts.getDoubaoMaxToken;

@Slf4j
@Service
public class DouBaoCompletionCreator implements CompletionCreator {

    private final ReviewMapper reviewMapper;
    @Value("${server.url}")
    public String serverUrl;
    @Resource
    private PromptsRedisService promptsRedisService;

    private static String defaultPromptPrefix = "你最终需要返回json字符串，请严格按照以下JSON格式返回json字符串（注意：必须要返回相应题目数量的结果，不能多也不能少；所有key必须严格使用'题目X'的格式，X为题目编号，例如'题目1'，不能有其他key。）：\n";

    public DouBaoCompletionCreator(ReviewMapper reviewMapper) {
        this.reviewMapper = reviewMapper;
    }

    @Override
    public ChatCompletionDTO createDocAreaCompletion(DocCorrectRecordDTO record, JSONObject areaObj,
                                                     JSONObject areaRes) {
        return createDocAreaResponseFormatCompletion(record, areaObj, areaRes, "");
    }

    @Override
    public boolean isSupported(String aimodel, Boolean jsonSchema) {
        if (Boolean.TRUE.equals(jsonSchema)) {
            return false;
        }
        return AIModelConsts.douBaoJSONModels.contains(aimodel);
    }

    @Override
    public ChatCompletionDTO createRxtraQsCompletion(ExtraQsForm params) {
        String aimodel = params.getAimodel();

        ChatCompletionDTO completion = new ChatCompletionDTO();
        completion.setModel(aimodel);
        completion.setMaxTokens(getDoubaoMaxToken(aimodel));

        List<MessageDTO> messages = new ArrayList<MessageDTO>();
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setRole("user");
        JSONArray contents = new JSONArray();
        // 图片信息
        JSONObject imageContent = new JSONObject();
        imageContent.set("type", "image_url");
        imageContent.set("image_url", JSONUtil.createObj().set("url", "data:image/jpeg;base64," + params.getImgStr()).set("detail", "high"));
        contents.put(imageContent);

        JSONObject promptContent = new JSONObject();
        promptContent.set("type", "text");
        String sysMsg = promptsRedisService.getExtraQsRespformatTemplete();
        promptContent.set("text", sysMsg);
        contents.put(promptContent);


        JSONObject qsContent = new JSONObject();
        qsContent.set("type", "text");
        String qsText = "请按照下面格式输出JSON，每一个问题是一个obj，最后形成一个JsonArray" + promptsRedisService.getExtraQsRespformatDoubaoSchema();
        qsContent.set("text", qsText);
        contents.put(qsContent);



        messageDTO.setContent(contents);
        messages.add(messageDTO);
        completion.setMessages(messages);
        return completion;
    }

    @Override
    public ChatCompletionDTO createDocAreaResponseFormatCompletion(DocCorrectRecordDTO record,
                                                                   JSONObject areaObj,
                                                                   JSONObject areaRes,
                                                                   String ocrContent) {
        String aimodel = record.getAimodel();
        ChatCompletionDTO completion = new ChatCompletionDTO();
        completion.setModel(aimodel);
        completion.setMaxTokens(getDoubaoMaxToken(aimodel));

        List<MessageDTO> messages = new ArrayList<MessageDTO>();
        JSONArray contents = new JSONArray();
        // 系统提示词
        if ("system".equals(record.getModelSetting().getSingleRoundPromptType()) || "mix".equals(record.getModelSetting().getSingleRoundPromptType())) {
            MessageDTO messageDTO = new MessageDTO();
            messageDTO.setRole("system");
            String promptType = null;
            if ("system".equals(record.getModelSetting().getSingleRoundPromptType())) {
                promptType = "system";
            } else if("mix".equals(record.getModelSetting().getSingleRoundPromptType())) {
                promptType = "mix-system";
            }
            // 根据提示词类型获取
            String sysMsg = promptsRedisService.getPromptByMeta(
                    record.getModelRequestId(),
                    "大题类型",
                    (String) areaObj.getOrDefault("commonQuestionType", "通用"),
                    "整体一轮",
                    promptType,
                    null);
            messageDTO.setContent(sysMsg);
            messages.add(messageDTO);
        }
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setRole("user");
        // 图片信息
        JSONObject imageContent = new JSONObject();
        imageContent.set("type", "image_url");
        String areaImg = areaObj.getStr("areaImg");
        areaImg = getProcessedImageBase64(record, areaObj, areaImg);
        imageContent.set("image_url", JSONUtil.createObj().set("url", areaImg).set("detail", "high"));
        contents.put(imageContent);
        // user提示词
        if("user".equals(record.getModelSetting().getSingleRoundPromptType()) || "mix".equals(record.getModelSetting().getSingleRoundPromptType())) {
            String promptType = null;
            if ("user".equals(record.getModelSetting().getSingleRoundPromptType())) {
                promptType = "user";
            } else {
                promptType = "mix-user";
            }
            String sysMsg = promptsRedisService.getPromptByMeta(
                    record.getModelRequestId(),
                    "大题类型",
                    (String) areaObj.getOrDefault("commonQuestionType", "通用"),
                    "整体一轮",
                    promptType,
                    null);
            JSONObject promptContent = new JSONObject();
            promptContent.set("type", "text");
            promptContent.set("text", sysMsg);
            contents.put(promptContent);
        }

        // 题目信息
        StringBuilder content = new StringBuilder();
        content.append("以下是我们指定的答案和题目信息：\n");
        JSONArray questions = areaObj.getJSONArray("questions");
        for (int index = 0; index < questions.size(); index++) {
            JSONObject question = (JSONObject) questions.get(index);
            content.append(String.format("</%s>", "题目" + (index + 1)));
            content.append(String.format(promptsRedisService.getDocCorrectResponseFormatTemplate(record.getModelRequestId()),
//                    index + 1,
                    question.getStr("question"),
                    question.getStr("qsInfo", ""),
                    question.getStr("answer"),
                    question.getStr("score", "未定义")
//                    question.getStr("scorePoints", "未定义")
            ));
            content.append(String.format("</%s>", "题目" + (index + 1)));
            content.append("\n");
        }
        JSONObject questionInfoContent = new JSONObject();
        questionInfoContent.set("type", "text");
        questionInfoContent.set("text", content.toString());
        contents.put(questionInfoContent);
        // 结构化输出
        JSONObject responseFormatContent = new JSONObject();
        responseFormatContent.set("type", "text");
        JSONObject step5SchemaProps = new JSONObject();
        JSONObject schemaPropObj = getDefaultRequireJSONFormat();
        for (int i = 0; i < questions.size(); i++) {
            String propName = "题目" + (i + 1);
            step5SchemaProps.set(propName, schemaPropObj);
        }
        responseFormatContent.set("text", defaultPromptPrefix + JSONUtil.toJsonStr(step5SchemaProps));
        contents.put(responseFormatContent);

        // 完成
        messageDTO.setContent(contents);
        messages.add(messageDTO);
        completion.setMessages(messages);

        // 设置 responseFormat
        if (Boolean.TRUE.equals(record.getJsonobject())) {
            JSONObject responseFormat = new JSONObject();
            responseFormat.set("type", "json_object");
            completion.setResponseFormat(responseFormat);
        }

        return completion;
    }

    @Override
    public ChatCompletionDTO createDocAreaNormalQsFirstRequest(DocCorrectRecordDTO record, JSONObject areaObj, JSONObject areaRes) {
        String aimodel = record.getAimodel();
        ChatCompletionDTO completion = new ChatCompletionDTO();
        completion.setModel(aimodel);
        completion.setMaxTokens(getDoubaoMaxToken(aimodel));

        List<MessageDTO> messages = new ArrayList<MessageDTO>();
        JSONArray contents = new JSONArray();
        if ("system".equals(record.getModelSetting().getFirstRoundPromptType()) || "mix".equals(record.getModelSetting().getFirstRoundPromptType())) {
            MessageDTO messageDTO = new MessageDTO();
            messageDTO.setRole("system");
            String promptType = null;
            if ("system".equals(record.getModelSetting().getFirstRoundPromptType())) {
                promptType = "system";
            } else if("mix".equals(record.getModelSetting().getFirstRoundPromptType())) {
                promptType = "mix-system";
            }
            // 根据提示词类型获取
            String sysMsg = promptsRedisService.getPromptByMeta(
                    record.getModelRequestId(),
                    "大题类型",
                    (String) areaObj.getOrDefault("commonQuestionType", "通用"),
                    "第一轮",
                    promptType,
                    null);
            messageDTO.setContent(sysMsg);
            messages.add(messageDTO);
        }

        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setRole("user");
        // 图片信息
        JSONObject imageContent = new JSONObject();
        imageContent.set("type", "image_url");
        String areaImg = areaObj.getStr("areaImg");
        areaImg = getProcessedImageBase64(record, areaObj, areaImg);
        imageContent.set("image_url", JSONUtil.createObj().set("url", areaImg).set("detail", "high"));
        contents.put(imageContent);
        // 系统提示词
        if("user".equals(record.getModelSetting().getFirstRoundPromptType()) || "mix".equals(record.getModelSetting().getFirstRoundPromptType())) {
            String promptType = null;
            if ("user".equals(record.getModelSetting().getFirstRoundPromptType())) {
                promptType = "user";
            } else {
                promptType = "mix-user";
            }
            String sysMsg = promptsRedisService.getPromptByMeta(
                    record.getModelRequestId(),
                    "大题类型",
                    (String) areaObj.getOrDefault("commonQuestionType", "通用"),
                    "第一轮",
                    promptType,
                    null);
            JSONObject promptContent = new JSONObject();
            promptContent.set("type", "text");
            promptContent.set("text", sysMsg);
            contents.put(promptContent);
        }

        // 结构化输出
        JSONObject responseFormatContent = new JSONObject();
        responseFormatContent.set("type", "text");
        JSONObject step5SchemaProps = new JSONObject();
        JSONArray questions = areaObj.getJSONArray("questions");
        for (int i = 0; i < questions.size(); i++) {
            String propName = "题目" + (i + 1);
            step5SchemaProps.set(propName, "学生答案");
        }
        responseFormatContent.set("text", defaultPromptPrefix + JSONUtil.toJsonStr(step5SchemaProps));
        contents.put(responseFormatContent);

        // 完成
        messageDTO.setContent(contents);
        messages.add(messageDTO);
        // 填充全部
        completion.setMessages(messages);
        // 设置 responseFormat
        if (Boolean.TRUE.equals(record.getJsonobject())) {
            JSONObject responseFormat = new JSONObject();
            responseFormat.set("type", "json_object");
            completion.setResponseFormat(responseFormat);
        }

        return completion;
    }

    @Override
    public ChatCompletionDTO createDocAreaNormalQsSecondRequest(DocCorrectRecordDTO record, JSONObject areaObj, JSONObject areaRes, JSONArray resolvedRes) {
        String aimodel = record.getAimodel();
        ChatCompletionDTO completion = new ChatCompletionDTO();
        completion.setModel(aimodel);
        completion.setMaxTokens(getDoubaoMaxToken(aimodel));

        // 系统提示词
        List<MessageDTO> messages = new ArrayList<MessageDTO>();
        JSONArray contents = new JSONArray();

        if ("system".equals(record.getModelSetting().getSecondRoundPromptType()) || "mix".equals(record.getModelSetting().getSecondRoundPromptType())) {
            MessageDTO messageDTO = new MessageDTO();
            messageDTO.setRole("system");
            String promptType = null;
            if ("system".equals(record.getModelSetting().getSecondRoundPromptType())) {
                promptType = "system";
            } else if("mix".equals(record.getModelSetting().getSecondRoundPromptType())) {
                promptType = "mix-system";
            }
            // 根据提示词类型获取
            String sysMsg = promptsRedisService.getPromptByMeta(
                    record.getModelRequestId(),
                    "大题类型",
                    (String) areaObj.getOrDefault("commonQuestionType", "通用"),
                    "第二轮",
                    promptType,
                    record.getModelSetting().getIsSecondRoundUseImage());
            messageDTO.setContent(sysMsg);
            messages.add(messageDTO);
        }

        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setRole("user");
        // 根据 isSecondRoundUseImage 参数决定是否添加图片
        ModelSetting modelSetting = record.getModelSetting();
        boolean useImageInSecondRound = modelSetting != null && Boolean.TRUE.equals(modelSetting.getIsSecondRoundUseImage());
        if (useImageInSecondRound) {
            // 图片信息
            JSONObject imageContent = new JSONObject();
            imageContent.set("type", "image_url");
            String areaImg = areaObj.getStr("areaImg");
            areaImg = getProcessedImageBase64(record, areaObj, areaImg);
            imageContent.set("image_url", JSONUtil.createObj().set("url", areaImg).set("detail", "high"));
            contents.put(imageContent);
        }

        // 系统提示词
        if("user".equals(record.getModelSetting().getSecondRoundPromptType()) || "mix".equals(record.getModelSetting().getSecondRoundPromptType())) {
            String promptType = null;
            if ("user".equals(record.getModelSetting().getSecondRoundPromptType())) {
                promptType = "user";
            } else {
                promptType = "mix-user";
            }
            String sysMsg = promptsRedisService.getPromptByMeta(
                    record.getModelRequestId(),
                    "大题类型",
                    (String) areaObj.getOrDefault("commonQuestionType", "通用"),
                    "第二轮",
                    promptType,
                    record.getModelSetting().getIsSecondRoundUseImage());
            JSONObject promptContent = new JSONObject();
            promptContent.set("type", "text");
            promptContent.set("text", sysMsg);
            contents.put(promptContent);
        }

        // 题目信息
        StringBuilder content = new StringBuilder();
        content.append("以下是学生答案：\n");
        for (int index = 0; index < resolvedRes.size(); index++) {
            JSONObject studentAnswer = (JSONObject) resolvedRes.get(index);
            content.append(
                    String.format("</%s> \n %s", "学生答案" + (index + 1), studentAnswer.getStr("studentAnswer"))
            );
            content.append(String.format("</%s>", "学生答案" + (index + 1)));
            content.append("\n");
        }

        content.append("以下是我们指定的答案和题目信息：\n");
        JSONArray questions = areaObj.getJSONArray("questions");
        for (int index = 0; index < questions.size(); index++) {
            JSONObject question = (JSONObject) questions.get(index);
            content.append(String.format("</%s>", "题目" + (index + 1)));
            content.append(String.format(promptsRedisService.getDocCorrectResponseFormatTemplate(record.getModelRequestId()),
//                    index + 1,
                    question.getStr("question"),
                    question.getStr("qsInfo", ""),
                    question.getStr("answer"),
                    question.getStr("score", "未定义")
//                    question.getStr("scorePoints", "未定义")
            ));
            content.append(String.format("</%s>", "题目" + (index + 1)));
            content.append("\n");
        }
        JSONObject questionInfoContent = new JSONObject();
        questionInfoContent.set("type", "text");
        questionInfoContent.set("text", content.toString());
        contents.add(questionInfoContent);
        // 结构化输出
        JSONObject responseFormatContent = new JSONObject();
        responseFormatContent.set("type", "text");
        JSONObject step5SchemaProps = new JSONObject();
        JSONObject schemaPropObj = getDefaultRequireJSONFormat();
        for (int i = 0; i < questions.size(); i++) {
            String propName = "题目" + (i + 1);
            step5SchemaProps.set(propName, schemaPropObj);
        }
        responseFormatContent.set("text", defaultPromptPrefix + JSONUtil.toJsonStr(step5SchemaProps));
        contents.put(responseFormatContent);

        // 完成
        messageDTO.setContent(contents);
        messages.add(messageDTO);
        completion.setMessages(messages);

        // 设置 responseFormat
        if (Boolean.TRUE.equals(record.getJsonobject())) {
            JSONObject responseFormat = new JSONObject();
            responseFormat.set("type", "json_object");
            completion.setResponseFormat(responseFormat);
        }

        return completion;
    }

    private JSONObject getDefaultRequireJSONFormat() {
        return JSONUtil.parseObj(promptsRedisService.getDoubaoCommonQuestionReturnStructure());
    }

//    private JSONObject getNormalQsFirstDefaultRequireJSONFormat() {
//        return JSONUtil.parseObj(promptsRedisService.getNormalQsFirstDefaultRequireJSONFormat());
//    }

    @Override
    public ChatCompletionDTO createWriteQsCompletion1Request(DocCorrectRecordDTO record,
                                                             JSONObject areaObj,
                                                             JSONObject areaRes,
                                                             Integer allScore,
                                                             String ocrContent,
                                                             Integer modelRequestId) {
        String aimodel = record.getTask().getAimodel();
        JSONArray questions = areaObj.getJSONArray("questions");
        ChatCompletionDTO completion = new ChatCompletionDTO();
        completion.setModel(aimodel);
        completion.setMaxTokens(getDoubaoMaxToken(aimodel));
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setRole("user");
        JSONArray contents = new JSONArray();
        // 图片信息
        JSONObject imageContent = new JSONObject();
        imageContent.set("type", "image_url");
        String areaImg = areaObj.getStr("areaImg");
        areaImg = getProcessedImageBase64(record, areaObj, areaImg);
        imageContent.set("image_url", JSONUtil.createObj().set("url", areaImg).set("detail", "high"));
        contents.add(imageContent);
        // 系统提示词
        List<MessageDTO> messages = new ArrayList<MessageDTO>();
        String sysMsg = null;
        if (StrUtil.isNotBlank(ocrContent)) {
            sysMsg = String.format(promptsRedisService.getDocCorrectWriteQsSystemPrompt(modelRequestId) + "\n ocr的识别结果：%s", allScore, ocrContent);
        } else {
            sysMsg = String.format(promptsRedisService.getDocCorrectWriteQsSystemPrompt(modelRequestId), allScore);
        }
        if (Objects.nonNull(record.getConfig())) {
            sysMsg = record.getConfig().getConfigObj().getStr("prompt", sysMsg);
        }
        JSONObject promptContent = new JSONObject();
        promptContent.set("type", "text");
        promptContent.set("text", sysMsg);
        contents.add(promptContent);
        // 结构化输出
        JSONObject responseFormatContent = new JSONObject();
        responseFormatContent.set("type", "text");
        responseFormatContent.set("text", defaultPromptPrefix + promptsRedisService.getDocCorrectWriteQsResponseDoubaoFormat(modelRequestId));
        contents.add(responseFormatContent);

        // 完成
        messageDTO.setContent(contents);
        messages.add(messageDTO);
        completion.setMessages(messages);
        return completion;
    }

    @Override
    public ChatCompletionDTO createWriteQsCompletion2Request(DocCorrectRecordDTO record,
                                                             JSONObject areaObj,
                                                             String essay,
                                                             String gradeName,
                                                             String title,
                                                             String apiCorrectResult,
                                                             Integer allScore,
                                                             Boolean isEnglishEssay,
                                                             Integer modelRequestId) {
        String aimodel = record.getTask().getAimodel();
        JSONArray questions = areaObj.getJSONArray("questions");
        ChatCompletionDTO completion = new ChatCompletionDTO();
        completion.setModel(aimodel);
        completion.setMaxTokens(getDoubaoMaxToken(aimodel));
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setRole("user");
        JSONArray contents = new JSONArray();
        // 图片信息
        JSONObject imageContent = new JSONObject();
        imageContent.set("type", "image_url");
        String areaImg = areaObj.getStr("areaImg");
        areaImg = getProcessedImageBase64(record, areaObj, areaImg);
        imageContent.set("image_url", JSONUtil.createObj().set("url", areaImg).set("detail", "high"));
        contents.put(imageContent);
        // 系统提示词
        List<MessageDTO> messages = new ArrayList<MessageDTO>();
        String sysMsg = String.format(promptsRedisService.getDocCorrectWriteQsSystemPrompt2(modelRequestId), apiCorrectResult, isEnglishEssay ? "英语" : "汉语", gradeName, title, allScore, essay);
        if (Objects.nonNull(record.getConfig())) {
            sysMsg = record.getConfig().getConfigObj().getStr("prompt", sysMsg);
        }
        JSONObject promptContent = new JSONObject();
        promptContent.set("type", "text");
        promptContent.set("text", sysMsg);
        contents.put(promptContent);
        // 结构化输出
        JSONObject responseFormatContent = new JSONObject();
        responseFormatContent.set("type", "text");
        responseFormatContent.set("text", defaultPromptPrefix + promptsRedisService.getDocCorrectWriteQsResponseDoubaoFormat2(modelRequestId));
        contents.put(responseFormatContent);

        // 完成
        messageDTO.setContent(contents);
        messages.add(messageDTO);
        completion.setMessages(messages);
        return completion;
    }

    /**
     * 分数提取
     *
     * @param record
     * @param areaObj
     * @param areaRes
     * @param ocrContent
     * @return
     */
    @Override
    public ChatCompletionDTO createScoreCompletion(DocCorrectRecordDTO record,
                                                   JSONObject areaObj,
                                                   JSONObject areaRes,
                                                   String ocrContent) {
        String aimodel = record.getTask().getAimodel();
        ChatCompletionDTO completion = new ChatCompletionDTO();
        completion.setModel(aimodel);
        completion.setMaxTokens(getDoubaoMaxToken(aimodel));

        List<MessageDTO> messages = new ArrayList<MessageDTO>();
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setRole("user");
        // 提示词信息
        JSONObject promptContent = new JSONObject();
        JSONArray contents = new JSONArray();
        // 图片信息
        JSONObject imageContent = new JSONObject();
        imageContent.set("type", "image_url");
        String areaImg = areaObj.getStr("areaImg");
        String areaImgUrl = String.format("%s%s", serverUrl, areaImg);
        if (!Base64.isBase64(areaImgUrl)) {
            if (!areaImgUrl.startsWith("http")) {
                areaImgUrl = String.format("%s%s", serverUrl, areaImgUrl);
            }
            areaImgUrl = "data:image/jpeg;base64," + FileUtil.INSTANCE.url2Base64(areaImgUrl);
        }
        imageContent.set("image_url", JSONUtil.createObj().set("url", areaImgUrl).set("detail", "high"));
        contents.add(imageContent);

        promptContent.set("type", "text");
        String sysMsg = promptsRedisService.getDocCorrectScoreSystemPrompt(record.getModelRequestId());
        if (StrUtil.isNotBlank(ocrContent)) {
            sysMsg = String.format(sysMsg + "\n 请参考ocr的识别结果：%s", ocrContent);
        }
        promptContent.set("text", sysMsg);
        contents.put(promptContent);
        // 结构化输出
//        JSONObject responseFormatContent = JSONUtil.parseObj(promptsRedisService.getDocCorrectScoreResponseJSONPrompt());
//        contents.put(responseFormatContent);

        messageDTO.setContent(contents);
        messages.add(messageDTO);
        completion.setMessages(messages);
        return completion;
    }

    @Override
    public ChatCompletionDTO createEssayAnalyticalReportCompletion(List<DocCorrectRecordDTO> records, DocCorrectRecordDTO record, String scoreSituation, String gradeName) {
        String aimodel = record.getAimodel();
        ChatCompletionDTO completion = new ChatCompletionDTO();
        completion.setModel(aimodel);
        completion.setMaxTokens(getDoubaoMaxToken(aimodel));
        List<MessageDTO> messages = new ArrayList<MessageDTO>();
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setRole("user");
        JSONObject promptContent = new JSONObject();
        JSONArray contents = new JSONArray();
        promptContent.set("type", "text");
        String sysMsg = promptsRedisService.getDocCorrectEssayReportDoubaoPrompt();
        promptContent.set("text", String.format(sysMsg, gradeName));
        contents.put(promptContent);
        // 题目信息
        JSONObject qsContentObj = new JSONObject();
        qsContentObj.set("type", "text");
        String qsText = getQs(records);
        qsContentObj.set("text", "所有学生作文信息：\n" + qsText);
        contents.put(qsContentObj);

        messageDTO.setContent(contents);
        messages.add(messageDTO);
        completion.setMessages(messages);
        return completion;
    }

    private String getQs(List<DocCorrectRecordDTO> records) {
        JSONArray qsArray = new JSONArray();
        records.forEach(record -> {
            JSONObject qsObj = new JSONObject();
            JSONArray reviewed = JSONUtil.parseArray(record.getReviewed());
            qsObj = reviewed.getByPath("[0].reviewed[0].review", JSONObject.class);
            qsArray.put(qsObj);
        });
        return qsArray.toString();
    }

    @Override
    public ChatCompletionDTO createExtractStudentName(String aimodel, String areaImgUrl, String ocrContent, Integer modelRequestId) {
        ChatCompletionDTO completion = new ChatCompletionDTO();
        completion.setModel(aimodel);
        completion.setMaxTokens(getDoubaoMaxToken(aimodel));
        List<MessageDTO> messages = new ArrayList<MessageDTO>();
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setRole("user");
        JSONObject promptContent = new JSONObject();
        JSONArray contents = new JSONArray();
        // 图片信息
        JSONObject imageContent = new JSONObject();
        imageContent.set("type", "image_url");
        if (!Base64.isBase64(areaImgUrl)) {
            if (!areaImgUrl.startsWith("http")) {
                areaImgUrl = String.format("%s%s", serverUrl, areaImgUrl);
            }
            areaImgUrl = "data:image/jpeg;base64," + FileUtil.INSTANCE.url2Base64(areaImgUrl);
        }
        imageContent.set("image_url", JSONUtil.createObj().set("url", areaImgUrl).set("detail", "high"));
        contents.put(imageContent);
        // 提示词信息
        promptContent.set("type", "text");
        String sysMsg = promptsRedisService.getDocCorrectExtractStudentNamePrompt(modelRequestId);
        if (StrUtil.isNotBlank(ocrContent)) {
            sysMsg = String.format(sysMsg + "\n 请参考ocr的识别结果：%s", ocrContent);
        }
        promptContent.set("text", sysMsg);
        contents.put(promptContent);
        // 结构化输出
        JSONObject responseFormatContent = new JSONObject();
        responseFormatContent.set("type", "text");
        JSONObject responseJSON = new JSONObject();
        responseJSON.set("studentName", "请填写学生学号，识别不出请填写无");
        responseFormatContent.set("text", defaultPromptPrefix + JSONUtil.toJsonStr(responseJSON));
        contents.put(responseFormatContent);

        log.info("createExtractStudentName prompt: {}", String.format(sysMsg, ocrContent));
        messageDTO.setContent(contents);
        messages.add(messageDTO);
        completion.setMessages(messages);

        return completion;
    }

    public static class Data {
        public String type;
        public Map<String, Object> properties;
        public List<String> required;

        public Data(String type, Map<String, Object> properties, List<String> required) {
            this.type = type;
            this.properties = properties;
            this.required = required;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Map<String, Object> getProperties() {
            return properties;
        }

        public void setProperties(Map<String, Object> properties) {
            this.properties = properties;
        }

        public List<String> getRequired() {
            return required;
        }

        public void setRequired(List<String> required) {
            this.required = required;
        }
    }

    @Override
    public ChatCompletionDTO createExtractStudentNumber(String aimodel, String areaImgUrl, String ocrContent, Integer modelRequestId) {
        ChatCompletionDTO completion = new ChatCompletionDTO();
        completion.setModel(aimodel);
        completion.setMaxTokens(getDoubaoMaxToken(aimodel));
        List<MessageDTO> messages = new ArrayList<MessageDTO>();
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setRole("user");
        JSONObject promptContent = new JSONObject();
        JSONArray contents = new JSONArray();
        // 图片信息
        JSONObject imageContent = new JSONObject();
        imageContent.set("type", "image_url");
        if (!Base64.isBase64(areaImgUrl)) {
            if (!areaImgUrl.startsWith("http")) {
                areaImgUrl = String.format("%s%s", serverUrl, areaImgUrl);
            }
            areaImgUrl = "data:image/jpeg;base64," + FileUtil.INSTANCE.url2Base64(areaImgUrl);
        }
        imageContent.set("image_url", JSONUtil.createObj().set("url", areaImgUrl).set("detail", "high"));
        contents.put(imageContent);
        // 提示词信息
        promptContent.set("type", "text");
        String sysMsg = promptsRedisService.getDocCorrectExtractStudentNumberPrompt(modelRequestId);
        if (StrUtil.isNotBlank(ocrContent)) {
            sysMsg = String.format(sysMsg + "\n 请参考ocr的识别结果：%s", ocrContent);
        }
        promptContent.set("text", sysMsg);
        contents.put(promptContent);
        // 结构化输出
        JSONObject responseFormatContent = new JSONObject();
        responseFormatContent.set("type", "text");
        JSONObject responseJSON = new JSONObject();
        responseJSON.set("studentNumber", "请填写学生学号，识别不出请填写无");
        responseFormatContent.set("text", defaultPromptPrefix + JSONUtil.toJsonStr(responseJSON));
        contents.put(responseFormatContent);

        log.info("createExtractStudentNumber prompt: {}", String.format(sysMsg, ocrContent));
        messageDTO.setContent(contents);
        messages.add(messageDTO);
        completion.setMessages(messages);
        return completion;
    }

    @Override
    public ChatCompletionDTO createExtractPaperTopic(String aimodel, List<String> imgUrls, List<PaperTopicDTO> topics) {
        ChatCompletionDTO completion = new ChatCompletionDTO();
        completion.setModel(aimodel);
        completion.setMaxTokens(getDoubaoMaxToken(aimodel));
        List<MessageDTO> messages = new ArrayList<MessageDTO>();
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setRole("user");
        JSONArray contents = new JSONArray();
        // 图片信息
        for (String areaImgUrl : imgUrls) {
            JSONObject imageContent = new JSONObject();
            imageContent.set("type", "image_url");
            if (!Base64.isBase64(areaImgUrl)) {
                if (!areaImgUrl.startsWith("http")) {
                    areaImgUrl = String.format("%s%s", serverUrl, areaImgUrl);
                }
                areaImgUrl = "data:image/jpeg;base64," + FileUtil.INSTANCE.url2Base64(areaImgUrl);
            }
            imageContent.set("image_url", JSONUtil.createObj().set("url", areaImgUrl).set("detail", "high"));
            contents.put(imageContent);
        }
        // 提示词信息
        JSONObject promptContent = new JSONObject();
        promptContent.set("type", "text");
        String sysMsg = promptsRedisService.getExtractPaperTopicPrompt();
        sysMsg = String.format(sysMsg, getOtherTopics(topics));

        promptContent.set("text", sysMsg);
        contents.put(promptContent);
        // 结构化输出
        JSONObject responseFormatContent = new JSONObject();
        responseFormatContent.set("type", "text");
        JSONObject responseJSON = new JSONObject();
        responseJSON.set("name", "仔细阅读学生试卷，确定试卷名称");
        responseJSON.set("subject", "判断试卷的学科类型，如数学、语文、英语等");
        responseJSON.set("detail", "提取试卷上所有的题目内容，不包括学生手写答案");
        responseJSON.set("isSameToOthersPaper", "值为true或false，表示是否有和这份试卷相似的模板");
        responseJSON.set("sameToOthersPaperId", "记录相似试卷的id，如果没有则为空字符串");
        responseFormatContent.set("text", defaultPromptPrefix + JSONUtil.toJsonStr(responseJSON));
        contents.put(responseFormatContent);


        messageDTO.setContent(contents);
        messages.add(messageDTO);
        completion.setMessages(messages);
        return completion;
    }

    public static String getOtherTopics(List<PaperTopicDTO> topics) {
        if (Objects.isNull(topics) || topics.isEmpty()) {
            return "[]";
        }
        JSONArray otherTopics = new JSONArray();
        for (PaperTopicDTO topic : topics) {
            JSONObject topicObj = new JSONObject();
            topicObj.set("name", topic.getName());
            topicObj.set("subject", topic.getSubject());
            if (StrUtil.isNotBlank(topic.getDetail())) {
                topicObj.set("detail", topic.getDetail());
            } else {
                topicObj.set("detail", "无");
            }
            topicObj.set("id", topic.getId());
            otherTopics.add(topicObj);
        }
        return JSONUtil.toJsonStr(otherTopics);
    }

    /**
     * 获取处理后的图片base64字符串
     * @param record 批改记录
     * @param areaObj 区域对象
     * @param areaImg 图片URL
     * @return 处理后的base64字符串
     */
    private String getProcessedImageBase64(DocCorrectRecordDTO record, JSONObject areaObj, String areaImg) {
        if (Base64.isBase64(areaImg)) {
            return areaImg;
        }

        String areaImgUrl = areaImg;
        if (StrUtil.isNotBlank(areaImg) && !areaImg.startsWith("http")) {
            areaImgUrl = String.format("%s%s", serverUrl, areaImg);
        }

        // 获取图像增强配置
        Boolean enableImageEnhancement = record.getModelSetting() != null ?
            record.getModelSetting().getEnableImageEnhancement() : null;

        return "data:image/jpeg;base64," + FileUtil.INSTANCE.url2Base64(areaImgUrl, areaObj, enableImageEnhancement);
    }
}
