package com.chaty.service;


import cn.hutool.core.util.StrUtil;
import com.chaty.dto.VikingMatchResult;
import com.volcengine.service.vikingDB.CollectionClient;
import com.volcengine.service.vikingDB.IndexClient;
import com.volcengine.service.vikingDB.VikingDBService;
import com.volcengine.service.vikingDB.common.DataObject;

import com.volcengine.service.vikingDB.common.SearchWithMultiModalParam;
import com.volcengine.tos.model.object.PutObjectOutput;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 只实现 upsert（插入/更新）向量数据的方法，按官方文档结构构建 DataObject。
 */
@Service
public class VikingVectorService {

    private final VikingDBService vikingDBService;
    private final String collectionName;
    private final CollectionClient collectionClient;
    private final IndexClient indexClient;

    @Resource
    private ImageStorageService imageStorageService;
    public VikingVectorService(
            @Value("${vikingdb.host}") String host,
            @Value("${vikingdb.region}") String region,
            @Value("${vikingdb.ak}") String ak,
            @Value("${vikingdb.sk}") String sk,
            @Value("${vikingdb.collection}") String collectionName,
            @Value("${vikingdb.indexName}") String indexName
    ) throws Exception {
        this.vikingDBService = new VikingDBService(host, region, ak, sk, "http");
        this.collectionName = collectionName;

        this.collectionClient = new CollectionClient(collectionName, host, region, ak, sk, "http");
        //获取指定索引，程序初始化时调用即可，无需重复调用
        this.indexClient = new IndexClient(collectionName, indexName, host, region, ak, sk, "http");
    }

    /**
     * upsert 一条向量记录。字段名需和你的 collection schema 对应，如 f_id, f_vector 等。
     *
     * @param id          主键，比如文档里是 doc_id 或你 schema 定义的 id
     * @param imagePath      向量（浮点数列表）
     * @param otherFields 其他任意字段（可为空），key 是字段名，value 是字段值（支持 string/number/bool/list 等）
     * @throws Exception 失败抛出
     */
    public void upsertVector(String id, String imagePath, Map<String, Object> otherFields) throws Exception {
        // 构造一条 data object
        HashMap<String, Object> fieldMap = new HashMap<>();
        fieldMap.put("id", id);
        fieldMap.put("text", id);
        fieldMap.put("image", "tos://embedding/" + imagePath); // 假设 image 字段存储在 TOS 的 embedding 桶下

        if (otherFields != null) {
            fieldMap.putAll(otherFields);
        }

        DataObject dataObject = new DataObject()
                .setFields(fieldMap)
                .build();

        List<DataObject> dataObjects = Collections.singletonList(dataObject);

        collectionClient.upsertData(dataObjects);
    }

    /**
     * 多模态检索：支持 text + image 组合。
     *
     * @param text 输入文本，可以为 null
     * @param imageTosUri 图片输入，格式为 tos://bucket/object 或 base64://...，可以为 null
     * @param limit 返回数量（可选，默认 10）
     * @return 匹配到的 DataObject 列表
     * @throws Exception 失败抛出
     */
    public List<DataObject> searchWithMultiModal(String text, String imageTosUri, Integer limit) throws Exception {
        if ((StrUtil.isBlank(text)) && StrUtil.isBlank(imageTosUri)) {
            throw new IllegalArgumentException("text 和 image 不能都为空，至少传一个模态输入");
        }

        SearchWithMultiModalParam param = new SearchWithMultiModalParam();
        if (StrUtil.isNotBlank(text)) {
            param.setText(text);
        }
        if (StrUtil.isNotBlank(imageTosUri)) {
            param.setImage(imageTosUri);
        }
        if (limit != null) {
            param.setLimit(limit);
        }


        // 执行检索
        List<DataObject> results = indexClient.searchWithMultiModal(param.build());
        return results != null ? results : Collections.emptyList();
    }

    /**
     * 简化调用：只用 text 检索
     */
    public List<DataObject> searchByText(String text, Integer limit) throws Exception {
        return searchWithMultiModal(text, null, limit);
    }

    /**
     * 简化调用：只用 image 检索
     */
    public List<DataObject> searchByImage(String imageTosUri, Integer limit) throws Exception {
        return searchWithMultiModal(null, imageTosUri, limit);
    }

    /**
     * 逻辑：
     * 1. 本地图片上传到 TOS（object key 用 id），生成 tos://... URI
     * 2. 用这个图片做多模态检索（limit 5），取第一个 score >= 0.8 的结果
     * 3. 如果找到，返回该匹配记录的 id
     * 4. 如果没找到，将当前 image 用 upsertVector 写入 VikingDB，返回 null
     */
    public VikingMatchResult matchOrUpsert(String id, String localImagePath) throws Exception {
        VikingMatchResult res = new VikingMatchResult();
        // 1. 上传图片到 TOS（你需要实现这个方法或替换为已有 ImageStorageService 调用）
        PutObjectOutput putObjectOutput = imageStorageService.uploadImageByLocalPath(localImagePath,id); // 例如 "tos://embedding/{id}.png"
        res.setImageUrl(getImageUrl(id));
        String tosImageUri = getTosUrl(id);
        // 2. 多模态检索：只用 image，limit=5
        List<DataObject> results = searchWithMultiModal(null, tosImageUri, 5);
        res.setMatchResult(results);
        // 3. 找第一个 score >= 0.8
        if (results != null) {
            for (DataObject obj : results) {
                // 官方返回有 getScore 方法；若没有，可从 fields 里取 "score"
                Double score = null;
                try {
                    if (obj.getScore() != null) {
                        score = obj.getScore();
                    } else if (obj.getFields() != null && obj.getFields().get("score") instanceof Number) {
                        score = ((Number) obj.getFields().get("score")).doubleValue();
                    }
                } catch (Exception ignored) {}

                if (score != null && score >= 0.8) {
                    // 命中，返回匹配的 id（主键）
                    res.setMatchId((String) obj.getId());
                    res.setMatchedScore(score);
                    res.setMatchedImageUrl(getImageUrl((String) obj.getId()));
                    return res;
                }
            }
        }

        // 4. 没命中：插入（upsert）当前 image 记录
        upsertVector(id, id + ".png", null);
        return res;
    }

    /**
     * 占位：把本地图片上传到 TOS 并返回 tos:// URI。
     */
    private String getTosUrl(String id) throws Exception {
        return "tos://embedding/" + id + ".png";
    }

    /**
     * 占位：把本地图片上传到 TOS 并返回 tos:// URI。
     */
    private String getImageUrl(String id) throws Exception {
        return "https://embedding.tos-cn-beijing.volces.com/" + id + ".png";
    }
}
