package com.chaty.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chaty.entity.TestSetEntity;
import com.chaty.mapper.TestSetMapper;
import com.chaty.service.TestSetService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Service
public class TestSetServiceImpl extends ServiceImpl<TestSetMapper, TestSetEntity>
        implements TestSetService {

    @Resource
    private TestSetMapper testSetMapper;

    @Override
    public IPage<TestSetEntity> page(Integer pageNumber, Integer pageSize,
                                     String name, String questionTypes) {
        Page<TestSetEntity> page = new Page<>(
                pageNumber == null ? 1 : pageNumber,
                pageSize == null ? 10 : pageSize
        );

        LambdaQueryWrapper<TestSetEntity> qw = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(name)) {
            qw.like(TestSetEntity::getName, name);
        }
        if (StringUtils.hasText(questionTypes)) {
            // 这里用 LIKE 在 question_types 中模糊匹配某个题型关键词
            qw.like(TestSetEntity::getQuestionTypes, questionTypes);
        }
        qw.orderByDesc(TestSetEntity::getCreateTime);

        return this.page(page, qw);
    }

    @Override
    public Long add(TestSetEntity param) {
        this.save(param);
        return param.getId();
    }

    @Override
    public Long updateOne(TestSetEntity param) {
        this.updateById(param);
        return param.getId();
    }

    @Override
    public void deleteById(Long id) {
        if (id != null) {
            this.removeById(id);
        }
    }
}
