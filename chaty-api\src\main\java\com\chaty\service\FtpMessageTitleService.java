package com.chaty.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.entity.FtpMessageTitle;
import com.chaty.dto.FtpMessageTitleDTO;

public interface FtpMessageTitleService extends IService<FtpMessageTitle> {
    
    FtpMessageTitleDTO addTitle(FtpMessageTitleDTO dto);
    
    void deleteTitle(Integer id);
    
    FtpMessageTitleDTO updateTitle(FtpMessageTitleDTO dto);
    
    IPage<FtpMessageTitleDTO> selectPage(FtpMessageTitleDTO param);

    IPage<FtpMessageTitleDTO> selectPageWithAuth(FtpMessageTitleDTO param);
}