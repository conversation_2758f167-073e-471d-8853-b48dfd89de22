package com.chaty.service.impl;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;

import com.chaty.entity.GptAskLogEntity;
import org.springframework.stereotype.Service;

import com.chaty.api.ollama.OllamaApi;
import com.chaty.dto.ChatCompletionDTO;
import com.chaty.service.BasicAiService;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import static com.chaty.util.ModelRequestUtil.mergeModelRequest;

@Slf4j
@Service
public class OllamaAiServiceImpl implements BasicAiService {

    @Resource
    private OllamaApi ollamaApi;
    @Resource
    private GptAskLogServiceImpl gptAskLogService;

    private Map<String, Object> models = MapUtil
            .builder(new HashMap<String, Object>())
            .put("gemma:2b", "gemma:2b")
            .put("llama3", "llama3")
            .build();

    @Override
    public Map<String, Object> chatForCompletion(ChatCompletionDTO param, GptAskLogEntity gptAskLogEntity) {
        Map<String, Object> apiParam = convert2CompletionParam(param);
        // 记录问答开始时间
        gptAskLogEntity.setStartTime(System.currentTimeMillis());
        log.debug("ollama chat request params: {}", apiParam);
        Map<String, Object> resp = ollamaApi.chat(apiParam);

        // 异常处理
        resp.put("$response", JSONUtil.getByPath(JSONUtil.parseObj(resp), "message.content"));

        gptAskLogService.add(BasicAiService.getGptAskLogEntityInAsk(apiParam, resp, gptAskLogEntity));
        return resp;
    }

    @Override
    public Map<String, Object> getFinalCompletion(ChatCompletionDTO param) {
        return convert2CompletionParam(param);
    }

    private Map<String, Object> convert2CompletionParam(ChatCompletionDTO param) {
        Map<String, Object> apiParam = new HashMap<String, Object>();
        apiParam.put("model", param.getModel());
        apiParam.put("stream", param.getStream());
        apiParam.put("messages", param.getMessages());
        Map<String, Object> options = new HashMap<String, Object>();
        Optional.ofNullable(param.getTopp()).ifPresent(topp -> options.put("top_p", topp));
        Optional.ofNullable(param.getTopk()).ifPresent(topk -> options.put("top_k", topk.intValue()));
        Optional.ofNullable(param.getTemperature()).ifPresent(temp -> options.put("temperature", temp));
        options.put("num_ctx", 4096);
        apiParam.put("options", options);

        if (Objects.nonNull(param.getModelRequestObj())) {
            apiParam = mergeModelRequest(apiParam, param);
        }
        return apiParam;
    }

    @Override
    public Boolean isSupport(String model) {
        return models.containsKey(model);
    }

}
