package com.chaty.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

import com.chaty.entity.DocLibrary;

@Mapper
public interface DocLibraryMapper {


    int insert(DocLibrary entity);

    int updateById(DocLibrary entity);

    List<DocLibrary> list(DocLibrary params);

    DocLibrary selectById(String id);

    @Update("update doc_library set deleted = 1 where id = #{id}")
    int deleteById(String id);

}
