package com.chaty.config;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

@Configuration
public class DataSourceConfig {

    // —— MySQL （主数据源）——
    @Bean
    @Primary
    @ConfigurationProperties("spring.datasource")
    public DataSourceProperties mysqlProps() {
        return new DataSourceProperties();
    }

    @Bean(name = "mysqlDataSource")
    @Primary
    public HikariDataSource mysqlDataSource(@Qualifier("mysqlProps") DataSourceProperties props) {
        return props
                .initializeDataSourceBuilder()
                .type(HikariDataSource.class)
                .build();
    }

    // —— PostgreSQL （第二数据源）——
    @Bean
    @ConfigurationProperties("postgres.datasource")
    public DataSourceProperties pgProps() {
        return new DataSourceProperties();
    }

    @Bean(name = "postgresDataSource")
    public HikariDataSource postgresDataSource(@Qualifier("pgProps") DataSourceProperties props) {
        return props
                .initializeDataSourceBuilder()
                .type(HikariDataSource.class)
                .build();
    }

    // —— 事务管理器（可选）——
    @Bean(name = "mysqlTxMgr")
    @Primary
    public PlatformTransactionManager mysqlTxMgr(@Qualifier("mysqlDataSource") DataSource ds) {
        return new DataSourceTransactionManager(ds);
    }

    @Bean(name = "postgresTxMgr")
    public PlatformTransactionManager postgresTxMgr(@Qualifier("postgresDataSource") DataSource ds) {
        return new DataSourceTransactionManager(ds);
    }
}
