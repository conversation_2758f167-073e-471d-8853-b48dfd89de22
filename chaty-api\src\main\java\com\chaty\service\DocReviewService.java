package com.chaty.service;

import java.util.List;
import java.util.Map;

import com.chaty.dto.DocReviewDTO;
import com.chaty.dto.DocReviewDTO.AreaDTO;
import com.chaty.entity.DocReview;

public interface DocReviewService {

    DocReview add(DocReview param);

    void updateById(DocReview param);

    List<DocReview> list(DocReview param);

    void deleteById(String id);

    Map<String, ?> review(DocReviewDTO params);

    Map<String, Object> ocrCropArea(String docurl, AreaDTO area, String service);

    Map<String, Object> batchReviewDoc(DocReviewDTO params);

    Map<String, Object> reviewStatsDoc(DocReviewDTO params);

    List<DocReview> addBatch(List<DocReview> params);

}
