package com.chaty.api.lianke;

import cn.hutool.json.JSONObject;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@ConfigurationProperties(prefix = "lianke")
public class LiankeConfig {

    private String url;
    private String apiKey;
    private List<Device> devices;  // 使用 List 来接收多个设备
    private String username;
    private String password;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public List<Device> getDevices() {
        return devices;
    }

    public void setDevices(List<Device> devices) {
        this.devices = devices;
    }

    public String getUsername() {return username;}

    public void setUsername(String username) {this.username = username;}

    public String getPassword() {return password;}

    public void setPassword(String password) {this.password = password;}

    public static class Device {
        private String deviceId;
        private String deviceKey;
        private String deviceName;
        private Integer online;
        private Integer needResetName;
        private JSONObject params;

        public String getDeviceId() {
            return deviceId;
        }

        public void setDeviceId(String deviceId) {
            this.deviceId = deviceId;
        }

        public String getDeviceKey() {
            return deviceKey;
        }

        public void setDeviceKey(String deviceKey) {
            this.deviceKey = deviceKey;
        }

        public String getDeviceName() {
            return deviceName;
        }

        public void setDeviceName(String deviceName) {
            this.deviceName = deviceName;
        }

        public Integer getOnline() {return online;}

        public void setOnline(Integer online) {this.online = online;}

        public Integer getNeedResetName() {return needResetName;}

        public void setNeedResetName(Integer needResetName) {this.needResetName = needResetName;}

        public JSONObject getParams() {return params;}

        public void setParams(JSONObject params) {this.params = params;}
    }
}

