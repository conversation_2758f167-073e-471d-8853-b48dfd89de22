package com.chaty.service;

import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.dto.QuickFixDTO;
import com.chaty.entity.QuickFix;

public interface QuickFixServce extends IService<QuickFix> {

    IPage<QuickFixDTO> getPage(QuickFixDTO params);

    QuickFix doFix(QuickFixDTO params);

    Map<String, Object> getFile(String id); 
    
}
