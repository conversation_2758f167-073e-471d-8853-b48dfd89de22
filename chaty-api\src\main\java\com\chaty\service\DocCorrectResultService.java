package com.chaty.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chaty.dto.DocCorrectRecordDTO;
import com.chaty.dto.DocCorrectResultDTO;
import com.chaty.entity.DocCorrectResult;

public interface DocCorrectResultService extends IService<DocCorrectResult> {

    void saveByRecord(DocCorrectRecordDTO record);

    void saveByTask(String taskId);

    void saveByRecordId(String recordId);

    IPage<DocCorrectResultDTO> page(DocCorrectResultDTO param);
    
}
